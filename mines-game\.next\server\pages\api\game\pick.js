"use strict";(()=>{var e={};e.id=707,e.ids=[707],e.modules={802:e=>{e.exports=import("clsx")},829:e=>{e.exports=require("jsonwebtoken")},3139:e=>{e.exports=import("bcryptjs")},3873:e=>{e.exports=require("path")},4729:e=>{e.exports=require("bcryptjs")},5511:e=>{e.exports=require("crypto")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5979:e=>{e.exports=import("tailwind-merge")},6153:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>u});var i=t(9103),a=t(2998),o=t(3546),n=e([i,a]);[i,a]=n.then?(await n)():n;let u=(0,i.ru)(async(e,r,t)=>{if((0,o.s4)(),"POST"!==e.method)return r.status(405).json({success:!1,error:"Method not allowed"});try{let{game_id:s,cell_index:i}=e.body;if("number"!=typeof s||s<=0)return r.status(400).json({success:!1,error:"Invalid game ID"});if("number"!=typeof i||i<0||i>=25)return r.status(400).json({success:!1,error:"Invalid cell index"});let o=await (0,a.revealCell)(s,i,t.id);if(o.success)return r.status(200).json({success:!0,hit:o.hit,multiplier:o.multiplier,gameOver:o.gameOver,profit:o.profit,minePositions:o.minePositions,message:o.hit?"Mine hit! Game over.":"Safe cell revealed!"});return r.status(400).json({success:!1,error:o.error||"Failed to reveal cell"})}catch(e){return console.error("Reveal cell API error:",e),r.status(500).json({success:!1,error:"Internal server error"})}});s()}catch(e){s(e)}})},7550:e=>{e.exports=require("better-sqlite3")},8016:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>c,default:()=>l,routeModule:()=>p});var i=t(3480),a=t(8667),o=t(6435),n=t(6153),u=e([n]);n=(u.then?(await u)():u)[0];let l=(0,o.M)(n,"default"),c=(0,o.M)(n,"config"),p=new i.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/game/pick",pathname:"/api/game/pick",bundlePath:"",filename:""},userland:n});s()}catch(e){s(e)}})},9021:e=>{e.exports=require("fs")}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[405,476],()=>t(8016));module.exports=s})();