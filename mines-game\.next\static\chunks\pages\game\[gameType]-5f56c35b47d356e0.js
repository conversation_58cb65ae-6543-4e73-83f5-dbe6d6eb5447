(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[803],{4492:(e,s,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/game/[gameType]",function(){return t(4720)}])},4720:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>eN});var a=t(7876),l=t(4232),r=t(9099),i=t(4918),n=t(3460),c=t(1173),d=t(1887),x=t(6960),o=t(8638),m=t(8592),h=t(3907),u=t(9330),g=t(684);function j(e){let{gameState:s,onCellClick:t,loading:r}=e,[i,n]=(0,l.useState)(!1);(0,l.useEffect)(()=>{if((null==s?void 0:s.status)==="lost"){n(!0);let e=setTimeout(()=>n(!1),600);return()=>clearTimeout(e)}},[null==s?void 0:s.status]);let c=e=>{s&&"active"===s.status&&!r&&(((null==s?void 0:s.revealed_cells)||[]).includes(e)||(u.J.play("click"),t(e)))},d=e=>{if(!s)return(0,a.jsx)("button",{className:"cell-hidden w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-lg flex items-center justify-center text-white font-bold transition-all duration-200 disabled:opacity-50",disabled:!0,children:(0,a.jsx)("span",{className:"text-lg",children:"?"})},e);let t=(null==s?void 0:s.revealed_cells)||[],l=(null==s?void 0:s.mine_positions)||[],i=t.includes(e),n=l.includes(e),d="active"!==s.status;if(i)if(n)return(0,a.jsx)("button",{className:"cell-mine w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-lg flex items-center justify-center text-white animate-pulse",disabled:!0,children:(0,a.jsx)(m.A,{className:"h-6 w-6 sm:h-7 sm:w-7"})},e);else return(0,a.jsx)("button",{className:"cell-gem w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-lg flex items-center justify-center text-white animate-pulse",disabled:!0,children:(0,a.jsx)(h.A,{className:"h-6 w-6 sm:h-7 sm:w-7"})},e);return d&&n&&"lost"===s.status?(0,a.jsx)("button",{className:"bg-red-900/50 border border-red-700 w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-lg flex items-center justify-center text-red-400 opacity-60",disabled:!0,children:(0,a.jsx)(m.A,{className:"h-5 w-5 sm:h-6 sm:w-6"})},e):(0,a.jsx)("button",{onClick:()=>c(e),className:(0,g.cn)("cell-hidden w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-lg flex items-center justify-center text-white font-bold transition-all duration-200",r||"active"!==s.status?"opacity-50 cursor-not-allowed":"hover:scale-105 active:scale-95"),disabled:r||"active"!==s.status,children:(0,a.jsx)("span",{className:"text-lg",children:"?"})},e)};return(0,a.jsxs)("div",{className:(0,g.cn)("flex flex-col items-center space-y-4",i&&"animate-screen-shake"),children:[(0,a.jsx)("div",{className:"grid grid-cols-5 gap-2 p-4 bg-gray-900/50 rounded-xl border border-gray-700",children:Array.from({length:25},(e,s)=>d(s))}),s&&(0,a.jsx)("div",{className:"text-center space-y-2",children:(0,a.jsxs)("div",{className:"flex justify-center space-x-6 text-sm text-gray-300",children:[(0,a.jsxs)("span",{children:["Mines: ",(null==s?void 0:s.mine_count)||"N/A"]}),(0,a.jsxs)("span",{children:["Revealed: ",((null==s?void 0:s.revealed_cells)||[]).length]}),(0,a.jsxs)("span",{children:["Safe Cells: ",25-((null==s?void 0:s.mine_count)||0)-((null==s?void 0:s.revealed_cells)||[]).length]})]})})]})}var b=t(7932),N=t(4871),y=t(822),p=t(1575),v=t(3276);let f=N.bL;N.YJ;let w=N.WT,C=l.forwardRef((e,s)=>{let{className:t,children:l,...r}=e;return(0,a.jsxs)(N.l9,{ref:s,className:(0,g.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...r,children:[l,(0,a.jsx)(N.In,{asChild:!0,children:(0,a.jsx)(y.A,{className:"h-4 w-4 opacity-50"})})]})});C.displayName=N.l9.displayName;let S=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(N.PP,{ref:s,className:(0,g.cn)("flex cursor-default items-center justify-center py-1",t),...l,children:(0,a.jsx)(p.A,{className:"h-4 w-4"})})});S.displayName=N.PP.displayName;let _=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(N.wn,{ref:s,className:(0,g.cn)("flex cursor-default items-center justify-center py-1",t),...l,children:(0,a.jsx)(y.A,{className:"h-4 w-4"})})});_.displayName=N.wn.displayName;let A=l.forwardRef((e,s)=>{let{className:t,children:l,position:r="popper",...i}=e;return(0,a.jsx)(N.ZL,{children:(0,a.jsxs)(N.UC,{ref:s,className:(0,g.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:r,...i,children:[(0,a.jsx)(S,{}),(0,a.jsx)(N.LM,{className:(0,g.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:l}),(0,a.jsx)(_,{})]})})});A.displayName=N.UC.displayName,l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(N.JU,{ref:s,className:(0,g.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...l})}).displayName=N.JU.displayName;let k=l.forwardRef((e,s)=>{let{className:t,children:l,...r}=e;return(0,a.jsxs)(N.q7,{ref:s,className:(0,g.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...r,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(N.VF,{children:(0,a.jsx)(v.A,{className:"h-4 w-4"})})}),(0,a.jsx)(N.p4,{children:l})]})});k.displayName=N.q7.displayName,l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(N.wv,{ref:s,className:(0,g.cn)("-mx-1 my-1 h-px bg-muted",t),...l})}).displayName=N.wv.displayName;var F=t(5961),T=t(3975);function R(e){let{user:s,gameState:t,onStartGame:r,onCashOut:i,loading:n}=e,{forceResetActiveGame:c}=(0,T.t)(),[d,m]=(0,l.useState)(1),[h,u]=(0,l.useState)(3),j=(null==t?void 0:t.status)==="active",N=(null==t?void 0:t.revealed_cells)||[],y=j&&N.length>0,p=Math.min(s.usdt_balance,g.si.MAX_BET),v=n||d<=0||d>s.usdt_balance,S=e=>{m(Math.max(0,Math.min(parseFloat(e)||0,p)))};[.1,.5,1,5,10].filter(e=>e<=p);let _=e=>{m(Math.min(s.usdt_balance*e/100,p))},R=async()=>{confirm("Are you sure you want to reset the current game? Your bet will be refunded.")&&await c()};return(0,a.jsxs)("div",{className:"space-y-4",children:[j&&(0,a.jsx)(o.Zp,{className:"bg-gray-800/80 border-gray-600 backdrop-blur-sm",children:(0,a.jsx)(o.Wu,{className:"pt-4 pb-4",children:(0,a.jsxs)("div",{className:"text-center space-y-3",children:[(0,a.jsxs)("div",{className:"bg-green-500/20 border border-green-500/30 rounded-lg p-3",children:[(0,a.jsx)("div",{className:"text-sm text-green-400 mb-1",children:"Current Multiplier"}),(0,a.jsxs)("div",{className:"text-2xl font-bold text-green-400",children:[t.current_multiplier.toFixed(2),"x"]})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xs text-gray-400 mb-1",children:"Potential Profit"}),(0,a.jsxs)("div",{className:"text-lg font-semibold ".concat(t.profit>=0?"text-green-400":"text-red-400"),children:[t.profit>=0?"+":"",(0,g.vv)(t.profit)," USDT"]})]})]})})}),j&&(0,a.jsx)(o.Zp,{className:"bg-red-900/20 border-red-600/30 backdrop-blur-sm",children:(0,a.jsx)(o.Wu,{className:"pt-4 pb-4",children:(0,a.jsxs)("div",{className:"text-center space-y-3",children:[(0,a.jsx)("div",{className:"text-sm text-red-400 mb-2",children:"Game in Progress"}),(0,a.jsxs)(x.$,{onClick:R,disabled:n,variant:"outline",className:"w-full border-red-600 text-red-400 hover:bg-red-600 hover:text-white",children:[(0,a.jsx)(F.A,{className:"w-4 h-4 mr-2"}),"Reset Game (Refund Bet)"]}),(0,a.jsx)("p",{className:"text-xs text-red-300",children:"This will cancel the current game and refund your bet amount"})]})})}),(0,a.jsx)(o.Zp,{className:"bg-gray-800/80 border-gray-600 backdrop-blur-sm",children:(0,a.jsx)(o.Wu,{className:"p-4",children:(0,a.jsx)("div",{className:"space-y-4",children:j?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.$,{onClick:i,disabled:n||!y,className:"w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 text-lg",children:n?"Cashing Out...":y?"Cash Out":"Reveal a cell first"}),y&&(0,a.jsx)("div",{className:"text-center text-sm text-gray-300",children:"Cash out now to secure your profit!"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"Bet Amount"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(b.p,{type:"number",min:g.si.MIN_BET,max:p,step:"0.01",value:d,onChange:e=>S(e.target.value),className:"bg-gray-700/50 border-gray-600 text-white pr-12",placeholder:"0.00000000"}),(0,a.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-400",children:"₿"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-2",children:[(0,a.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>{m(Math.max(d/2,g.si.MIN_BET))},className:"text-xs border-gray-600 text-gray-300 hover:bg-gray-600",children:"\xbd"}),(0,a.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>{m(Math.min(2*d,p))},className:"text-xs border-gray-600 text-gray-300 hover:bg-gray-600",children:"2\xd7"}),(0,a.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>_(25),className:"text-xs border-gray-600 text-gray-300 hover:bg-gray-600",children:"25%"}),(0,a.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>m(p),className:"text-xs border-gray-600 text-gray-300 hover:bg-gray-600",children:"Max"})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-400",children:[(0,g.vv)(s.usdt_balance)," ₿"]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"Mines"}),(0,a.jsxs)(f,{value:h.toString(),onValueChange:e=>u(parseInt(e)),children:[(0,a.jsx)(C,{className:"bg-gray-700/50 border-gray-600 text-white",children:(0,a.jsx)(w,{})}),(0,a.jsx)(A,{className:"bg-gray-800 border-gray-600",children:Array.from({length:g.si.MAX_MINES},(e,s)=>s+1).map(e=>(0,a.jsx)(k,{value:e.toString(),className:"text-white hover:bg-gray-700",children:e},e))})]})]}),(0,a.jsx)(x.$,{onClick:()=>{d<=0||d>s.usdt_balance||r(d,h)},disabled:v,className:"w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 text-lg",children:n?"Starting...":"Bet"})]})})})}),!j&&(0,a.jsx)(o.Zp,{className:"bg-gray-800/50 border-gray-600",children:(0,a.jsx)(o.Wu,{className:"pt-4 pb-4",children:(0,a.jsxs)("div",{className:"text-xs text-gray-400 space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"House Edge:"}),(0,a.jsxs)("span",{children:[(100*g.si.HOUSE_EDGE).toFixed(1),"%"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Min Bet:"}),(0,a.jsxs)("span",{children:[(0,g.vv)(g.si.MIN_BET)," ₿"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Max Bet:"}),(0,a.jsxs)("span",{children:[(0,g.vv)(g.si.MAX_BET)," ₿"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Max Multiplier:"}),(0,a.jsx)("span",{children:"1000.00x"})]})]})})})]})}var P=t(140),M=t(4660),W=t(1899);function B(e){let{user:s,gameState:t,onStartGame:r,onRollDice:i,loading:n,canRollDice:c,calculateWinChance:d,calculateMultiplier:m}=e,[h,u]=(0,l.useState)("1.00"),[j,N]=(0,l.useState)(50),[y,p]=(0,l.useState)(!0),v=d(j,y),f=m(j,y),w=e=>{(""===e||/^\d*\.?\d*$/.test(e))&&u(e)},C=e=>{let s=parseInt(e);!isNaN(s)&&s>=2&&s<=98&&N(s)},S=async()=>{let e=parseFloat(h);e>0&&e<=s.usdt_balance&&await r(e,j,y)},_=!t&&(()=>{let e=parseFloat(h);return e>0&&e<=s.usdt_balance&&e>=.01})()&&!n,A=t&&c&&!n,k=Math.min(s.usdt_balance,g.si.MAX_BET);return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(o.Zp,{className:"bg-gray-800/50 border-gray-600",children:[(0,a.jsx)(o.aR,{className:"pb-3",children:(0,a.jsx)(o.ZB,{className:"text-white text-sm",children:"Bet Amount"})}),(0,a.jsxs)(o.Wu,{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(b.p,{type:"text",value:h,onChange:e=>w(e.target.value),className:"bg-gray-700 border-gray-600 text-white pr-12",placeholder:"0.00",disabled:!!t}),(0,a.jsx)("span",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-orange-400 text-sm font-medium",children:"₿"})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>u("0.01"),className:"flex-1 bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600",disabled:!!t,children:"Min"}),(0,a.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>u((2*parseFloat(h)).toFixed(2)),className:"flex-1 bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600",disabled:!!t,children:"2x"}),(0,a.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>u(k.toFixed(2)),className:"flex-1 bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600",disabled:!!t,children:"Max"})]})]})]}),(0,a.jsxs)(o.Zp,{className:"bg-gray-800/50 border-gray-600",children:[(0,a.jsxs)(o.aR,{className:"pb-3",children:[(0,a.jsx)(o.ZB,{className:"text-white text-sm",children:"Target Number"}),(0,a.jsx)(o.BT,{className:"text-gray-400 text-xs",children:"Choose a number between 2-98"})]}),(0,a.jsxs)(o.Wu,{className:"space-y-3",children:[(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)(b.p,{type:"number",min:"2",max:"98",value:j,onChange:e=>C(e.target.value),className:"bg-gray-700 border-gray-600 text-white",disabled:!!t})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>N(2),className:"flex-1 bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600",disabled:!!t,children:"Min (2)"}),(0,a.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>N(50),className:"flex-1 bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600",disabled:!!t,children:"50"}),(0,a.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>N(98),className:"flex-1 bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600",disabled:!!t,children:"Max (98)"})]})]})]}),(0,a.jsxs)(o.Zp,{className:"bg-gray-800/50 border-gray-600",children:[(0,a.jsx)(o.aR,{className:"pb-3",children:(0,a.jsx)(o.ZB,{className:"text-white text-sm",children:"Roll Direction"})}),(0,a.jsx)(o.Wu,{children:(0,a.jsx)(P.tU,{value:y?"under":"over",onValueChange:e=>p("under"===e),children:(0,a.jsxs)(P.j7,{className:"grid w-full grid-cols-2 bg-gray-700",children:[(0,a.jsxs)(P.Xi,{value:"under",className:"data-[state=active]:bg-red-600 data-[state=active]:text-white",disabled:!!t,children:[(0,a.jsx)(M.A,{className:"h-4 w-4 mr-1"}),"Roll Under"]}),(0,a.jsxs)(P.Xi,{value:"over",className:"data-[state=active]:bg-green-600 data-[state=active]:text-white",disabled:!!t,children:[(0,a.jsx)(W.A,{className:"h-4 w-4 mr-1"}),"Roll Over"]})]})})})]}),(0,a.jsxs)(o.Zp,{className:"bg-gray-800/50 border-gray-600",children:[(0,a.jsx)(o.aR,{className:"pb-3",children:(0,a.jsx)(o.ZB,{className:"text-white text-sm",children:"Game Stats"})}),(0,a.jsxs)(o.Wu,{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Win Chance:"}),(0,a.jsxs)("span",{className:"text-white text-sm font-medium",children:[v,"%"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Multiplier:"}),(0,a.jsxs)("span",{className:"text-green-400 text-sm font-medium",children:[f.toFixed(4),"x"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Potential Profit:"}),(0,a.jsxs)("span",{className:"text-yellow-400 text-sm font-medium",children:[(0,g.vv)(parseFloat(h||"0")*(f-1))," ₿"]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[_&&(0,a.jsx)(x.$,{onClick:S,disabled:!_||n,className:"w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3",children:n?"Starting...":"Bet ".concat((0,g.vv)(parseFloat(h||"0"))," ₿")}),A&&(0,a.jsx)(x.$,{onClick:i,disabled:!c||n,className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3",children:n?"Rolling...":"\uD83C\uDFB2 Roll Dice"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-gray-400 text-xs",children:"Balance"}),(0,a.jsxs)("div",{className:"text-white font-medium",children:[(0,g.vv)(s.usdt_balance)," ₿"]})]})]})}var D=t(8186);function O(e){var s;let{gameState:t,loading:r}=e,[i,n]=(0,l.useState)(!1),[c,d]=(0,l.useState)(null);return(0,l.useEffect)(()=>{(null==t?void 0:t.result)!==void 0&&t.result!==c&&(n(!0),setTimeout(()=>{d(t.result),n(!1)},1500))},[null==t?void 0:t.result,c]),(0,l.useEffect)(()=>{t||(d(null),n(!1))},[t]),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(o.Zp,{className:"bg-gray-800/50 border-gray-600",children:(0,a.jsx)(o.Wu,{className:"p-8",children:(0,a.jsxs)("div",{className:"text-center space-y-6",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"\n                w-32 h-32 mx-auto rounded-xl border-4 border-gray-600 \n                bg-gradient-to-br from-gray-700 to-gray-800 \n                flex items-center justify-center text-6xl font-bold\n                ".concat(i?"animate-bounce":"","\n                ").concat(t&&null!==c?"won"===t.status?"text-green-400":"text-red-400":"text-gray-400","\n                transition-all duration-300\n              "),children:i?(0,a.jsx)("div",{className:"animate-spin",children:"\uD83C\uDFB2"}):null===c?"?":c.toString()}),i&&(0,a.jsx)("div",{className:"absolute -bottom-4 left-1/2 transform -translate-x-1/2",children:(0,a.jsx)("div",{className:"text-yellow-400 text-sm font-medium animate-pulse",children:"Rolling..."})})]}),t&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(D.A,{className:"h-4 w-4 text-gray-400"}),(0,a.jsx)("span",{className:"text-gray-400",children:"Target:"}),(0,a.jsx)("span",{className:"text-white font-medium",children:t.target_number})]}),(0,a.jsx)("div",{className:"flex items-center space-x-1",children:t.roll_under?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(M.A,{className:"h-4 w-4 text-red-400"}),(0,a.jsx)("span",{className:"text-red-400",children:"Roll Under"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(W.A,{className:"h-4 w-4 text-green-400"}),(0,a.jsx)("span",{className:"text-green-400",children:"Roll Over"})]})})]}),(0,a.jsxs)("div",{className:"flex justify-center space-x-6 text-sm",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-gray-400",children:"Bet Amount"}),(0,a.jsxs)("div",{className:"text-white font-medium",children:[(0,g.vv)(t.bet_amount)," ₿"]})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-gray-400",children:"Multiplier"}),(0,a.jsxs)("div",{className:"text-yellow-400 font-medium",children:[t.current_multiplier.toFixed(4),"x"]})]})]}),null!==c&&(()=>{if(!t||null===c)return null;let e="won"===t.status,s=t.roll_under?"under":"over";return e?(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("div",{className:"text-green-400 font-bold text-lg",children:"\uD83C\uDF89 You Won!"}),(0,a.jsxs)("div",{className:"text-white text-sm",children:["Rolled ",c," (",s," ",t.target_number,")"]}),(0,a.jsxs)("div",{className:"text-green-400 font-medium",children:["+",(0,g.vv)(t.profit)," ₿"]})]}):(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("div",{className:"text-red-400 font-bold text-lg",children:"\uD83D\uDCA5 You Lost!"}),(0,a.jsxs)("div",{className:"text-white text-sm",children:["Rolled ",c," (",s," ",t.target_number,")"]}),(0,a.jsxs)("div",{className:"text-red-400 font-medium",children:[(0,g.vv)(t.profit)," ₿"]})]})})()]}),!t&&(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("div",{className:"text-gray-400 text-lg",children:"Ready to Roll"}),(0,a.jsx)("div",{className:"text-gray-500 text-sm",children:"Place your bet to start playing"})]}),r&&(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("div",{className:"text-yellow-400 text-lg animate-pulse",children:"Processing..."}),(0,a.jsx)("div",{className:"text-gray-500 text-sm",children:"Please wait"})]})]})})}),(0,a.jsx)(o.Zp,{className:"bg-gray-800/50 border-gray-600",children:(0,a.jsx)(o.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("h3",{className:"text-white font-medium text-sm",children:"How to Play"}),(0,a.jsxs)("div",{className:"text-gray-400 text-xs space-y-1",children:[(0,a.jsx)("p",{children:"1. Set your bet amount and target number (2-98)"}),(0,a.jsx)("p",{children:'2. Choose "Roll Under" or "Roll Over"'}),(0,a.jsx)("p",{children:'3. Click "Roll Dice" to see if you win!'}),(0,a.jsx)("p",{children:"4. Higher risk = Higher reward"})]})]})})}),(0,a.jsx)(o.Zp,{className:"bg-gray-800/50 border-gray-600",children:(0,a.jsx)(o.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("h3",{className:"text-green-400 font-medium text-sm",children:"\uD83D\uDD12 Provably Fair"}),(0,a.jsx)("div",{className:"text-gray-400 text-xs",children:"Every roll is cryptographically verifiable using SHA-256 hashing"}),t&&(0,a.jsxs)("div",{className:"text-gray-500 text-xs font-mono",children:["Game ID: #",(null==(s=t.id)?void 0:s.toString().slice(-8))||"N/A"]})]})})})]})}var E=t(5940),$=t(9116);function Z(e){var s;let{user:t,gameState:r,onStartGame:i,onCashOut:n,loading:c,canPlaceBet:d,canCashOut:m,currentMultiplier:h,roundPhase:u,timeUntilNextRound:g,getCrashStats:j}=e,[N,y]=(0,l.useState)(1),[p,v]=(0,l.useState)(void 0),[f,w]=(0,l.useState)(0),C=j();(0,l.useEffect)(()=>{if("betting"===u&&g>0||"waiting"===u&&g>0){w(Math.ceil(g/1e3));let e=setInterval(()=>{w(e=>Math.max(0,e-1))},1e3);return()=>clearInterval(e)}},[u,g]);let S=async()=>{if(d&&!(N<=0))try{await i(N,p)}catch(e){console.error("Failed to start crash game:",e)}},_=async()=>{if(m)try{await n()}catch(e){console.error("Failed to cash out:",e)}},A=(()=>{switch(u){case"betting":return{title:"Place Your Bet",subtitle:"Round starts in ".concat(f,"s"),color:"bg-blue-500"};case"flying":return{title:"Flying!",subtitle:"Cash out before it crashes!",color:"bg-green-500"};case"crashed":return{title:"Crashed!",subtitle:"Next round in ".concat(f,"s"),color:"bg-red-500"};case"waiting":return{title:"Waiting",subtitle:"Next round in ".concat(f,"s"),color:"bg-gray-500"};default:return{title:"Crash",subtitle:"Waiting for round...",color:"bg-gray-500"}}})();return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(o.Zp,{className:"bg-gray-800/80 border-gray-600",children:(0,a.jsx)(o.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(A.color," animate-pulse")}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-white font-semibold",children:A.title}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:A.subtitle})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-white",children:[h.toFixed(2),"x"]}),(0,a.jsx)("div",{className:"text-sm text-gray-400",children:"Multiplier"})]})]})})}),"betting"===u&&!r&&(0,a.jsxs)(o.Zp,{className:"bg-gray-800/80 border-gray-600",children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"text-white flex items-center",children:[(0,a.jsx)(E.A,{className:"h-5 w-5 mr-2"}),"Place Bet"]})}),(0,a.jsxs)(o.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Bet Amount"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)($.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)(b.p,{type:"number",value:N,onChange:e=>y(Number(e.target.value)),min:"0.01",max:(null==t?void 0:t.usdt_balance)||1e3,step:"0.01",className:"pl-10 bg-gray-700 border-gray-600 text-white",placeholder:"Enter bet amount"})]}),(0,a.jsxs)("div",{className:"flex justify-between mt-2",children:[(0,a.jsx)("span",{className:"text-xs text-gray-400",children:"Min: $0.01"}),(0,a.jsxs)("span",{className:"text-xs text-gray-400",children:["Balance: $",(null==t||null==(s=t.usdt_balance)?void 0:s.toFixed(2))||"0.00"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Auto Cash Out (Optional)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(W.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)(b.p,{type:"number",value:p||"",onChange:e=>v(e.target.value?Number(e.target.value):void 0),min:"1.01",step:"0.01",className:"pl-10 bg-gray-700 border-gray-600 text-white",placeholder:"e.g., 2.00"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Automatically cash out at this multiplier"})]}),(0,a.jsx)("div",{className:"grid grid-cols-4 gap-2",children:[.1,1,10,100].map(e=>(0,a.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>y(e),className:"border-gray-600 text-gray-300 hover:bg-gray-700",children:["$",e]},e))}),t&&0===t.usdt_balance&&(0,a.jsx)(x.$,{onClick:async()=>{try{(await fetch("/api/test/add-balance",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({amount:100})})).ok&&window.location.reload()}catch(e){console.error("Failed to add test balance:",e)}},className:"w-full bg-blue-600 hover:bg-blue-700 text-white mb-2",children:"Add $100 Test Balance"}),(0,a.jsx)(x.$,{onClick:S,disabled:c||!d||N<=0||f<=0||((null==t?void 0:t.usdt_balance)||0)<N,className:"w-full bg-green-600 hover:bg-green-700 text-white",children:c?"Placing Bet...":((null==t?void 0:t.usdt_balance)||0)<N?"Insufficient Balance":"Place Bet ($".concat(N,")")})]})]}),r&&"flying"===u&&(0,a.jsx)(o.Zp,{className:"bg-gray-800/80 border-gray-600",children:(0,a.jsx)(o.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-3xl font-bold text-green-400",children:["$",(r.bet_amount*h).toFixed(2)]}),(0,a.jsx)("div",{className:"text-sm text-gray-400",children:"Potential Payout"})]}),(0,a.jsx)(x.$,{onClick:_,disabled:c||!m,className:"w-full bg-orange-600 hover:bg-orange-700 text-white text-lg py-3",children:c?"Cashing Out...":"Cash Out @ ".concat(h.toFixed(2),"x")}),r.auto_cash_out&&(0,a.jsxs)("div",{className:"text-xs text-gray-400",children:["Auto cash out at ",r.auto_cash_out,"x"]})]})})}),C&&(0,a.jsxs)(o.Zp,{className:"bg-gray-800/80 border-gray-600",children:[(0,a.jsx)(o.aR,{children:(0,a.jsx)(o.ZB,{className:"text-white text-sm",children:"Current Round"})}),(0,a.jsxs)(o.Wu,{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Bet Amount:"}),(0,a.jsxs)("span",{className:"text-white",children:["$",C.betAmount]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Current Multiplier:"}),(0,a.jsxs)("span",{className:"text-green-400",children:[C.currentMultiplier.toFixed(2),"x"]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Potential Payout:"}),(0,a.jsxs)("span",{className:"text-white",children:["$",C.potentialPayout.toFixed(2)]})]}),C.autoCashOut&&(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Auto Cash Out:"}),(0,a.jsxs)("span",{className:"text-yellow-400",children:[C.autoCashOut,"x"]})]})]})]})]})}let G=(0,t(9518).F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function z(e){let{className:s,variant:t,...l}=e;return(0,a.jsx)("div",{className:(0,g.cn)(G({variant:t}),s),...l})}function U(e){let{gameState:s,currentMultiplier:t,roundPhase:r,timeElapsed:i,timeUntilNextRound:n}=e,c=(0,l.useRef)(null),d=(0,l.useRef)(),x=(0,l.useRef)([]);return(0,l.useEffect)(()=>{let e=c.current;if(!e)return;let s=e.getContext("2d");if(!s)return;let a=()=>{s.clearRect(0,0,e.width,e.height),e.width=e.offsetWidth*window.devicePixelRatio,e.height=e.offsetHeight*window.devicePixelRatio,s.scale(window.devicePixelRatio,window.devicePixelRatio);let l=e.offsetWidth,n=e.offsetHeight,c=s.createLinearGradient(0,0,0,n);c.addColorStop(0,"rgba(16, 185, 129, 0.1)"),c.addColorStop(1,"rgba(16, 185, 129, 0.05)"),s.fillStyle=c,s.fillRect(0,0,l,n),s.strokeStyle="rgba(75, 85, 99, 0.3)",s.lineWidth=1;for(let e=1;e<=5;e++){let t=n/6*e;s.beginPath(),s.moveTo(0,t),s.lineTo(l,t),s.stroke()}for(let e=1;e<=10;e++){let t=l/11*e;s.beginPath(),s.moveTo(t,0),s.lineTo(t,n),s.stroke()}if("flying"===r||"crashed"===r){if("flying"===r&&(x.current.push(t),x.current.length>100&&x.current.shift()),x.current.length>0){let e=Math.max(i,5e3),a=Math.max(t,2);s.strokeStyle="crashed"===r?"#ef4444":"#10b981",s.lineWidth=4,s.beginPath();let c=n-50,d=50+i/e*(l-100),x=c-(t-1)/(a-1)*(n-100);s.moveTo(50,c),s.lineTo(d,x),s.stroke(),s.fillStyle="crashed"===r?"rgba(239, 68, 68, 0.1)":"rgba(16, 185, 129, 0.1)",s.beginPath(),s.moveTo(50,c),s.lineTo(d,x),s.lineTo(d,c),s.closePath(),s.fill(),"flying"===r&&(s.fillStyle="#fbbf24",s.font="24px sans-serif",s.textAlign="center",s.fillText("\uD83D\uDE80",d,x-10)),"crashed"===r&&(s.fillStyle="#ef4444",s.beginPath(),s.arc(d,x,12,0,2*Math.PI),s.fill(),s.fillStyle="#ffffff",s.font="24px sans-serif",s.textAlign="center",s.fillText("\uD83D\uDCA5",d,x+8),s.fillStyle="#ffffff",s.font="bold 18px sans-serif",s.fillText("CRASHED!",d,x-25),s.font="bold 14px sans-serif",s.fillStyle="#ef4444",s.fillText("".concat(t.toFixed(2),"x"),d,x+35))}}else x.current=[];d.current=requestAnimationFrame(a)};return a(),()=>{d.current&&cancelAnimationFrame(d.current)}},[t,r,s]),(0,a.jsx)(o.Zp,{className:"bg-gray-800/80 border-gray-600 h-full",children:(0,a.jsxs)(o.Wu,{className:"p-6 h-full flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full ".concat((()=>{switch(r){case"betting":return"bg-blue-500";case"flying":return"bg-green-500";case"crashed":return"bg-red-500";default:return"bg-gray-500"}})()," animate-pulse")}),(0,a.jsx)("span",{className:"text-white font-medium",children:(()=>{switch(r){case"betting":{let e=Math.ceil(n/1e3);if(s)return"Betting Phase - ".concat(e,"s");return e>0?"Betting Phase - ".concat(e,"s"):"Waiting for bets..."}case"flying":return"Flying!";case"crashed":return"Crashed at ".concat(t.toFixed(2),"x");case"waiting":return"Next Round - ".concat(Math.ceil(n/1e3),"s");default:return"Waiting..."}})()})]}),(0,a.jsx)(z,{variant:"outline",className:"border-gray-600 text-gray-300",children:"Crash Game"})]}),(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsxs)("div",{className:"text-6xl font-bold mb-2 ".concat("crashed"===r?"text-red-400":"flying"===r?"text-green-400":"text-gray-400"),children:[t.toFixed(2),"x"]}),(0,a.jsxs)("div",{className:"text-gray-400 text-sm",children:["flying"===r&&"".concat((i/1e3).toFixed(1),"s elapsed"),"betting"===r&&(s?"Round starting soon!":"Place your bet to start!"),"crashed"===r&&"Round ended","waiting"===r&&"Preparing next round..."]})]}),(0,a.jsxs)("div",{className:"flex-1 relative min-h-[300px]",children:[(0,a.jsx)("canvas",{ref:c,className:"w-full h-full rounded-lg border border-gray-600",style:{background:"rgba(17, 24, 39, 0.5)"}}),"betting"===r&&(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-4xl mb-2",children:"\uD83D\uDE80"}),(0,a.jsx)("div",{className:"text-white text-xl font-semibold",children:s?"Get Ready!":"Place Your Bet!"}),(0,a.jsx)("div",{className:"text-gray-400",children:s?"Round starting soon...":"Round will start when bets are placed"})]})}),"waiting"===r&&(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-4xl mb-2",children:"⏳"}),(0,a.jsx)("div",{className:"text-white text-xl font-semibold",children:"Round Ended"}),(0,a.jsx)("div",{className:"text-gray-400",children:"Next round starting soon..."})]})})]}),s&&(0,a.jsxs)("div",{className:"mt-4 grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-gray-400",children:"Your Bet"}),(0,a.jsxs)("div",{className:"text-white font-semibold",children:["$",s.bet_amount]})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-gray-400",children:"Potential Win"}),(0,a.jsxs)("div",{className:"text-green-400 font-semibold",children:["$",(s.bet_amount*t).toFixed(2)]})]})]})]})})}var L=t(9843),I=t(3816),J=t(1220),H=t(6729),Y=t(9142);function X(e){let{isOpen:s,onClose:t}=e,[r,i]=(0,l.useState)([]),[n,c]=(0,l.useState)(!1),[d,u]=(0,l.useState)(null);(0,l.useEffect)(()=>{s&&j()},[s]);let j=async()=>{c(!0);try{let e=await fetch(g.Sn.GAME.HISTORY,{credentials:"include"});if(e.ok){let s=await e.json();s.success&&i(s.games)}}catch(e){console.error("Failed to load game history:",e)}finally{c(!1)}},b=e=>{switch(e){case"won":return"text-green-400";case"lost":return"text-red-400";case"cashed_out":return"text-yellow-400";default:return"text-gray-400"}},N=e=>{switch(e){case"won":return(0,a.jsx)(W.A,{className:"h-4 w-4"});case"lost":return(0,a.jsx)(M.A,{className:"h-4 w-4"});case"cashed_out":return(0,a.jsx)(h.A,{className:"h-4 w-4"});default:return(0,a.jsx)(J.A,{className:"h-4 w-4"})}},y=e=>{switch(e){case"mines":return(0,a.jsx)(m.A,{className:"h-4 w-4"});case"dice":return(0,a.jsx)(H.A,{className:"h-4 w-4"});default:return(0,a.jsx)(D.A,{className:"h-4 w-4"})}},p=e=>{switch(e){case"mines":return"Mines";case"dice":return"Dice";default:return e.charAt(0).toUpperCase()+e.slice(1)}},v=e=>"mines"===e.game_type?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"text-sm text-gray-300",children:(0,a.jsxs)("span",{children:["Mines: ",e.mine_count||"N/A"]})}),(0,a.jsx)("div",{className:"text-sm text-gray-300",children:(0,a.jsxs)("span",{children:["Revealed: ",(e.revealed_cells||[]).length]})})]}):"dice"===e.game_type?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"text-sm text-gray-300",children:(0,a.jsxs)("span",{children:["Target: ",e.roll_under?"<":">"," ",e.target_number]})}),(0,a.jsx)("div",{className:"text-sm text-gray-300",children:(0,a.jsxs)("span",{children:["Result: ",void 0!==e.result?e.result:"N/A"]})})]}):(0,a.jsx)("div",{className:"text-sm text-gray-300",children:(0,a.jsxs)("span",{children:["Multiplier: ",e.current_multiplier.toFixed(2),"x"]})}),f=r.reduce((e,s)=>e+s.profit,0),w=r.length,C=w>0?r.filter(e=>e.profit>0).length/w*100:0;return(0,a.jsx)(I.lG,{open:s,onOpenChange:t,children:(0,a.jsxs)(I.Cf,{className:"sm:max-w-4xl bg-gray-800 border-gray-700 text-white max-h-[80vh] overflow-y-auto",children:[(0,a.jsx)(I.c7,{children:(0,a.jsxs)(I.L3,{className:"flex items-center",children:[(0,a.jsx)(J.A,{className:"h-5 w-5 mr-2"}),"Game History"]})}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 mb-6",children:[(0,a.jsx)(o.Zp,{className:"bg-gray-700/50 border-gray-600",children:(0,a.jsx)(o.Wu,{className:"pt-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-white",children:w}),(0,a.jsx)("div",{className:"text-sm text-gray-300",children:"Total Games"})]})})}),(0,a.jsx)(o.Zp,{className:"bg-gray-700/50 border-gray-600",children:(0,a.jsx)(o.Wu,{className:"pt-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold ".concat(f>=0?"text-green-400":"text-red-400"),children:[f>=0?"+":"",(0,g.vv)(f)]}),(0,a.jsx)("div",{className:"text-sm text-gray-300",children:"Total Profit"})]})})}),(0,a.jsx)(o.Zp,{className:"bg-gray-700/50 border-gray-600",children:(0,a.jsx)(o.Wu,{className:"pt-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-blue-400",children:[C.toFixed(1),"%"]}),(0,a.jsx)("div",{className:"text-sm text-gray-300",children:"Win Rate"})]})})})]}),n?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"}),(0,a.jsx)("div",{className:"text-gray-300 mt-2",children:"Loading history..."})]}):0===r.length?(0,a.jsx)("div",{className:"text-center py-8 text-gray-400",children:"No games played yet. Start your first game!"}):(0,a.jsx)("div",{className:"space-y-3",children:r.map(e=>(0,a.jsx)(o.Zp,{className:"bg-gray-700/30 border-gray-600 hover:bg-gray-700/50 transition-colors cursor-pointer",onClick:()=>u(e),children:(0,a.jsx)(o.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 ".concat(b(e.status)),children:[N(e.status),(0,a.jsx)("span",{className:"font-medium capitalize",children:e.status.replace("_"," ")})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-300",children:[y(e.game_type),(0,a.jsx)("span",{children:p(e.game_type)})]}),(0,a.jsx)("div",{className:"text-sm text-gray-300",children:(0,g.Yq)(e.created_at||"")})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,a.jsx)("div",{className:"text-sm text-gray-300",children:(0,a.jsxs)("span",{children:["Bet: ",(0,g.vv)(e.bet_amount)," USDT"]})}),v(e),(0,a.jsxs)("div",{className:"font-semibold ".concat(e.profit>=0?"text-green-400":"text-red-400"),children:[e.profit>=0?"+":"",(0,g.vv)(e.profit)," USDT"]}),(0,a.jsx)(x.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(Y.A,{className:"h-4 w-4"})})]})]})})},e.id))}),d&&(0,a.jsx)(I.lG,{open:!!d,onOpenChange:()=>u(null),children:(0,a.jsxs)(I.Cf,{className:"sm:max-w-md bg-gray-800 border-gray-700 text-white",children:[(0,a.jsx)(I.c7,{children:(0,a.jsxs)(I.L3,{className:"flex items-center space-x-2",children:[y(d.game_type),(0,a.jsxs)("span",{children:[p(d.game_type)," Game Details"]})]})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"text-center",children:(e=>{if("mines"===e.game_type){let s=e.revealed_cells||[],t=e.mine_positions||[],l=Array.from({length:e.grid_size||25},(l,r)=>{let i=s.includes(r),n=t.includes(r);return i?(0,a.jsx)("div",{className:"w-4 h-4 rounded-sm flex items-center justify-center ".concat(n?"bg-red-500":"bg-green-500"),children:n?(0,a.jsx)(m.A,{className:"h-2 w-2 text-white"}):(0,a.jsx)(h.A,{className:"h-2 w-2 text-white"})},r):"active"!==e.status&&n?(0,a.jsx)("div",{className:"w-4 h-4 rounded-sm bg-red-900/50 border border-red-700 flex items-center justify-center",children:(0,a.jsx)(m.A,{className:"h-2 w-2 text-red-400"})},r):(0,a.jsx)("div",{className:"w-4 h-4 rounded-sm bg-gray-600 border border-gray-500"},r)});return(0,a.jsx)("div",{className:"grid grid-cols-5 gap-1 p-2 bg-gray-900/50 rounded",children:l})}return"dice"===e.game_type?(0,a.jsxs)("div",{className:"p-4 bg-gray-900/50 rounded text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,a.jsx)(H.A,{className:"h-8 w-8 text-blue-400"}),(0,a.jsx)("div",{className:"text-lg font-bold",children:void 0!==e.result?e.result:"?"})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-300 mt-2",children:["Target: ",e.roll_under?"<":">"," ",e.target_number]})]}):(0,a.jsxs)("div",{className:"p-4 bg-gray-900/50 rounded text-center",children:[(0,a.jsx)("div",{className:"text-lg font-bold",children:p(e.game_type)}),(0,a.jsxs)("div",{className:"text-sm text-gray-300",children:["Multiplier: ",e.current_multiplier.toFixed(2),"x"]})]})})(d)}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-300",children:"Game Type:"}),(0,a.jsx)("span",{className:"ml-2 font-semibold",children:p(d.game_type)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-300",children:"Status:"}),(0,a.jsx)("span",{className:"ml-2 font-semibold ".concat(b(d.status)),children:d.status.replace("_"," ")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-300",children:"Bet Amount:"}),(0,a.jsxs)("span",{className:"ml-2 font-semibold",children:[(0,g.vv)(d.bet_amount)," USDT"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-300",children:"Multiplier:"}),(0,a.jsxs)("span",{className:"ml-2 font-semibold",children:[d.current_multiplier.toFixed(2),"x"]})]}),"mines"===d.game_type&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-300",children:"Mines:"}),(0,a.jsx)("span",{className:"ml-2 font-semibold",children:d.mine_count||"N/A"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-300",children:"Revealed:"}),(0,a.jsx)("span",{className:"ml-2 font-semibold",children:(d.revealed_cells||[]).length})]})]}),"dice"===d.game_type&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-300",children:"Target:"}),(0,a.jsxs)("span",{className:"ml-2 font-semibold",children:[d.roll_under?"<":">"," ",d.target_number]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-300",children:"Result:"}),(0,a.jsx)("span",{className:"ml-2 font-semibold",children:void 0!==d.result?d.result:"N/A"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-300",children:"Profit:"}),(0,a.jsxs)("span",{className:"ml-2 font-semibold ".concat(d.profit>=0?"text-green-400":"text-red-400"),children:[d.profit>=0?"+":"",(0,g.vv)(d.profit)," USDT"]})]})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-400",children:[(0,a.jsxs)("div",{children:["Game ID: ",d.id]}),(0,a.jsxs)("div",{children:["Client Seed: ",d.client_seed]}),(0,a.jsxs)("div",{children:["Date: ",(0,g.Yq)(d.created_at||"")]})]})]})]})})]})})}var q=t(7581),V=t(6558),K=t(672),Q=t(252),ee=t(1985),es=t(4758),et=t(2333),ea=t(4354),el=t(1874),er=t(5271),ei=t(8834);function en(e){let{data:s,className:t=""}=e,l=(s.length>0?s[s.length-1].cumulativeProfit:0)>=0?"#10b981":"#ef4444";return 0===s.length?(0,a.jsx)("div",{className:"flex items-center justify-center h-64 bg-gray-900/50 rounded-lg border border-gray-700 ".concat(t),children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-gray-400 text-lg mb-2",children:"No games played yet"}),(0,a.jsx)("div",{className:"text-gray-500 text-sm",children:"Start playing to see your profit/loss chart"})]})}):(0,a.jsx)("div",{className:"bg-gray-900/50 rounded-lg border border-gray-700 p-4 ".concat(t),children:(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(Q.u,{width:"100%",height:"100%",children:(0,a.jsxs)(ee.b,{data:s,margin:{top:5,right:30,left:20,bottom:5},children:[(0,a.jsx)(es.d,{strokeDasharray:"3 3",stroke:"#374151"}),(0,a.jsx)(et.W,{dataKey:"gameId",tickFormatter:(e,s)=>"#".concat(e),stroke:"#9ca3af",fontSize:12,tickLine:!1,axisLine:!1}),(0,a.jsx)(ea.h,{tickFormatter:e=>(0,g.vv)(e,!0),stroke:"#9ca3af",fontSize:12,tickLine:!1,axisLine:!1}),(0,a.jsx)(el.m,{content:(0,a.jsx)(e=>{let{active:s,payload:t,label:l}=e;if(s&&t&&t.length){let e=t[0].payload;return(0,a.jsxs)("div",{className:"bg-gray-800 border border-gray-600 rounded-lg p-3 shadow-lg",children:[(0,a.jsxs)("p",{className:"text-gray-300 text-sm mb-1",children:["Game #",e.gameId]}),(0,a.jsxs)("p",{className:"text-white font-semibold",children:["Cumulative: ",(0,g.vv)(e.cumulativeProfit)]}),(0,a.jsxs)("p",{className:"text-sm ".concat(e.profit>=0?"text-green-400":"text-red-400"),children:["This game: ",e.profit>=0?"+":"",(0,g.vv)(e.profit)]}),(0,a.jsxs)("p",{className:"text-gray-400 text-xs",children:["Bet: ",(0,g.vv)(e.betAmount)," • ",e.multiplier,"x"]})]})}return null},{})}),(0,a.jsx)(er.e,{y:0,stroke:"#6b7280",strokeDasharray:"2 2"}),(0,a.jsx)(ei.N,{type:"monotone",dataKey:"cumulativeProfit",stroke:l,strokeWidth:2,dot:{fill:l,strokeWidth:0,r:3},activeDot:{r:5,stroke:l,strokeWidth:2,fill:"#1f2937"},connectNulls:!1})]})})})})}function ec(e){let{isOpen:s,onClose:t,refreshTrigger:r}=e,[i,n]=(0,l.useState)(null),[c,d]=(0,l.useState)(!1),[m,h]=(0,l.useState)(null),[u,j]=(0,l.useState)("all");(0,l.useEffect)(()=>{s&&b()},[s]),(0,l.useEffect)(()=>{s&&r&&r>0&&b()},[r,s]);let b=async()=>{d(!0),h(null);try{let e=g.uW.getSessionStartTime();console.log("\uD83D\uDCCA Loading stats with session start time:",e);let s=new URL("/api/game/stats",window.location.origin);e&&s.searchParams.append("sessionStartTime",e);let t=await fetch(s.toString(),{credentials:"include"});if(t.ok){let e=await t.json();e.success?(n(e.stats),h(null)):(console.error("Stats API returned error:",e.error),h(e.error||"Failed to load stats"),n(null))}else{let e=await t.json().catch(()=>({}));console.error("Stats API HTTP error:",t.status,e),h(e.error||"HTTP ".concat(t.status,": Failed to load stats")),n(null)}}catch(e){console.error("Failed to load stats:",e),h(e instanceof Error?e.message:"Network error occurred"),n(null)}finally{d(!1)}},N=async()=>{try{(await fetch("/api/game/reset-stats",{method:"POST",credentials:"include"})).ok?(g.uW.resetSession(),b()):h("Failed to reset stats")}catch(e){console.error("Reset error:",e),h("Failed to reset stats")}},y=(()=>{if(!(null==i?void 0:i.chartData))return[];let e=i.chartData;switch(u){case"24h":return e.slice(-5);case"7d":return e.slice(-10);case"30d":return e.slice(-30);default:return e}})();return(0,a.jsx)(I.lG,{open:s,onOpenChange:t,children:(0,a.jsxs)(I.Cf,{className:"sm:max-w-4xl bg-gray-800 border-gray-700 text-white max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(I.c7,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(I.L3,{className:"flex items-center",children:[(0,a.jsx)(q.A,{className:"h-5 w-5 mr-2"}),"Session Stats"]}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)(x.$,{variant:"outline",size:"sm",onClick:N,disabled:c,className:"border-gray-600 text-gray-400 hover:bg-gray-700 hover:text-white",title:"Reset session stats",children:(0,a.jsx)(F.A,{className:"h-4 w-4 ".concat(c?"animate-spin":"")})})})]})}),c&&!i?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"}),(0,a.jsx)("div",{className:"text-gray-300 mt-2",children:"Loading stats..."})]}):i?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsx)(o.Zp,{className:"bg-gray-700/30 border-gray-600",children:(0,a.jsxs)(o.Wu,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)($.A,{className:"h-4 w-4 text-green-400"}),(0,a.jsx)("div",{className:"text-xs text-gray-400",children:"Session Profit"})]}),(0,a.jsxs)("div",{className:"text-lg font-bold ".concat(i.totalProfit>=0?"text-green-400":"text-red-400"),children:[i.totalProfit>=0?"+":"",(0,g.vv)(i.totalProfit)]})]})}),(0,a.jsx)(o.Zp,{className:"bg-gray-700/30 border-gray-600",children:(0,a.jsxs)(o.Wu,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(D.A,{className:"h-4 w-4 text-blue-400"}),(0,a.jsx)("div",{className:"text-xs text-gray-400",children:"Session Wagered"})]}),(0,a.jsx)("div",{className:"text-lg font-bold text-white",children:(0,g.vv)(i.totalWagered)})]})}),(0,a.jsx)(o.Zp,{className:"bg-gray-700/30 border-gray-600",children:(0,a.jsxs)(o.Wu,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(V.A,{className:"h-4 w-4 text-yellow-400"}),(0,a.jsx)("div",{className:"text-xs text-gray-400",children:"Session Wins"})]}),(0,a.jsx)("div",{className:"text-lg font-bold text-green-400",children:i.totalWins})]})}),(0,a.jsx)(o.Zp,{className:"bg-gray-700/30 border-gray-600",children:(0,a.jsxs)(o.Wu,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(K.A,{className:"h-4 w-4 text-red-400"}),(0,a.jsx)("div",{className:"text-xs text-gray-400",children:"Session Losses"})]}),(0,a.jsx)("div",{className:"text-lg font-bold text-red-400",children:i.totalLosses})]})})]}),(0,a.jsxs)(o.Zp,{className:"bg-gray-700/30 border-gray-600",children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(o.ZB,{className:"text-white",children:"Session Profit/Loss Chart"}),(0,a.jsxs)(f,{value:u,onValueChange:j,children:[(0,a.jsx)(C,{className:"w-32 bg-gray-700 border-gray-600 text-white",children:(0,a.jsx)(w,{})}),(0,a.jsxs)(A,{className:"bg-gray-700 border-gray-600",children:[(0,a.jsx)(k,{value:"all",children:"All Session"}),(0,a.jsx)(k,{value:"30d",children:"Last 30 Games"}),(0,a.jsx)(k,{value:"7d",children:"Last 10 Games"}),(0,a.jsx)(k,{value:"24h",children:"Last 5 Games"})]})]})]})}),(0,a.jsx)(o.Wu,{children:(0,a.jsx)(en,{data:y})})]})]}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"text-red-400 mb-2",children:"Failed to load stats"}),(0,a.jsx)("div",{className:"text-gray-400 text-sm mb-4",children:m||"Please try again."}),(0,a.jsx)(x.$,{onClick:N,disabled:c,className:"bg-blue-600 hover:bg-blue-700 text-white",children:c?"Loading...":"Reset & Retry"})]})]})})}var ed=t(2885),ex=t(6973),eo=t(6829),em=t(2891),eh=t(395);let eu={boom:{text:"\uD83D\uDCA5 BOOM!",subtext:"You hit a mine!",className:"text-red-400 animate-shake",bgClassName:"bg-red-500/20 border-red-500/50",duration:750},win:{text:"\uD83C\uDF89 YOU WON!",subtext:"Perfect game!",className:"text-green-400 animate-bounce",bgClassName:"bg-green-500/20 border-green-500/50",duration:1e3},lost:{text:"\uD83D\uDC94 YOU LOST!",subtext:"Better luck next time!",className:"text-red-400 animate-shake",bgClassName:"bg-red-500/20 border-red-500/50",duration:1e3},cashout:{text:"\uD83D\uDCB0 CASHED OUT!",subtext:"Smart move!",className:"text-yellow-400 animate-pulse-glow",bgClassName:"bg-yellow-500/20 border-yellow-500/50",duration:1e3},perfect_win:{text:"\uD83C\uDFC6 PERFECT!",subtext:"All safe cells found!",className:"text-purple-400 animate-celebration",bgClassName:"bg-purple-500/20 border-purple-500/50",duration:1e3},big_win:{text:"\uD83D\uDE80 BIG WIN!",subtext:"Amazing multiplier!",className:"text-orange-400 animate-big-win",bgClassName:"bg-orange-500/20 border-orange-500/50",duration:1e3}};function eg(e){let{type:s,visible:t,onComplete:r,multiplier:i,profit:n}=e,[c,d]=(0,l.useState)(!1),[x,o]=(0,l.useState)(!1),m=eu[s];return((0,l.useEffect)(()=>{if(t&&m){d(!0),o(!0);let e=setTimeout(()=>{o(!1),setTimeout(()=>{d(!1),null==r||r()},300)},m.duration);return()=>clearTimeout(e)}},[t,m,r]),m)?c?(0,a.jsxs)("div",{className:(0,g.cn)("fixed inset-0 z-50 flex items-center justify-center pointer-events-none","transition-all duration-300",x?"opacity-100 scale-100":"opacity-0 scale-95"),children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-sm"}),(0,a.jsxs)("div",{className:(0,g.cn)("relative p-8 rounded-2xl border-2 backdrop-blur-md","transform transition-all duration-500",m.bgClassName,x?"scale-100 rotate-0":"scale-110 rotate-1"),children:[(0,a.jsx)("div",{className:(0,g.cn)("text-6xl font-bold text-center mb-4",m.className),children:m.text}),(0,a.jsx)("div",{className:"text-xl text-center text-gray-300 mb-4",children:m.subtext}),("cashout"===s||"win"===s||"lost"===s||"perfect_win"===s||"big_win"===s)&&(0,a.jsxs)("div",{className:"text-center space-y-2",children:[i&&(0,a.jsxs)("div",{className:"text-2xl font-bold text-yellow-400",children:[i.toFixed(2),"x Multiplier"]}),void 0!==n&&(0,a.jsxs)("div",{className:(0,g.cn)("text-xl font-semibold",n>=0?"text-green-400":"text-red-400"),children:[n>=0?"+".concat(n.toFixed(2)):n.toFixed(2)," USDT"]})]}),("perfect_win"===s||"big_win"===s)&&(0,a.jsx)("div",{className:"absolute inset-0 pointer-events-none",children:[...Array(12)].map((e,s)=>(0,a.jsx)("div",{className:(0,g.cn)("absolute w-2 h-2 bg-yellow-400 rounded-full","animate-confetti"),style:{left:"".concat(100*Math.random(),"%"),top:"".concat(100*Math.random(),"%"),animationDelay:"".concat(2*Math.random(),"s"),animationDuration:"".concat(2+2*Math.random(),"s")}},s))})]})]}):null:(console.warn("GameMessage: Unknown message type '".concat(s,"'")),null)}var ej=t(4188),eb=t(2487);function eN(){var e,s;let{user:t,logout:m,loading:b}=(0,i.A)(),N=(0,n.SX)(),y=(0,c.P)(),p=(0,d.x)(),v=(0,r.useRouter)(),{toast:f}=(0,eb.dj)(),{gameType:w}=v.query,{gameState:C,loading:S}="dice"===w?y:"crash"===w?p:N,[_,A]=(0,l.useState)(!1),[k,F]=(0,l.useState)(!1),[T,P]=(0,l.useState)(!1),[M,W]=(0,l.useState)(0),[D,E]=(0,l.useState)(!0),[$,G]=(0,l.useState)(null),[z,I]=(0,l.useState)(null);(0,l.useEffect)(()=>{b||t||v.push("/login")},[t,b,v]),(0,l.useEffect)(()=>{u.J.setEnabled(D)},[D]),(0,l.useEffect)(()=>{g.uW.initializeSession()},[]),(0,l.useEffect)(()=>{w&&"string"==typeof w&&H(w)},[w]);let H=async e=>{try{let s=await fetch("/api/game/list?active=true"),t=await s.json();if(t.success){let s=t.games.find(s=>s.id===e);s?G(s):(f({title:"Game Not Found",description:"The requested game is not available.",variant:"destructive"}),v.push("/lobby"))}}catch(e){console.error("Failed to load game config:",e),v.push("/lobby")}},Y=(e,s,t)=>{I({type:e,visible:!0,multiplier:s,profit:t})},V=()=>{W(e=>e+1)},K=async(e,s)=>{try{await N.startGame(e,s)&&u.J.play("click")}catch(e){console.error("Failed to start game:",e)}},Q=async(e,s,t)=>{try{await y.startGame(e,s,t)&&u.J.play("click")}catch(e){console.error("Failed to start dice game:",e)}},ee=async()=>{try{let e=await y.rollDice();e.won?(u.J.play("win"),setTimeout(()=>{Y("win",e.multiplier,e.profit),V()},500)):(u.J.play("lose"),setTimeout(()=>{Y("lost",e.multiplier,e.profit),V()},500))}catch(e){console.error("Failed to roll dice:",e)}},es=async(e,s)=>{try{await p.startGame(e,s)&&u.J.play("click")}catch(e){console.error("Failed to start crash game:",e)}},et=async()=>{try{let e=await p.cashOut();e.success&&(u.J.play("cashout"),setTimeout(()=>{Y("cashout",e.multiplier,e.profit),V()},200),f({title:"Successfully Cashed Out!",description:"Profit: +".concat(e.profit.toFixed(2)," USDT at ").concat(e.multiplier.toFixed(2),"x"),variant:"success",duration:3e3}))}catch(e){console.error("Failed to cash out:",e)}},ea=async e=>{if(C&&"active"===C.status&&!S&&!((null==C?void 0:C.revealed_cells)||[]).includes(e))try{let t=await N.revealCell(e);if(t.hit)u.J.play("mine"),setTimeout(()=>{u.J.play("lose"),Y("boom"),V()},300);else if(u.J.play("reveal"),t.gameOver)setTimeout(()=>{u.J.play("win"),Y("perfect_win",t.multiplier,t.profit||C.profit),V()},500);else if(t.multiplier>1.5){var s;f({title:"".concat(t.multiplier.toFixed(2),"x Multiplier!"),description:"Potential profit: ".concat(null==(s=C.profit)?void 0:s.toFixed(2)," USDT"),variant:"info",duration:2e3})}}catch(e){console.error("Failed to reveal cell:",e)}},el=async()=>{if(C&&"active"===C.status)try{let e=await N.cashOut();if(e.success){u.J.play("cashout");let s=C.current_multiplier>=5;setTimeout(()=>{s?Y("big_win",C.current_multiplier,e.profit):Y("cashout",C.current_multiplier,e.profit),V()},200),f({title:"Successfully Cashed Out!",description:"Profit: +".concat(e.profit.toFixed(2)," USDT"),variant:"success",duration:3e3})}}catch(e){console.error("Failed to cash out:",e)}},er=async()=>{F(!1)},ei=()=>{u.J.play("click"),v.push("/lobby")};return b?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-white"})}):t?$?"mines"!==w&&"dice"!==w&&"crash"!==w?(0,a.jsx)("div",{className:"min-h-screen bg-gray-900 flex items-center justify-center",children:(0,a.jsx)(o.Zp,{className:"bg-gray-800/80 border-gray-600 max-w-md mx-auto",children:(0,a.jsxs)(o.Wu,{className:"p-8 text-center",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:$.icon}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-white mb-4",children:$.name}),(0,a.jsx)("p",{className:"text-gray-400 mb-6",children:"This game is coming soon! Stay tuned for updates."}),(0,a.jsxs)(x.$,{onClick:ei,className:"bg-purple-600 hover:bg-purple-700",children:[(0,a.jsx)(ed.A,{className:"h-4 w-4 mr-2"}),"Back to Lobby"]})]})})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,a.jsx)("header",{className:"bg-gray-800/50 border-b border-gray-700",children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-3",children:(0,a.jsxs)("nav",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)(x.$,{variant:"ghost",size:"sm",onClick:ei,className:"text-gray-400 hover:text-white hover:bg-gray-700",children:[(0,a.jsx)(ed.A,{className:"h-4 w-4 mr-2"}),"Lobby"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(h.A,{className:"h-6 w-6 text-purple-400"}),(0,a.jsx)("span",{className:"text-xl font-bold text-white",children:"BetOctave"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"bg-gray-700/50 px-3 py-1 rounded-md text-white text-sm",children:[(0,a.jsxs)("span",{className:"text-gray-300",children:[(0,g.vv)(t.usdt_balance)," "]}),(0,a.jsx)("span",{className:"text-orange-400",children:"₿"})]}),(0,a.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>F(!0),className:"bg-blue-600 hover:bg-blue-700 text-white border-blue-600",children:[(0,a.jsx)(ex.A,{className:"h-4 w-4 sm:mr-2"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Wallet"})]}),(0,a.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>E(!D),className:"border-gray-500 text-gray-400 hover:bg-gray-600 hover:text-white",children:D?(0,a.jsx)(eo.A,{className:"h-4 w-4"}):(0,a.jsx)(em.A,{className:"h-4 w-4"})}),(0,a.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>A(!0),className:"border-gray-500 text-gray-400 hover:bg-gray-600 hover:text-white",children:(0,a.jsx)(J.A,{className:"h-4 w-4"})}),(0,a.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>P(!0),className:"border-gray-500 text-gray-400 hover:bg-gray-600 hover:text-white",children:(0,a.jsx)(q.A,{className:"h-4 w-4"})}),(0,a.jsx)(x.$,{variant:"outline",size:"sm",onClick:m,className:"border-gray-500 text-gray-400 hover:bg-gray-600 hover:text-white",children:(0,a.jsx)(eh.A,{className:"h-4 w-4"})})]})]})})}),(0,a.jsx)("main",{className:"container mx-auto px-4 py-6",children:"crash"===w?(0,a.jsxs)("div",{className:"grid lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-1 order-2 lg:order-1",children:(0,a.jsx)(Z,{user:t,gameState:C,onStartGame:es,onCashOut:et,loading:S,canPlaceBet:p.canPlaceBet(),canCashOut:p.canCashOut(),currentMultiplier:p.getCurrentMultiplier(),roundPhase:p.getRoundPhase(),timeUntilNextRound:p.getTimeUntilNextRound(),getCrashStats:p.getCrashStats})}),(0,a.jsx)("div",{className:"lg:col-span-3 order-1 lg:order-2",children:(0,a.jsx)(U,{gameState:C,currentMultiplier:p.getCurrentMultiplier(),roundPhase:p.getRoundPhase(),timeElapsed:p.getTimeElapsed(),timeUntilNextRound:p.getTimeUntilNextRound()})})]}):"dice"===w?(0,a.jsxs)("div",{className:"grid lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-1 order-2 lg:order-1",children:(0,a.jsx)(B,{user:t,gameState:C,onStartGame:Q,onRollDice:ee,loading:S,canRollDice:y.canRollDice(),calculateWinChance:y.calculateWinChance,calculateMultiplier:y.calculateMultiplier})}),(0,a.jsx)("div",{className:"lg:col-span-3 order-1 lg:order-2",children:(0,a.jsxs)("div",{className:"bg-gray-800/80 rounded-xl border border-gray-600 p-6 backdrop-blur-sm shadow-2xl",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-lg",children:$.icon})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-white",children:$.name}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"BetOctave Originals"})]})]}),C&&(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-sm text-gray-400",children:"Game ID"}),(0,a.jsxs)("div",{className:"text-sm text-gray-300 font-mono",children:["#",(null==(e=C.id)?void 0:e.toString().slice(-8))||"N/A"]})]})]}),(0,a.jsx)(O,{gameState:C,loading:S}),(0,a.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,a.jsxs)(x.$,{variant:"ghost",size:"sm",onClick:()=>P(!0),className:"text-gray-400 hover:text-white hover:bg-gray-700/50 p-2 rounded-lg",children:[(0,a.jsx)(q.A,{className:"h-4 w-4 mr-1"}),(0,a.jsx)("span",{className:"text-xs",children:"Stats"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Provably Fair"}),(0,a.jsx)("div",{className:"w-16"})," "]})]})})]}):(0,a.jsxs)("div",{className:"grid lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-1 order-2 lg:order-1",children:(0,a.jsx)(R,{user:t,gameState:C,onStartGame:K,onCashOut:el,loading:S})}),(0,a.jsx)("div",{className:"lg:col-span-3 order-1 lg:order-2",children:(0,a.jsxs)("div",{className:"bg-gray-800/80 rounded-xl border border-gray-600 p-6 backdrop-blur-sm shadow-2xl",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-lg",children:$.icon})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-white",children:$.name}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"BetOctave Originals"})]})]}),C&&(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-sm text-gray-400",children:"Game ID"}),(0,a.jsxs)("div",{className:"text-sm text-gray-300 font-mono",children:["#",(null==(s=C.id)?void 0:s.toString().slice(-8))||"N/A"]})]})]}),(0,a.jsx)("div",{className:"bg-gray-900/50 rounded-lg p-6 border border-gray-700",children:(0,a.jsx)(j,{gameState:C,onCellClick:ea,loading:S})}),(0,a.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,a.jsxs)(x.$,{variant:"ghost",size:"sm",onClick:()=>P(!0),className:"text-gray-400 hover:text-white hover:bg-gray-700/50 p-2 rounded-lg",children:[(0,a.jsx)(q.A,{className:"h-4 w-4 mr-1"}),(0,a.jsx)("span",{className:"text-xs",children:"Stats"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Fairness"}),(0,a.jsx)("div",{className:"w-16"})," "]})]})})]})}),(0,a.jsx)(L.N,{user:t,isOpen:k,onClose:()=>F(!1),onBalanceUpdate:er}),(0,a.jsx)(X,{isOpen:_,onClose:()=>A(!1)}),(0,a.jsx)(ec,{isOpen:T,onClose:()=>P(!1),refreshTrigger:M}),z&&(0,a.jsx)(eg,{type:z.type,visible:z.visible,multiplier:z.multiplier,profit:z.profit,onComplete:()=>{I(null)}}),(0,a.jsx)(ej.l,{})]}):(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-white"})}):null}}},e=>{var s=s=>e(e.s=s);e.O(0,[330,13,953,636,593,792],()=>s(4492)),_N_E=e.O()}]);