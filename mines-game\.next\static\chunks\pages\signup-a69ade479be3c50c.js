(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[252],{3276:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(1713).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5708:(e,s,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/signup",function(){return t(6543)}])},6543:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var r=t(7876),a=t(4232),l=t(9099),n=t(8230),c=t.n(n),i=t(4918),d=t(6960),o=t(7932),x=t(8638),m=t(3907),u=t(7897),h=t(2341),p=t(3276),f=t(672);function g(){let[e,s]=(0,a.useState)(""),[t,n]=(0,a.useState)(""),[g,b]=(0,a.useState)(""),[y,v]=(0,a.useState)(""),[N,j]=(0,a.useState)(!1),[w,A]=(0,a.useState)(!1),[C,k]=(0,a.useState)(""),[P,S]=(0,a.useState)(!1),{signup:_,user:R,loading:B}=(0,i.A)(),E=(0,l.useRouter)();(0,a.useEffect)(()=>{!B&&R&&E.push("/lobby")},[R,B,E]);let O={length:g.length>=8,uppercase:/[A-Z]/.test(g),lowercase:/[a-z]/.test(g),number:/\d/.test(g)},F=Object.values(O).every(Boolean),q=g===y&&y.length>0,T=async s=>{if(s.preventDefault(),k(""),!F)return void k("Password does not meet requirements");if(!q)return void k("Passwords do not match");S(!0);try{await _(e,t,g)?E.push("/lobby"):k("Signup failed. Please try again.")}catch(e){k("Signup failed. Please try again.")}finally{S(!1)}};return B?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-white"})}):R?null:(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsx)("div",{className:"text-center mb-8",children:(0,r.jsxs)(c(),{href:"/",className:"inline-flex items-center space-x-2 text-white hover:text-purple-400 transition-colors",children:[(0,r.jsx)(m.A,{className:"h-8 w-8"}),(0,r.jsx)("span",{className:"text-2xl font-bold",children:"BetOctave"})]})}),(0,r.jsxs)(x.Zp,{className:"bg-gray-800/50 border-gray-700 backdrop-blur-sm",children:[(0,r.jsxs)(x.aR,{className:"text-center",children:[(0,r.jsx)(x.ZB,{className:"text-2xl text-white",children:"Create Account"}),(0,r.jsx)(x.BT,{className:"text-gray-300",children:"Join thousands of players and start winning today"})]}),(0,r.jsxs)(x.Wu,{children:[(0,r.jsxs)("form",{onSubmit:T,className:"space-y-4",children:[C&&(0,r.jsx)("div",{className:"bg-red-500/10 border border-red-500/20 rounded-md p-3",children:(0,r.jsx)("p",{className:"text-red-400 text-sm",children:C})}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"username",className:"text-sm font-medium text-gray-300",children:"Username"}),(0,r.jsx)(o.p,{id:"username",type:"text",placeholder:"Choose a username",value:e,onChange:e=>s(e.target.value),required:!0,className:"bg-gray-700/50 border-gray-600 text-white placeholder:text-gray-400"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"email",className:"text-sm font-medium text-gray-300",children:"Email"}),(0,r.jsx)(o.p,{id:"email",type:"email",placeholder:"Enter your email",value:t,onChange:e=>n(e.target.value),required:!0,className:"bg-gray-700/50 border-gray-600 text-white placeholder:text-gray-400"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"password",className:"text-sm font-medium text-gray-300",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(o.p,{id:"password",type:N?"text":"password",placeholder:"Create a password",value:g,onChange:e=>b(e.target.value),required:!0,className:"bg-gray-700/50 border-gray-600 text-white placeholder:text-gray-400 pr-10"}),(0,r.jsx)("button",{type:"button",onClick:()=>j(!N),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300",children:N?(0,r.jsx)(u.A,{className:"h-4 w-4"}):(0,r.jsx)(h.A,{className:"h-4 w-4"})})]}),g&&(0,r.jsxs)("div",{className:"space-y-1 text-xs",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 ".concat(O.length?"text-green-400":"text-gray-400"),children:[O.length?(0,r.jsx)(p.A,{className:"h-3 w-3"}):(0,r.jsx)(f.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:"At least 8 characters"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 ".concat(O.uppercase?"text-green-400":"text-gray-400"),children:[O.uppercase?(0,r.jsx)(p.A,{className:"h-3 w-3"}):(0,r.jsx)(f.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:"One uppercase letter"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 ".concat(O.lowercase?"text-green-400":"text-gray-400"),children:[O.lowercase?(0,r.jsx)(p.A,{className:"h-3 w-3"}):(0,r.jsx)(f.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:"One lowercase letter"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 ".concat(O.number?"text-green-400":"text-gray-400"),children:[O.number?(0,r.jsx)(p.A,{className:"h-3 w-3"}):(0,r.jsx)(f.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:"One number"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"confirmPassword",className:"text-sm font-medium text-gray-300",children:"Confirm Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(o.p,{id:"confirmPassword",type:w?"text":"password",placeholder:"Confirm your password",value:y,onChange:e=>v(e.target.value),required:!0,className:"bg-gray-700/50 border-gray-600 text-white placeholder:text-gray-400 pr-10"}),(0,r.jsx)("button",{type:"button",onClick:()=>A(!w),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300",children:w?(0,r.jsx)(u.A,{className:"h-4 w-4"}):(0,r.jsx)(h.A,{className:"h-4 w-4"})})]}),y&&(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-xs ".concat(q?"text-green-400":"text-red-400"),children:[q?(0,r.jsx)(p.A,{className:"h-3 w-3"}):(0,r.jsx)(f.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:q?"Passwords match":"Passwords do not match"})]})]}),(0,r.jsx)(d.$,{type:"submit",className:"w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white",disabled:P||!F||!q,children:P?"Creating Account...":"Create Account"})]}),(0,r.jsx)("div",{className:"mt-6 text-center",children:(0,r.jsxs)("p",{className:"text-gray-400",children:["Already have an account?"," ",(0,r.jsx)(c(),{href:"/login",className:"text-purple-400 hover:text-purple-300 font-medium",children:"Sign in"})]})})]})]}),(0,r.jsx)("div",{className:"mt-8 text-center",children:(0,r.jsx)(c(),{href:"/",className:"text-gray-400 hover:text-white transition-colors",children:"← Back to Home"})})]})})}},6960:(e,s,t)=>{"use strict";t.d(s,{$:()=>d});var r=t(7876),a=t(4232),l=t(2987),n=t(9518),c=t(684);let i=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,s)=>{let{className:t,variant:a,size:n,asChild:d=!1,...o}=e,x=d?l.DX:"button";return(0,r.jsx)(x,{className:(0,c.cn)(i({variant:a,size:n,className:t})),ref:s,...o})});d.displayName="Button"},7932:(e,s,t)=>{"use strict";t.d(s,{p:()=>n});var r=t(7876),a=t(4232),l=t(684);let n=a.forwardRef((e,s)=>{let{className:t,type:a,...n}=e;return(0,r.jsx)("input",{type:a,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...n})});n.displayName="Input"},8638:(e,s,t)=>{"use strict";t.d(s,{BT:()=>d,Wu:()=>o,ZB:()=>i,Zp:()=>n,aR:()=>c});var r=t(7876),a=t(4232),l=t(684);let n=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});n.displayName="Card";let c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...a})});c.displayName="CardHeader";let i=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("h3",{ref:s,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});i.displayName="CardTitle";let d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("p",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",t),...a})});d.displayName="CardDescription";let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",t),...a})});o.displayName="CardContent",a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"}},e=>{var s=s=>e(e.s=s);e.O(0,[370,636,593,792],()=>s(5708)),_N_E=e.O()}]);