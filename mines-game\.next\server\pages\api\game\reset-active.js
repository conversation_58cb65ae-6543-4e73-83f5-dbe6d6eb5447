"use strict";(()=>{var e={};e.id=976,e.ids=[976],e.modules={829:e=>{e.exports=require("jsonwebtoken")},3139:e=>{e.exports=import("bcryptjs")},3873:e=>{e.exports=require("path")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6254:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>o});var a=t(9103),n=t(3546),u=e([a]);a=(u.then?(await u)():u)[0];let o=(0,a.ru)(async(e,r,t)=>{if((0,n.s4)(),"POST"!==e.method)return r.status(405).json({success:!1,error:"Method not allowed"});try{let e=n.dW.findActiveByUserId(t.id);if(!e)return r.status(200).json({success:!0,message:"No active game to reset"});n.dW.update(e.id,{status:"cancelled"});let s=n.Gy.findById(t.id);return s&&n.Gy.updateBalance(t.id,"USDT",s.usdt_balance+e.bet_amount),r.status(200).json({success:!0,message:"Active game reset successfully",refundAmount:e.bet_amount})}catch(e){return console.error("Reset active game API error:",e),r.status(500).json({success:!1,error:"Internal server error"})}});s()}catch(e){s(e)}})},7550:e=>{e.exports=require("better-sqlite3")},7854:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>l,default:()=>c,routeModule:()=>d});var a=t(3480),n=t(8667),u=t(6435),o=t(6254),i=e([o]);o=(i.then?(await i)():i)[0];let c=(0,u.M)(o,"default"),l=(0,u.M)(o,"config"),d=new a.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/game/reset-active",pathname:"/api/game/reset-active",bundlePath:"",filename:""},userland:o});s()}catch(e){s(e)}})},9021:e=>{e.exports=require("fs")},9103:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.d(r,{DY:()=>f,Eb:()=>g,Lx:()=>y,OB:()=>h,Tf:()=>w,VX:()=>p,b9:()=>d,ru:()=>m});var a=t(829),n=t.n(a),u=t(3139),o=t(3546),i=e([u]);u=(i.then?(await i)():i)[0];let v="your-super-secret-jwt-key-change-this-in-production-make-it-very-long-and-random";async function c(e){return u.default.hash(e,12)}async function l(e,r){return u.default.compare(e,r)}function d(e){let r=function(e){let r=e.headers.authorization;if(r&&r.startsWith("Bearer "))return r.substring(7);let t=e.cookies.token;return t||null}(e);if(!r)return null;let t=function(e){try{return n().verify(e,v)}catch(e){return null}}(r);return t&&t.userId?o.Gy.findById(t.userId):null}function m(e){return async(r,t)=>{try{let s=d(r);if(!s)return t.status(401).json({success:!1,error:"Authentication required"});await e(r,t,s)}catch(e){console.error("Auth middleware error:",e),t.status(500).json({success:!1,error:"Internal server error"})}}}async function f(e,r,t){try{let s=!e||e.length<3||e.length>20?"Username must be between 3 and 20 characters":/^[a-zA-Z0-9_]+$/.test(e)?r&&/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r)?!t||t.length<8?"Password must be at least 8 characters long":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(t)?null:"Password must contain at least one uppercase letter, one lowercase letter, and one number":"Please provide a valid email address":"Username can only contain letters, numbers, and underscores";if(s)return{success:!1,error:s};if(o.Gy.findByEmail(r))return{success:!1,error:"Email already registered"};if(o.Gy.findByUsername(e))return{success:!1,error:"Username already taken"};let a=await c(t),{password_hash:n,...u}=o.Gy.create(e,r,a);return{success:!0,user:u}}catch(e){return console.error("Registration error:",e),{success:!1,error:"Failed to register user"}}}async function y(e,r){try{let t=e&&r?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)?null:"Please provide a valid email address":"Email and password are required";if(t)return{success:!1,error:t};let s=o.Gy.findByEmail(e);if(!s||!await l(r,s.password_hash))return{success:!1,error:"Invalid email or password"};let a=function(e){let r={userId:e.id,username:e.username,email:e.email};return n().sign(r,v,{expiresIn:"7d"})}(s),{password_hash:u,...i}=s;return{success:!0,user:i,token:a}}catch(e){return console.error("Login error:",e),{success:!1,error:"Failed to login"}}}function p(e,r){e.setHeader("Set-Cookie",[`token=${r}; HttpOnly; Path=/; Max-Age=604800; SameSite=Strict; Secure`])}function h(e){e.setHeader("Set-Cookie",["token=; HttpOnly; Path=/; Max-Age=0; SameSite=Strict"])}let A=new Map;function g(e,r=5,t=9e5){let s=Date.now(),a=A.get(e);return!a||s>a.resetTime?(A.set(e,{count:1,resetTime:s+t}),!0):!(a.count>=r)&&(a.count++,!0)}function w(e){let r=e.headers["x-forwarded-for"];return(r?Array.isArray(r)?r[0]:r.split(",")[0]:e.socket.remoteAddress)||"unknown"}s()}catch(e){s(e)}})}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[405],()=>t(7854));module.exports=s})();