"use strict";(()=>{var e={};e.id=177,e.ids=[177],e.modules={829:e=>{e.exports=require("jsonwebtoken")},3139:e=>{e.exports=import("bcryptjs")},3873:e=>{e.exports=require("path")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6223:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>o});var a=t(9103),n=t(3546),i=e([a]);a=(i.then?(await i)():i)[0];let o=(0,a.ru)(async(e,r,t)=>{if((0,n.s4)(),"GET"!==e.method)return r.status(405).json({success:!1,error:"Method not allowed"});try{let{sessionStartTime:s}=e.query,a=n.dW.findByUserId(t.id,1e3).filter(e=>["won","lost","cashed_out"].includes(e.status));if(s&&"string"==typeof s){let e=new Date(s);a=a.filter(r=>{let t,s=r.created_at;return new Date(s.includes(" ")&&!s.includes("T")?s.replace(" ","T")+"Z":!s.includes("T")||s.includes("Z")||s.includes("+")?s:s+"Z")>=e})}let i=a.filter(e=>e.profit>0).length,o=a.filter(e=>e.profit<=0).length,u=a.length,c=u>0?i/u*100:0,l=a.reduce((e,r)=>e+r.profit,0),d=a.reduce((e,r)=>e+r.bet_amount,0),m=0,f=a.sort((e,r)=>new Date(e.created_at).getTime()-new Date(r.created_at).getTime()).map(e=>(m+=e.profit,{gameId:e.id,timestamp:e.created_at,profit:e.profit,cumulativeProfit:Number(m.toFixed(8)),betAmount:e.bet_amount,multiplier:e.current_multiplier,gameType:e.game_type})),p={totalProfit:Number(l.toFixed(8)),totalWagered:Number(d.toFixed(8)),totalWins:i,totalLosses:o,winRate:Number(c.toFixed(2)),chartData:f};r.status(200).json({success:!0,stats:p})}catch(e){console.error("Stats API error:",e),r.status(500).json({success:!1,error:"Internal server error"})}});s()}catch(e){s(e)}})},7550:e=>{e.exports=require("better-sqlite3")},8820:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>l,default:()=>c,routeModule:()=>d});var a=t(3480),n=t(8667),i=t(6435),o=t(6223),u=e([o]);o=(u.then?(await u)():u)[0];let c=(0,i.M)(o,"default"),l=(0,i.M)(o,"config"),d=new a.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/game/stats",pathname:"/api/game/stats",bundlePath:"",filename:""},userland:o});s()}catch(e){s(e)}})},9021:e=>{e.exports=require("fs")},9103:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.d(r,{DY:()=>f,Eb:()=>g,Lx:()=>p,OB:()=>h,Tf:()=>w,VX:()=>y,b9:()=>d,ru:()=>m});var a=t(829),n=t.n(a),i=t(3139),o=t(3546),u=e([i]);i=(u.then?(await u)():u)[0];let b="your-super-secret-jwt-key-change-this-in-production-make-it-very-long-and-random";async function c(e){return i.default.hash(e,12)}async function l(e,r){return i.default.compare(e,r)}function d(e){let r=function(e){let r=e.headers.authorization;if(r&&r.startsWith("Bearer "))return r.substring(7);let t=e.cookies.token;return t||null}(e);if(!r)return null;let t=function(e){try{return n().verify(e,b)}catch(e){return null}}(r);return t&&t.userId?o.Gy.findById(t.userId):null}function m(e){return async(r,t)=>{try{let s=d(r);if(!s)return t.status(401).json({success:!1,error:"Authentication required"});await e(r,t,s)}catch(e){console.error("Auth middleware error:",e),t.status(500).json({success:!1,error:"Internal server error"})}}}async function f(e,r,t){try{let s=!e||e.length<3||e.length>20?"Username must be between 3 and 20 characters":/^[a-zA-Z0-9_]+$/.test(e)?r&&/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r)?!t||t.length<8?"Password must be at least 8 characters long":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(t)?null:"Password must contain at least one uppercase letter, one lowercase letter, and one number":"Please provide a valid email address":"Username can only contain letters, numbers, and underscores";if(s)return{success:!1,error:s};if(o.Gy.findByEmail(r))return{success:!1,error:"Email already registered"};if(o.Gy.findByUsername(e))return{success:!1,error:"Username already taken"};let a=await c(t),{password_hash:n,...i}=o.Gy.create(e,r,a);return{success:!0,user:i}}catch(e){return console.error("Registration error:",e),{success:!1,error:"Failed to register user"}}}async function p(e,r){try{let t=e&&r?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)?null:"Please provide a valid email address":"Email and password are required";if(t)return{success:!1,error:t};let s=o.Gy.findByEmail(e);if(!s||!await l(r,s.password_hash))return{success:!1,error:"Invalid email or password"};let a=function(e){let r={userId:e.id,username:e.username,email:e.email};return n().sign(r,b,{expiresIn:"7d"})}(s),{password_hash:i,...u}=s;return{success:!0,user:u,token:a}}catch(e){return console.error("Login error:",e),{success:!1,error:"Failed to login"}}}function y(e,r){e.setHeader("Set-Cookie",[`token=${r}; HttpOnly; Path=/; Max-Age=604800; SameSite=Strict; Secure`])}function h(e){e.setHeader("Set-Cookie",["token=; HttpOnly; Path=/; Max-Age=0; SameSite=Strict"])}let x=new Map;function g(e,r=5,t=9e5){let s=Date.now(),a=x.get(e);return!a||s>a.resetTime?(x.set(e,{count:1,resetTime:s+t}),!0):!(a.count>=r)&&(a.count++,!0)}function w(e){let r=e.headers["x-forwarded-for"];return(r?Array.isArray(r)?r[0]:r.split(",")[0]:e.socket.remoteAddress)||"unknown"}s()}catch(e){s(e)}})}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[405],()=>t(8820));module.exports=s})();