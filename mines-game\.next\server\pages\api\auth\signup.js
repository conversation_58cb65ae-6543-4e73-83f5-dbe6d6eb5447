"use strict";(()=>{var e={};e.id=708,e.ids=[708],e.modules={829:e=>{e.exports=require("jsonwebtoken")},3139:e=>{e.exports=import("bcryptjs")},3873:e=>{e.exports=require("path")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5802:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>l,default:()=>c,routeModule:()=>d});var n=t(3480),a=t(8667),u=t(6435),o=t(6626),i=e([o]);o=(i.then?(await i)():i)[0];let c=(0,u.M)(o,"default"),l=(0,u.M)(o,"config"),d=new n.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/auth/signup",pathname:"/api/auth/signup",bundlePath:"",filename:""},userland:o});s()}catch(e){s(e)}})},6626:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>o});var n=t(9103),a=t(3546),u=e([n]);async function o(e,r){if((0,a.s4)(),"POST"!==e.method)return r.status(405).json({success:!1,error:"Method not allowed"});try{let s=(0,n.Tf)(e);if(!(0,n.Eb)(s,5,9e5))return r.status(429).json({success:!1,error:"Too many signup attempts. Please try again later."});let{username:a,email:u,password:o}=e.body;if(!a||!u||!o)return r.status(400).json({success:!1,error:"Username, email, and password are required"});let i=await (0,n.DY)(a,u,o);if(!i.success||!i.user)return r.status(400).json({success:!1,error:i.error||"Failed to create account"});{let e=t(829).sign({userId:i.user.id,username:i.user.username,email:i.user.email},"your-super-secret-jwt-key-change-this-in-production-make-it-very-long-and-random",{expiresIn:"7d"});return(0,n.VX)(r,e),r.status(201).json({success:!0,user:i.user,message:"Account created successfully"})}}catch(e){return console.error("Signup API error:",e),r.status(500).json({success:!1,error:"Internal server error"})}}n=(u.then?(await u)():u)[0],s()}catch(e){s(e)}})},7550:e=>{e.exports=require("better-sqlite3")},9021:e=>{e.exports=require("fs")},9103:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.d(r,{DY:()=>f,Eb:()=>g,Lx:()=>y,OB:()=>h,Tf:()=>w,VX:()=>p,b9:()=>d,ru:()=>m});var n=t(829),a=t.n(n),u=t(3139),o=t(3546),i=e([u]);u=(i.then?(await i)():i)[0];let v="your-super-secret-jwt-key-change-this-in-production-make-it-very-long-and-random";async function c(e){return u.default.hash(e,12)}async function l(e,r){return u.default.compare(e,r)}function d(e){let r=function(e){let r=e.headers.authorization;if(r&&r.startsWith("Bearer "))return r.substring(7);let t=e.cookies.token;return t||null}(e);if(!r)return null;let t=function(e){try{return a().verify(e,v)}catch(e){return null}}(r);return t&&t.userId?o.Gy.findById(t.userId):null}function m(e){return async(r,t)=>{try{let s=d(r);if(!s)return t.status(401).json({success:!1,error:"Authentication required"});await e(r,t,s)}catch(e){console.error("Auth middleware error:",e),t.status(500).json({success:!1,error:"Internal server error"})}}}async function f(e,r,t){try{let s=!e||e.length<3||e.length>20?"Username must be between 3 and 20 characters":/^[a-zA-Z0-9_]+$/.test(e)?r&&/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r)?!t||t.length<8?"Password must be at least 8 characters long":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(t)?null:"Password must contain at least one uppercase letter, one lowercase letter, and one number":"Please provide a valid email address":"Username can only contain letters, numbers, and underscores";if(s)return{success:!1,error:s};if(o.Gy.findByEmail(r))return{success:!1,error:"Email already registered"};if(o.Gy.findByUsername(e))return{success:!1,error:"Username already taken"};let n=await c(t),{password_hash:a,...u}=o.Gy.create(e,r,n);return{success:!0,user:u}}catch(e){return console.error("Registration error:",e),{success:!1,error:"Failed to register user"}}}async function y(e,r){try{let t=e&&r?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)?null:"Please provide a valid email address":"Email and password are required";if(t)return{success:!1,error:t};let s=o.Gy.findByEmail(e);if(!s||!await l(r,s.password_hash))return{success:!1,error:"Invalid email or password"};let n=function(e){let r={userId:e.id,username:e.username,email:e.email};return a().sign(r,v,{expiresIn:"7d"})}(s),{password_hash:u,...i}=s;return{success:!0,user:i,token:n}}catch(e){return console.error("Login error:",e),{success:!1,error:"Failed to login"}}}function p(e,r){e.setHeader("Set-Cookie",[`token=${r}; HttpOnly; Path=/; Max-Age=604800; SameSite=Strict; Secure`])}function h(e){e.setHeader("Set-Cookie",["token=; HttpOnly; Path=/; Max-Age=0; SameSite=Strict"])}let k=new Map;function g(e,r=5,t=9e5){let s=Date.now(),n=k.get(e);return!n||s>n.resetTime?(k.set(e,{count:1,resetTime:s+t}),!0):!(n.count>=r)&&(n.count++,!0)}function w(e){let r=e.headers["x-forwarded-for"];return(r?Array.isArray(r)?r[0]:r.split(",")[0]:e.socket.remoteAddress)||"unknown"}s()}catch(e){s(e)}})}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[405],()=>t(5802));module.exports=s})();