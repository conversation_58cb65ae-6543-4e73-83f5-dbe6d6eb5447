"use strict";(()=>{var e={};e.id=639,e.ids=[639],e.modules={802:e=>{e.exports=import("clsx")},829:e=>{e.exports=require("jsonwebtoken")},2437:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>i});var a=r(9103),o=r(3546),u=e([a]);a=(u.then?(await u)():u)[0];let i=(0,a.ru)(async(e,t,s)=>{if((0,o.s4)(),"POST"!==e.method)return t.status(405).json({success:!1,error:"Method not allowed"});try{let a,{game_id:u,game_type:i}=e.body;if(console.log("\uD83D\uDCB0 Cash out API - Request received:",{game_id:u,game_type:i,user_id:s.id}),!(a=u&&"number"==typeof u?o.dW.findById(u):o.dW.findActiveGameByUserId(s.id)))return t.status(400).json({success:!1,error:"No active game found"});if(console.log("\uD83D\uDCB0 Cash out API - Found active game:",a),a.user_id!==s.id)return t.status(403).json({success:!1,error:"Unauthorized"});if("active"!==a.status)return t.status(400).json({success:!1,error:"Game is not active"});if("crash"===a.game_type){let e=JSON.parse(a.game_data||"{}").crash_point,r=Date.now(),u=new Date(a.created_at).getTime(),i=r-u,c=i/100,d=Math.pow(1.002,c);if(d=Math.round(100*d)/100,e&&d>=e)return t.status(400).json({success:!1,error:`Game already crashed at ${e.toFixed(2)}x`});let n=a.bet_amount*d-a.bet_amount;console.log("\uD83D\uDCB0 Cash out API - Crash game cash out:",{timeElapsedMs:i,timeElapsedScaled:c,currentMultiplier:d.toFixed(2),crashPoint:e?.toFixed(2)||"N/A",profit:n.toFixed(2)});let l=o.dW.update(a.id,{status:"cashed_out",current_multiplier:d,profit:n,cash_out_at:d,cashed_out:!0});return o.Gy.addToBalance(s.id,"USDT",n),console.log("\uD83D\uDCB0 Cash out API - Success:",{profit:n.toFixed(2),multiplier:d.toFixed(2)}),t.status(200).json({success:!0,profit:n,multiplier:d,gameState:l,message:`Successfully cashed out at ${d.toFixed(2)}x!`})}{let{cashOut:e}=r(2998),o=await e(a.id,s.id);if(o.success)return t.status(200).json({success:!0,profit:o.profit,message:"Successfully cashed out!"});return t.status(400).json({success:!1,error:o.error||"Failed to cash out"})}}catch(e){return console.error("\uD83D\uDCB0 Cash out API error:",e),t.status(500).json({success:!1,error:"Internal server error"})}});s()}catch(e){s(e)}})},3139:e=>{e.exports=import("bcryptjs")},3873:e=>{e.exports=require("path")},4729:e=>{e.exports=require("bcryptjs")},5511:e=>{e.exports=require("crypto")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5979:e=>{e.exports=import("tailwind-merge")},7550:e=>{e.exports=require("better-sqlite3")},8208:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>n,default:()=>d,routeModule:()=>l});var a=r(3480),o=r(8667),u=r(6435),i=r(2437),c=e([i]);i=(c.then?(await c)():c)[0];let d=(0,u.M)(i,"default"),n=(0,u.M)(i,"config"),l=new a.PagesAPIRouteModule({definition:{kind:o.A.PAGES_API,page:"/api/game/cashout",pathname:"/api/game/cashout",bundlePath:"",filename:""},userland:i});s()}catch(e){s(e)}})},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[405,476],()=>r(8208));module.exports=s})();