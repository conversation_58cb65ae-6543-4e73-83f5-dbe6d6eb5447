"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/lobby",{

/***/ "(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/plus.js ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Plus)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(pages-dir-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M5 12h14\",\n            key: \"1ays0h\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 5v14\",\n            key: \"s699le\"\n        }\n    ]\n];\nconst Plus = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"plus\", __iconNode);\n //# sourceMappingURL=plus.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvcGx1cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHTyxNQUFNLFVBQXVCO0lBQ2xDO1FBQUMsTUFBUTtRQUFBO1lBQUUsR0FBRyxDQUFZO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtJQUN6QztRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBWTtZQUFBLEtBQUs7UUFBQSxDQUFVO0tBQUE7Q0FDM0M7QUFhTSxXQUFPLGtFQUFpQixTQUFRLENBQVUiLCJzb3VyY2VzIjpbIkU6XFxzcmNcXGljb25zXFxwbHVzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuaW1wb3J0IHsgSWNvbk5vZGUgfSBmcm9tICcuLi90eXBlcyc7XG5cbmV4cG9ydCBjb25zdCBfX2ljb25Ob2RlOiBJY29uTm9kZSA9IFtcbiAgWydwYXRoJywgeyBkOiAnTTUgMTJoMTQnLCBrZXk6ICcxYXlzMGgnIH1dLFxuICBbJ3BhdGgnLCB7IGQ6ICdNMTIgNXYxNCcsIGtleTogJ3M2OTlsZScgfV0sXG5dO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgUGx1c1xuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0pOTlNBeE1tZ3hOQ0lnTHo0S0lDQThjR0YwYUNCa1BTSk5NVElnTlhZeE5DSWdMejRLUEM5emRtYytDZz09KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9wbHVzXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgUGx1cyA9IGNyZWF0ZUx1Y2lkZUljb24oJ3BsdXMnLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IGRlZmF1bHQgUGx1cztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./pages/lobby.tsx":
/*!*************************!*\
  !*** ./pages/lobby.tsx ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Lobby)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(pages-dir-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(pages-dir-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(pages-dir-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(pages-dir-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/tabs */ \"(pages-dir-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_wallet_WalletModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/wallet/WalletModal */ \"(pages-dir-browser)/./components/wallet/WalletModal.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Plus_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,Gem,LogOut,Play,Plus,Search,Star,TrendingUp,Trophy,Users,Volume2,VolumeX,Wallet!=!lucide-react */ \"(pages-dir-browser)/__barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,Gem,LogOut,Play,Plus,Search,Star,TrendingUp,Trophy,Users,Volume2,VolumeX,Wallet!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(pages-dir-browser)/./lib/utils.ts\");\n/* harmony import */ var _lib_sounds__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/sounds */ \"(pages-dir-browser)/./lib/sounds.ts\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/toaster */ \"(pages-dir-browser)/./components/ui/toaster.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(pages-dir-browser)/./components/ui/use-toast.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Lobby() {\n    _s();\n    const { user, logout, loading: authLoading, refreshUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast)();\n    // UI state\n    const [showWallet, setShowWallet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [soundEnabled, setSoundEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    // Data state\n    const [games, setGames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [featuredGames, setFeaturedGames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [lobbyStats, setLobbyStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        playersOnline: 0,\n        totalBetsToday: 0,\n        biggestWinToday: 0,\n        totalGamesPlayed: 0\n    });\n    const [recentWinners, setRecentWinners] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Carousel state\n    const [currentFeaturedIndex, setCurrentFeaturedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Lobby.useEffect\": ()=>{\n            if (!authLoading && !user) {\n                router.push('/login');\n            }\n        }\n    }[\"Lobby.useEffect\"], [\n        user,\n        authLoading,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Lobby.useEffect\": ()=>{\n            _lib_sounds__WEBPACK_IMPORTED_MODULE_10__.soundManager.setEnabled(soundEnabled);\n        }\n    }[\"Lobby.useEffect\"], [\n        soundEnabled\n    ]);\n    // Load games and lobby data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Lobby.useEffect\": ()=>{\n            if (user) {\n                loadLobbyData();\n            }\n        }\n    }[\"Lobby.useEffect\"], [\n        user\n    ]);\n    // Auto-rotate featured games carousel\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Lobby.useEffect\": ()=>{\n            if (featuredGames.length > 1) {\n                const interval = setInterval({\n                    \"Lobby.useEffect.interval\": ()=>{\n                        setCurrentFeaturedIndex({\n                            \"Lobby.useEffect.interval\": (prev)=>prev === featuredGames.length - 1 ? 0 : prev + 1\n                        }[\"Lobby.useEffect.interval\"]);\n                    }\n                }[\"Lobby.useEffect.interval\"], 5000);\n                return ({\n                    \"Lobby.useEffect\": ()=>clearInterval(interval)\n                })[\"Lobby.useEffect\"];\n            }\n        }\n    }[\"Lobby.useEffect\"], [\n        featuredGames.length\n    ]);\n    const loadLobbyData = async ()=>{\n        try {\n            setLoading(true);\n            // Load available games\n            const gamesResponse = await fetch('/api/game/list?active=true');\n            const gamesData = await gamesResponse.json();\n            if (gamesData.success) {\n                setGames(gamesData.games);\n                setFeaturedGames(gamesData.games.filter((game)=>game.isFeatured));\n            }\n            // Load lobby statistics\n            const statsResponse = await fetch('/api/lobby/stats');\n            const statsData = await statsResponse.json();\n            if (statsData.success) {\n                setLobbyStats({\n                    playersOnline: statsData.stats.playersOnline,\n                    totalBetsToday: statsData.stats.totalBetsToday,\n                    biggestWinToday: statsData.stats.biggestWinToday,\n                    totalGamesPlayed: statsData.stats.totalGamesPlayed\n                });\n                setRecentWinners(statsData.stats.recentWinners);\n            } else {\n                // Fallback to mock data if API fails\n                setLobbyStats({\n                    playersOnline: Math.floor(Math.random() * 500) + 100,\n                    totalBetsToday: Math.floor(Math.random() * 10000) + 5000,\n                    biggestWinToday: Math.floor(Math.random() * 50000) + 10000,\n                    totalGamesPlayed: Math.floor(Math.random() * 100000) + 50000\n                });\n                setRecentWinners([]);\n            }\n        } catch (error) {\n            console.error('Failed to load lobby data:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load lobby data\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleGameSelect = (gameId)=>{\n        _lib_sounds__WEBPACK_IMPORTED_MODULE_10__.soundManager.play('click');\n        if (gameId === 'mines') {\n            router.push('/game/mines');\n        } else if (gameId === 'dice') {\n            router.push('/game/dice');\n        } else {\n            toast({\n                title: \"Coming Soon\",\n                description: \"\".concat(gameId, \" will be available soon!\"),\n                variant: \"info\"\n            });\n        }\n    };\n    const handleAddFunds = async ()=>{\n        try {\n            const response = await fetch('/api/test/add-balance', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    amount: 100\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Refresh user data to get updated balance\n                await refreshUser();\n                toast({\n                    title: \"Funds Added!\",\n                    description: \"Successfully added $100 to your balance\",\n                    variant: \"default\"\n                });\n                _lib_sounds__WEBPACK_IMPORTED_MODULE_10__.soundManager.play('win');\n            } else {\n                throw new Error(data.error || 'Failed to add funds');\n            }\n        } catch (error) {\n            console.error('Failed to add funds:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to add funds. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const filteredGames = games.filter((game)=>{\n        const matchesSearch = game.name.toLowerCase().includes(searchQuery.toLowerCase()) || game.description.toLowerCase().includes(searchQuery.toLowerCase());\n        const matchesCategory = selectedCategory === 'all' || game.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    const nextFeatured = ()=>{\n        setCurrentFeaturedIndex((prev)=>prev === featuredGames.length - 1 ? 0 : prev + 1);\n    };\n    const prevFeatured = ()=>{\n        setCurrentFeaturedIndex((prev)=>prev === 0 ? featuredGames.length - 1 : prev - 1);\n    };\n    if (authLoading || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-white\"\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                lineNumber: 225,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n            lineNumber: 224,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null; // Will redirect to login\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gray-800/50 border-b border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Plus_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Gem, {\n                                        className: \"h-6 w-6 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-white\",\n                                        children: \"BetOctave\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: handleAddFunds,\n                                        className: \"bg-green-600 hover:bg-green-700 text-white border-green-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Plus_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Plus, {\n                                                className: \"h-4 w-4 sm:mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Add Fund\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-700/50 px-3 py-1 rounded-md text-white text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-300\",\n                                                children: [\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(user.usdt_balance),\n                                                    \" \"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-orange-400\",\n                                                children: \"₿\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowWallet(true),\n                                        className: \"bg-blue-600 hover:bg-blue-700 text-white border-blue-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Plus_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Wallet, {\n                                                className: \"h-4 w-4 sm:mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Wallet\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSoundEnabled(!soundEnabled),\n                                        className: \"border-gray-500 text-gray-400 hover:bg-gray-600 hover:text-white\",\n                                        children: soundEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Plus_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Volume2, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 33\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Plus_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.VolumeX, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 67\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: logout,\n                                        className: \"border-gray-500 text-gray-400 hover:bg-gray-600 hover:text-white\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Plus_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.LogOut, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"bg-gray-800/80 border-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            className: \"pb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-white flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Plus_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.TrendingUp, {\n                                                        className: \"h-5 w-5 mr-2 text-green-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Live Stats\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Plus_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Users, {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"Players Online\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400 font-semibold\",\n                                                            children: lobbyStats.playersOnline\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Plus_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Trophy, {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"Biggest Win\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 317,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-yellow-400 font-semibold\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(lobbyStats.biggestWinToday)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Plus_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Clock, {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"Bets Today\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 324,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-400 font-semibold\",\n                                                            children: lobbyStats.totalBetsToday.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"bg-gray-800/80 border-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            className: \"pb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-white flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Plus_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Trophy, {\n                                                        className: \"h-5 w-5 mr-2 text-yellow-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Recent Winners\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-3\",\n                                            children: recentWinners.map((winner)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-2 bg-gray-700/50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-sm font-medium\",\n                                                                    children: winner.username\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-400 text-xs\",\n                                                                    children: winner.game\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-green-400 text-sm font-semibold\",\n                                                                    children: [\n                                                                        winner.multiplier,\n                                                                        \"x\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-300 text-xs\",\n                                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(winner.winAmount)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, winner.id, true, {\n                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3 space-y-6\",\n                            children: [\n                                featuredGames.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"bg-gray-800/80 border-gray-600 overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            className: \"pb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-white flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Plus_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Star, {\n                                                        className: \"h-5 w-5 mr-2 text-yellow-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Featured Games\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"p-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"overflow-hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex transition-transform duration-500 ease-in-out\",\n                                                            style: {\n                                                                transform: \"translateX(-\".concat(currentFeaturedIndex * 100, \"%)\")\n                                                            },\n                                                            children: featuredGames.map((game)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full flex-shrink-0\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative bg-gradient-to-r from-purple-600 to-pink-600 p-6 text-white\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                            className: \"text-2xl font-bold mb-2\",\n                                                                                            children: game.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                            lineNumber: 379,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-purple-100 mb-4\",\n                                                                                            children: game.description\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                            lineNumber: 380,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-4 mb-4\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"bg-white/20 px-2 py-1 rounded text-sm\",\n                                                                                                    children: [\n                                                                                                        \"Max \",\n                                                                                                        game.maxMultiplier,\n                                                                                                        \"x\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                                    lineNumber: 382,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"bg-white/20 px-2 py-1 rounded text-sm\",\n                                                                                                    children: [\n                                                                                                        \"Min \",\n                                                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(game.minBet)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                                    lineNumber: 385,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                            lineNumber: 381,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                            onClick: ()=>handleGameSelect(game.id),\n                                                                                            className: \"bg-white text-purple-600 hover:bg-gray-100\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Plus_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Play, {\n                                                                                                    className: \"h-4 w-4 mr-2\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                                    lineNumber: 393,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                \"Play Now\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                            lineNumber: 389,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                    lineNumber: 378,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-6xl opacity-20\",\n                                                                                    children: game.icon\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                    lineNumber: 397,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                            lineNumber: 377,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                        lineNumber: 376,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, game.id, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    featuredGames.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                onClick: prevFeatured,\n                                                                className: \"absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/20 hover:bg-black/40 text-white\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Plus_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.ChevronLeft, {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 415,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                onClick: nextFeatured,\n                                                                className: \"absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/20 hover:bg-black/40 text-white\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Plus_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.ChevronRight, {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 423,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"bg-gray-800/80 border-gray-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Plus_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Search, {\n                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            placeholder: \"Search games...\",\n                                                            value: searchQuery,\n                                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                                            className: \"pl-10 bg-gray-700 border-gray-600 text-white placeholder-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.Tabs, {\n                                                    value: selectedCategory,\n                                                    onValueChange: setSelectedCategory,\n                                                    className: \"w-full sm:w-auto\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsList, {\n                                                        className: \"bg-gray-700 border-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                                                value: \"all\",\n                                                                className: \"data-[state=active]:bg-purple-600\",\n                                                                children: \"All\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                                                value: \"originals\",\n                                                                className: \"data-[state=active]:bg-purple-600\",\n                                                                children: \"Originals\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                                                value: \"slots\",\n                                                                className: \"data-[state=active]:bg-purple-600\",\n                                                                children: \"Slots\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                                                value: \"live\",\n                                                                className: \"data-[state=active]:bg-purple-600\",\n                                                                children: \"Live\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                                                value: \"table\",\n                                                                className: \"data-[state=active]:bg-purple-600\",\n                                                                children: \"Table\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                    children: filteredGames.map((game)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                            className: \"bg-gray-800/80 border-gray-600 hover:border-purple-500 transition-all duration-200 cursor-pointer group\",\n                                            onClick: ()=>handleGameSelect(game.id),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-3xl\",\n                                                                children: game.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    game.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Plus_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Star, {\n                                                                        className: \"h-4 w-4 text-yellow-400 fill-current\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                        lineNumber: 471,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    game.isNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"bg-green-500 text-white text-xs px-2 py-1 rounded\",\n                                                                        children: \"NEW\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                        lineNumber: 474,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-white font-semibold mb-2 group-hover:text-purple-400 transition-colors\",\n                                                        children: game.name\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm mb-3 line-clamp-2\",\n                                                        children: game.description\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between text-xs text-gray-500 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Min: \",\n                                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(game.minBet)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Max: \",\n                                                                    game.maxMultiplier,\n                                                                    \"x\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 489,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1 mb-3\",\n                                                        children: game.features.slice(0, 2).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded\",\n                                                                children: feature\n                                                            }, index, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        className: \"w-full bg-purple-600 hover:bg-purple-700 text-white group-hover:bg-purple-500\",\n                                                        size: \"sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Plus_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Play, {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            game.id === 'mines' || game.id === 'dice' ? 'Play Now' : 'Coming Soon'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, game.id, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, this),\n                                filteredGames.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"bg-gray-800/80 border-gray-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"p-8 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-400 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Plus_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Search, {\n                                                        className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-2\",\n                                                        children: \"No games found\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Try adjusting your search or filter criteria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>{\n                                                    setSearchQuery('');\n                                                    setSelectedCategory('all');\n                                                },\n                                                className: \"border-gray-500 text-gray-400 hover:bg-gray-600 hover:text-white\",\n                                                children: \"Clear Filters\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallet_WalletModal__WEBPACK_IMPORTED_MODULE_8__.WalletModal, {\n                user: user,\n                isOpen: showWallet,\n                onClose: ()=>setShowWallet(false),\n                onBalanceUpdate: ()=>setShowWallet(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                lineNumber: 542,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_11__.Toaster, {}, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                lineNumber: 550,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n        lineNumber: 235,\n        columnNumber: 5\n    }, this);\n}\n_s(Lobby, \"0yhwNJPYqg8QKQXyNYnAuIniKGs=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast\n    ];\n});\n_c = Lobby;\nvar _c;\n$RefreshReg$(_c, \"Lobby\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/lobby.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/__barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,Gem,LogOut,Play,Plus,Search,Star,TrendingUp,Trophy,Users,Volume2,VolumeX,Wallet!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,Gem,LogOut,Play,Plus,Search,Star,TrendingUp,Trophy,Users,Volume2,VolumeX,Wallet!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChevronLeft: () => (/* reexport safe */ _icons_chevron_left_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   ChevronRight: () => (/* reexport safe */ _icons_chevron_right_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Clock: () => (/* reexport safe */ _icons_clock_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Gem: () => (/* reexport safe */ _icons_gem_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   LogOut: () => (/* reexport safe */ _icons_log_out_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   Play: () => (/* reexport safe */ _icons_play_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   Plus: () => (/* reexport safe */ _icons_plus_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   Search: () => (/* reexport safe */ _icons_search_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   Star: () => (/* reexport safe */ _icons_star_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   TrendingUp: () => (/* reexport safe */ _icons_trending_up_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   Trophy: () => (/* reexport safe */ _icons_trophy_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"]),\n/* harmony export */   Users: () => (/* reexport safe */ _icons_users_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"]),\n/* harmony export */   Volume2: () => (/* reexport safe */ _icons_volume_2_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"]),\n/* harmony export */   VolumeX: () => (/* reexport safe */ _icons_volume_x_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"]),\n/* harmony export */   Wallet: () => (/* reexport safe */ _icons_wallet_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_chevron_left_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/chevron-left.js */ \"(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _icons_chevron_right_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/chevron-right.js */ \"(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _icons_clock_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/clock.js */ \"(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _icons_gem_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/gem.js */ \"(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/gem.js\");\n/* harmony import */ var _icons_log_out_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/log-out.js */ \"(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _icons_play_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/play.js */ \"(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _icons_plus_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/plus.js */ \"(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _icons_search_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/search.js */ \"(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _icons_star_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./icons/star.js */ \"(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _icons_trending_up_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./icons/trending-up.js */ \"(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _icons_trophy_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./icons/trophy.js */ \"(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _icons_users_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./icons/users.js */ \"(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _icons_volume_2_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./icons/volume-2.js */ \"(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _icons_volume_x_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./icons/volume-x.js */ \"(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _icons_wallet_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./icons/wallet.js */ \"(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUNoZXZyb25MZWZ0LENoZXZyb25SaWdodCxDbG9jayxHZW0sTG9nT3V0LFBsYXksUGx1cyxTZWFyY2gsU3RhcixUcmVuZGluZ1VwLFRyb3BoeSxVc2VycyxWb2x1bWUyLFZvbHVtZVgsV2FsbGV0IT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ2dFO0FBQ0U7QUFDZjtBQUNKO0FBQ087QUFDTDtBQUNBO0FBQ0k7QUFDSjtBQUNhO0FBQ1Q7QUFDRjtBQUNLO0FBQ0EiLCJzb3VyY2VzIjpbIkU6XFwxMTFcXFBST0pFQ1RcXG1pbmVzLWdhbWVcXG5vZGVfbW9kdWxlc1xcbHVjaWRlLXJlYWN0XFxkaXN0XFxlc21cXGx1Y2lkZS1yZWFjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hldnJvbkxlZnQgfSBmcm9tIFwiLi9pY29ucy9jaGV2cm9uLWxlZnQuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGV2cm9uUmlnaHQgfSBmcm9tIFwiLi9pY29ucy9jaGV2cm9uLXJpZ2h0LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2xvY2sgfSBmcm9tIFwiLi9pY29ucy9jbG9jay5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEdlbSB9IGZyb20gXCIuL2ljb25zL2dlbS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIExvZ091dCB9IGZyb20gXCIuL2ljb25zL2xvZy1vdXQuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBQbGF5IH0gZnJvbSBcIi4vaWNvbnMvcGxheS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFBsdXMgfSBmcm9tIFwiLi9pY29ucy9wbHVzLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2VhcmNoIH0gZnJvbSBcIi4vaWNvbnMvc2VhcmNoLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU3RhciB9IGZyb20gXCIuL2ljb25zL3N0YXIuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUcmVuZGluZ1VwIH0gZnJvbSBcIi4vaWNvbnMvdHJlbmRpbmctdXAuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUcm9waHkgfSBmcm9tIFwiLi9pY29ucy90cm9waHkuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2VycyB9IGZyb20gXCIuL2ljb25zL3VzZXJzLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVm9sdW1lMiB9IGZyb20gXCIuL2ljb25zL3ZvbHVtZS0yLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVm9sdW1lWCB9IGZyb20gXCIuL2ljb25zL3ZvbHVtZS14LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgV2FsbGV0IH0gZnJvbSBcIi4vaWNvbnMvd2FsbGV0LmpzXCIiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/__barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,Gem,LogOut,Play,Plus,Search,Star,TrendingUp,Trophy,Users,Volume2,VolumeX,Wallet!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n"));

/***/ })

});