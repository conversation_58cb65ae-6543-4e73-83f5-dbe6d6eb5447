"use strict";(()=>{var e={};e.id=532,e.ids=[532],e.modules={829:e=>{e.exports=require("jsonwebtoken")},3139:e=>{e.exports=import("bcryptjs")},3873:e=>{e.exports=require("path")},5586:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>u});var a=t(9103),n=t(3546),o=e([a]);a=(o.then?(await o)():o)[0];let u=(0,a.ru)(async(e,r,t)=>{if((0,n.s4)(),"POST"!==e.method)return r.status(405).json({success:!1,error:"Method not allowed"});try{let{amount:s=100}=e.body;n.Gy.addToBalance(t.id,"USDT",s);let a=n.Gy.findById(t.id);return r.status(200).json({success:!0,message:`Added ${s} USDT to your balance for testing`,balance:{usdt:a?.usdt_balance||0,ltc:a?.ltc_balance||0}})}catch(e){return console.error("Add balance API error:",e),r.status(500).json({success:!1,error:"Internal server error"})}});s()}catch(e){s(e)}})},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5776:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>l,default:()=>c,routeModule:()=>d});var a=t(3480),n=t(8667),o=t(6435),u=t(5586),i=e([u]);u=(i.then?(await i)():i)[0];let c=(0,o.M)(u,"default"),l=(0,o.M)(u,"config"),d=new a.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/test/add-balance",pathname:"/api/test/add-balance",bundlePath:"",filename:""},userland:u});s()}catch(e){s(e)}})},7550:e=>{e.exports=require("better-sqlite3")},9021:e=>{e.exports=require("fs")},9103:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.d(r,{DY:()=>f,Eb:()=>g,Lx:()=>m,OB:()=>p,Tf:()=>w,VX:()=>h,b9:()=>d,ru:()=>y});var a=t(829),n=t.n(a),o=t(3139),u=t(3546),i=e([o]);o=(i.then?(await i)():i)[0];let b="your-super-secret-jwt-key-change-this-in-production-make-it-very-long-and-random";async function c(e){return o.default.hash(e,12)}async function l(e,r){return o.default.compare(e,r)}function d(e){let r=function(e){let r=e.headers.authorization;if(r&&r.startsWith("Bearer "))return r.substring(7);let t=e.cookies.token;return t||null}(e);if(!r)return null;let t=function(e){try{return n().verify(e,b)}catch(e){return null}}(r);return t&&t.userId?u.Gy.findById(t.userId):null}function y(e){return async(r,t)=>{try{let s=d(r);if(!s)return t.status(401).json({success:!1,error:"Authentication required"});await e(r,t,s)}catch(e){console.error("Auth middleware error:",e),t.status(500).json({success:!1,error:"Internal server error"})}}}async function f(e,r,t){try{let s=!e||e.length<3||e.length>20?"Username must be between 3 and 20 characters":/^[a-zA-Z0-9_]+$/.test(e)?r&&/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r)?!t||t.length<8?"Password must be at least 8 characters long":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(t)?null:"Password must contain at least one uppercase letter, one lowercase letter, and one number":"Please provide a valid email address":"Username can only contain letters, numbers, and underscores";if(s)return{success:!1,error:s};if(u.Gy.findByEmail(r))return{success:!1,error:"Email already registered"};if(u.Gy.findByUsername(e))return{success:!1,error:"Username already taken"};let a=await c(t),{password_hash:n,...o}=u.Gy.create(e,r,a);return{success:!0,user:o}}catch(e){return console.error("Registration error:",e),{success:!1,error:"Failed to register user"}}}async function m(e,r){try{let t=e&&r?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)?null:"Please provide a valid email address":"Email and password are required";if(t)return{success:!1,error:t};let s=u.Gy.findByEmail(e);if(!s||!await l(r,s.password_hash))return{success:!1,error:"Invalid email or password"};let a=function(e){let r={userId:e.id,username:e.username,email:e.email};return n().sign(r,b,{expiresIn:"7d"})}(s),{password_hash:o,...i}=s;return{success:!0,user:i,token:a}}catch(e){return console.error("Login error:",e),{success:!1,error:"Failed to login"}}}function h(e,r){e.setHeader("Set-Cookie",[`token=${r}; HttpOnly; Path=/; Max-Age=604800; SameSite=Strict; Secure`])}function p(e){e.setHeader("Set-Cookie",["token=; HttpOnly; Path=/; Max-Age=0; SameSite=Strict"])}let v=new Map;function g(e,r=5,t=9e5){let s=Date.now(),a=v.get(e);return!a||s>a.resetTime?(v.set(e,{count:1,resetTime:s+t}),!0):!(a.count>=r)&&(a.count++,!0)}function w(e){let r=e.headers["x-forwarded-for"];return(r?Array.isArray(r)?r[0]:r.split(",")[0]:e.socket.remoteAddress)||"unknown"}s()}catch(e){s(e)}})}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[405],()=>t(5776));module.exports=s})();