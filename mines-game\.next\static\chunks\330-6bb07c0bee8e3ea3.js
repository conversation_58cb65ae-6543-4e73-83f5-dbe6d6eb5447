(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[330],{294:(e,t,n)=>{"use strict";n.d(t,{B:()=>u});var r,o=n(4232),a=n(1285),i=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),c=0;function u(e){let[t,n]=o.useState(i());return(0,a.N)(()=>{e||n(e=>e??String(c++))},[e]),e||(t?`radix-${t}`:"")}},395:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(1713).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},801:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(1713).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},870:(e,t,n)=>{"use strict";n.d(t,{n:()=>d});var r=n(4232),o=n(714),a=n(6326),i=n(2146),c=n(7876),u="focusScope.autoFocusOnMount",l="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:m,onUnmountAutoFocus:g,...y}=e,[b,w]=r.useState(null),E=(0,i.c)(m),x=(0,i.c)(g),A=r.useRef(null),k=(0,o.s)(t,e=>w(e)),C=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(C.paused||!b)return;let t=e.target;b.contains(t)?A.current=t:v(A.current,{select:!0})},t=function(e){if(C.paused||!b)return;let t=e.relatedTarget;null!==t&&(b.contains(t)||v(A.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&v(b)});return b&&n.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,b,C.paused]),r.useEffect(()=>{if(b){h.add(C);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(u,s);b.addEventListener(u,E),b.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(v(r,{select:t}),document.activeElement!==n)return}(f(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&v(b))}return()=>{b.removeEventListener(u,E),setTimeout(()=>{let t=new CustomEvent(l,s);b.addEventListener(l,x),b.dispatchEvent(t),t.defaultPrevented||v(e??document.body,{select:!0}),b.removeEventListener(l,x),h.remove(C)},0)}}},[b,E,x,C]);let R=r.useCallback(e=>{if(!n&&!d||C.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,a]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&a?e.shiftKey||r!==a?e.shiftKey&&r===o&&(e.preventDefault(),n&&v(a,{select:!0})):(e.preventDefault(),n&&v(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,C.paused]);return(0,c.jsx)(a.sG.div,{tabIndex:-1,...y,ref:k,onKeyDown:R})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function v(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var h=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=m(e,t)).unshift(t)},remove(t){e=m(e,t),e[0]?.resume()}}}();function m(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},1347:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(1713).A)("circle-arrow-down",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 8v8",key:"napkw2"}],["path",{d:"m8 12 4 4 4-4",key:"k98ssh"}]])},1592:(e,t,n)=>{"use strict";n.d(t,{UC:()=>z,B8:()=>U,bL:()=>$,l9:()=>Z});var r=n(4232),o=n(3716),a=n(1844),i=n(8775),c=n(714),u=n(294),l=n(6326),s=n(2146),d=n(8162),f=n(4966),p=n(7876),v="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},m="RovingFocusGroup",[g,y,b]=(0,i.N)(m),[w,E]=(0,a.A)(m,[b]),[x,A]=w(m),k=r.forwardRef((e,t)=>(0,p.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(C,{...e,ref:t})})}));k.displayName=m;var C=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:i=!1,dir:u,currentTabStopId:g,defaultCurrentTabStopId:b,onCurrentTabStopIdChange:w,onEntryFocus:E,preventScrollOnEntryFocus:A=!1,...k}=e,C=r.useRef(null),R=(0,c.s)(t,C),M=(0,f.jH)(u),[S,I]=(0,d.i)({prop:g,defaultProp:b??null,onChange:w,caller:m}),[D,N]=r.useState(!1),T=(0,s.c)(E),F=y(n),L=r.useRef(!1),[O,P]=r.useState(0);return r.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(v,T),()=>e.removeEventListener(v,T)},[T]),(0,p.jsx)(x,{scope:n,orientation:a,dir:M,loop:i,currentTabStopId:S,onItemFocus:r.useCallback(e=>I(e),[I]),onItemShiftTab:r.useCallback(()=>N(!0),[]),onFocusableItemAdd:r.useCallback(()=>P(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>P(e=>e-1),[]),children:(0,p.jsx)(l.sG.div,{tabIndex:D||0===O?-1:0,"data-orientation":a,...k,ref:R,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{L.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!L.current;if(e.target===e.currentTarget&&t&&!D){let t=new CustomEvent(v,h);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=F().filter(e=>e.focusable);j([e.find(e=>e.active),e.find(e=>e.id===S),...e].filter(Boolean).map(e=>e.ref.current),A)}}L.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>N(!1))})})}),R="RovingFocusGroupItem",M=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:a=!0,active:i=!1,tabStopId:c,children:s,...d}=e,f=(0,u.B)(),v=c||f,h=A(R,n),m=h.currentTabStopId===v,b=y(n),{onFocusableItemAdd:w,onFocusableItemRemove:E,currentTabStopId:x}=h;return r.useEffect(()=>{if(a)return w(),()=>E()},[a,w,E]),(0,p.jsx)(g.ItemSlot,{scope:n,id:v,focusable:a,active:i,children:(0,p.jsx)(l.sG.span,{tabIndex:m?0:-1,"data-orientation":h.orientation,...d,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?h.onItemFocus(v):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>h.onItemFocus(v)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void h.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return S[o]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=h.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>j(n))}}),children:"function"==typeof s?s({isCurrentTabStop:m,hasTabStop:null!=x}):s})})});M.displayName=R;var S={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function j(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var I=n(6822),D="Tabs",[N,T]=(0,a.A)(D,[E]),F=E(),[L,O]=N(D),P=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:o,defaultValue:a,orientation:i="horizontal",dir:c,activationMode:s="automatic",...v}=e,h=(0,f.jH)(c),[m,g]=(0,d.i)({prop:r,onChange:o,defaultProp:a??"",caller:D});return(0,p.jsx)(L,{scope:n,baseId:(0,u.B)(),value:m,onValueChange:g,orientation:i,dir:h,activationMode:s,children:(0,p.jsx)(l.sG.div,{dir:h,"data-orientation":i,...v,ref:t})})});P.displayName=D;var _="TabsList",B=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...o}=e,a=O(_,n),i=F(n);return(0,p.jsx)(k,{asChild:!0,...i,orientation:a.orientation,dir:a.dir,loop:r,children:(0,p.jsx)(l.sG.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:t})})});B.displayName=_;var K="TabsTrigger",W=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:a=!1,...i}=e,c=O(K,n),u=F(n),s=q(c.baseId,r),d=V(c.baseId,r),f=r===c.value;return(0,p.jsx)(M,{asChild:!0,...u,focusable:!a,active:f,children:(0,p.jsx)(l.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":d,"data-state":f?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:s,...i,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(r)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(r)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==c.activationMode;f||a||!e||c.onValueChange(r)})})})});W.displayName=K;var H="TabsContent",G=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:o,forceMount:a,children:i,...c}=e,u=O(H,n),s=q(u.baseId,o),d=V(u.baseId,o),f=o===u.value,v=r.useRef(f);return r.useEffect(()=>{let e=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(I.C,{present:a||f,children:({present:n})=>(0,p.jsx)(l.sG.div,{"data-state":f?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":s,hidden:!n,id:d,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:v.current?"0s":void 0},children:n&&i})})});function q(e,t){return`${e}-trigger-${t}`}function V(e,t){return`${e}-content-${t}`}G.displayName=H;var $=P,U=B,Z=W,z=G},1899:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(1713).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},2634:(e,t,n)=>{"use strict";n.d(t,{A:()=>$});var r,o,a=function(){return(a=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var c=("function"==typeof SuppressedError&&SuppressedError,n(4232)),u="right-scroll-bar-position",l="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?c.useLayoutEffect:c.useEffect,f=new WeakMap;function p(e){return e}var v=function(e){void 0===e&&(e={});var t,n,r,o,i=(t=null,void 0===n&&(n=p),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var a=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}});return i.options=a({async:!0,ssr:!1},e),i}(),h=function(){},m=c.forwardRef(function(e,t){var n,r,o,u,l=c.useRef(null),p=c.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),m=p[0],g=p[1],y=e.forwardProps,b=e.children,w=e.className,E=e.removeScrollBar,x=e.enabled,A=e.shards,k=e.sideCar,C=e.noRelative,R=e.noIsolation,M=e.inert,S=e.allowPinchZoom,j=e.as,I=e.gapMode,D=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),N=(n=[l,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,c.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,d(function(){var e=f.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}f.set(u,n)},[n]),u),T=a(a({},D),m);return c.createElement(c.Fragment,null,x&&c.createElement(k,{sideCar:v,removeScrollBar:E,shards:A,noRelative:C,noIsolation:R,inert:M,setCallbacks:g,allowPinchZoom:!!S,lockRef:l,gapMode:I}),y?c.cloneElement(c.Children.only(b),a(a({},T),{ref:N})):c.createElement(void 0===j?"div":j,a({},T,{className:w,ref:N}),b))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:l,zeroRight:u};var g=function(e){var t=e.sideCar,n=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return c.createElement(r,a({},n))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,i;(a=t).styleSheet?a.styleSheet.cssText=r:a.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},b=function(){var e=y();return function(t,n){c.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},w=function(){var e=b();return function(t){return e(t.styles,t.dynamic),null}},E={left:0,top:0,right:0,gap:0},x=function(e){return parseInt(e||"",10)||0},A=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[x(n),x(r),x(o)]},k=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return E;var t=A(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},C=w(),R="data-scroll-locked",M=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body[").concat(R,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(l," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(R,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},S=function(){var e=parseInt(document.body.getAttribute(R)||"0",10);return isFinite(e)?e:0},j=function(){c.useEffect(function(){return document.body.setAttribute(R,(S()+1).toString()),function(){var e=S()-1;e<=0?document.body.removeAttribute(R):document.body.setAttribute(R,e.toString())}},[])},I=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;j();var a=c.useMemo(function(){return k(o)},[o]);return c.createElement(C,{styles:M(a,!t,o,n?"":"!important")})},D=!1;if("undefined"!=typeof window)try{var N=Object.defineProperty({},"passive",{get:function(){return D=!0,!0}});window.addEventListener("test",N,N),window.removeEventListener("test",N,N)}catch(e){D=!1}var T=!!D&&{passive:!1},F=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},L=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),O(e,r)){var o=P(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},O=function(e,t){return"v"===e?F(t,"overflowY"):F(t,"overflowX")},P=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},_=function(e,t,n,r,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),c=i*r,u=n.target,l=t.contains(u),s=!1,d=c>0,f=0,p=0;do{var v=P(e,u),h=v[0],m=v[1]-v[2]-i*h;(h||m)&&O(e,u)&&(f+=m,p+=h),u=u.parentNode.host||u.parentNode}while(!l&&u!==document.body||l&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&c>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-c>p)&&(s=!0),s},B=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},K=function(e){return[e.deltaX,e.deltaY]},W=function(e){return e&&"current"in e?e.current:e},H=0,G=[];let q=(r=function(e){var t=c.useRef([]),n=c.useRef([0,0]),r=c.useRef(),o=c.useState(H++)[0],a=c.useState(w)[0],i=c.useRef(e);c.useEffect(function(){i.current=e},[e]),c.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(W),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=c.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,a=B(e),c=n.current,u="deltaX"in e?e.deltaX:c[0]-a[0],l="deltaY"in e?e.deltaY:c[1]-a[1],s=e.target,d=Math.abs(u)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=L(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=L(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||l)&&(r.current=o),!o)return!0;var p=r.current||o;return _(p,t,e,"h"===p?u:l,!0)},[]),l=c.useCallback(function(e){if(G.length&&G[G.length-1]===a){var n="deltaY"in e?K(e):B(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(i.current.shards||[]).map(W).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=c.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=c.useCallback(function(e){n.current=B(e),r.current=void 0},[]),f=c.useCallback(function(t){s(t.type,K(t),t.target,u(t,e.lockRef.current))},[]),p=c.useCallback(function(t){s(t.type,B(t),t.target,u(t,e.lockRef.current))},[]);c.useEffect(function(){return G.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",l,T),document.addEventListener("touchmove",l,T),document.addEventListener("touchstart",d,T),function(){G=G.filter(function(e){return e!==a}),document.removeEventListener("wheel",l,T),document.removeEventListener("touchmove",l,T),document.removeEventListener("touchstart",d,T)}},[]);var v=e.removeScrollBar,h=e.inert;return c.createElement(c.Fragment,null,h?c.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?c.createElement(I,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},v.useMedium(r),g);var V=c.forwardRef(function(e,t){return c.createElement(m,a({},e,{ref:t,sideCar:q}))});V.classNames=m.classNames;let $=V},2658:(e,t,n)=>{"use strict";n.d(t,{Oh:()=>a});var r=n(4232),o=0;function a(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??i()),document.body.insertAdjacentElement("beforeend",e[1]??i()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function i(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},2891:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(1713).A)("volume-x",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]])},3590:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(1713).A)("circle-arrow-up",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m16 12-4-4-4 4",key:"177agl"}],["path",{d:"M12 16V8",key:"1sbj14"}]])},3907:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(1713).A)("gem",[["path",{d:"M6 3h12l4 6-10 13L2 9Z",key:"1pcd5k"}],["path",{d:"M11 3 8 9l4 13 4-13-3-6",key:"1fcu3u"}],["path",{d:"M2 9h20",key:"16fsjt"}]])},4769:(e,t,n)=>{"use strict";n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,a=new WeakMap,i={},c=0,u=function(e){return e&&(e.host||u(e.parentNode))},l=function(e,t,n,r){var l=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[n]||(i[n]=new WeakMap);var s=i[n],d=[],f=new Set,p=new Set(l),v=function(e){!e||f.has(e)||(f.add(e),v(e.parentNode))};l.forEach(v);var h=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))h(e);else try{var t=e.getAttribute(r),i=null!==t&&"false"!==t,c=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,c),s.set(e,u),d.push(e),1===c&&i&&a.set(e,!0),1===u&&e.setAttribute(n,"true"),i||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),f.clear(),c++,function(){d.forEach(function(e){var t=o.get(e)-1,i=s.get(e)-1;o.set(e,t),s.set(e,i),t||(a.has(e)||e.removeAttribute(r),a.delete(e)),i||e.removeAttribute(n)}),--c||(o=new WeakMap,o=new WeakMap,a=new WeakMap,i={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),a=t||r(e);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live], script"))),l(o,a,n,"aria-hidden")):function(){return null}}},4966:(e,t,n)=>{"use strict";n.d(t,{jH:()=>a});var r=n(4232);n(7876);var o=r.createContext(void 0);function a(e){let t=r.useContext(o);return e||t||"ltr"}},6303:(e,t,n)=>{"use strict";n.d(t,{UC:()=>en,VY:()=>eo,ZL:()=>ee,bL:()=>J,bm:()=>ea,hE:()=>er,hJ:()=>et,l9:()=>Q});var r=n(4232),o=n(3716),a=n(714),i=n(1844),c=n(294),u=n(8162),l=n(3520),s=n(870),d=n(1893),f=n(6822),p=n(6326),v=n(2658),h=n(2634),m=n(4769),g=n(2987),y=n(7876),b="Dialog",[w,E]=(0,i.A)(b),[x,A]=w(b),k=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:a,onOpenChange:i,modal:l=!0}=e,s=r.useRef(null),d=r.useRef(null),[f,p]=(0,u.i)({prop:o,defaultProp:a??!1,onChange:i,caller:b});return(0,y.jsx)(x,{scope:t,triggerRef:s,contentRef:d,contentId:(0,c.B)(),titleId:(0,c.B)(),descriptionId:(0,c.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:l,children:n})};k.displayName=b;var C="DialogTrigger",R=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=A(C,n),c=(0,a.s)(t,i.triggerRef);return(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":$(i.open),...r,ref:c,onClick:(0,o.m)(e.onClick,i.onOpenToggle)})});R.displayName=C;var M="DialogPortal",[S,j]=w(M,{forceMount:void 0}),I=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:a}=e,i=A(M,t);return(0,y.jsx)(S,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,y.jsx)(f.C,{present:n||i.open,children:(0,y.jsx)(d.Z,{asChild:!0,container:a,children:e})}))})};I.displayName=M;var D="DialogOverlay",N=r.forwardRef((e,t)=>{let n=j(D,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=A(D,e.__scopeDialog);return a.modal?(0,y.jsx)(f.C,{present:r||a.open,children:(0,y.jsx)(F,{...o,ref:t})}):null});N.displayName=D;var T=(0,g.TL)("DialogOverlay.RemoveScroll"),F=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=A(D,n);return(0,y.jsx)(h.A,{as:T,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(p.sG.div,{"data-state":$(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),L="DialogContent",O=r.forwardRef((e,t)=>{let n=j(L,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=A(L,e.__scopeDialog);return(0,y.jsx)(f.C,{present:r||a.open,children:a.modal?(0,y.jsx)(P,{...o,ref:t}):(0,y.jsx)(_,{...o,ref:t})})});O.displayName=L;var P=r.forwardRef((e,t)=>{let n=A(L,e.__scopeDialog),i=r.useRef(null),c=(0,a.s)(t,n.contentRef,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,m.Eq)(e)},[]),(0,y.jsx)(B,{...e,ref:c,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),_=r.forwardRef((e,t)=>{let n=A(L,e.__scopeDialog),o=r.useRef(!1),a=r.useRef(!1);return(0,y.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||n.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let r=t.target;n.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),B=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:c,...u}=e,d=A(L,n),f=r.useRef(null),p=(0,a.s)(t,f);return(0,v.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(s.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:c,children:(0,y.jsx)(l.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":$(d.open),...u,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(Y,{titleId:d.titleId}),(0,y.jsx)(X,{contentRef:f,descriptionId:d.descriptionId})]})]})}),K="DialogTitle",W=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=A(K,n);return(0,y.jsx)(p.sG.h2,{id:o.titleId,...r,ref:t})});W.displayName=K;var H="DialogDescription",G=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=A(H,n);return(0,y.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:t})});G.displayName=H;var q="DialogClose",V=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=A(q,n);return(0,y.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function $(e){return e?"open":"closed"}V.displayName=q;var U="DialogTitleWarning",[Z,z]=(0,i.q)(U,{contentName:L,titleName:K,docsSlug:"dialog"}),Y=({titleId:e})=>{let t=z(U),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return r.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},X=({contentRef:e,descriptionId:t})=>{let n=z("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return r.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&(document.getElementById(t)||console.warn(o))},[o,e,t]),null},J=k,Q=R,ee=I,et=N,en=O,er=W,eo=G,ea=V},6558:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(1713).A)("trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .*********** 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},6829:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(1713).A)("volume-2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]])},6973:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(1713).A)("wallet",[["path",{d:"M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1",key:"18etb6"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4",key:"xoc0q4"}]])},9099:(e,t,n)=>{e.exports=n(8253)}}]);