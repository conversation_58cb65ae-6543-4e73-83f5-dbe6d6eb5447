(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[295],{2982:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var a=t(7876),s=t(4232),l=t(9099),n=t(8230),i=t.n(n),o=t(4918),d=t(6960),c=t(7932),u=t(8638),m=t(3907),f=t(7897),x=t(2341);function p(){let[e,r]=(0,s.useState)(""),[t,n]=(0,s.useState)(""),[p,h]=(0,s.useState)(!1),[g,b]=(0,s.useState)(""),[v,y]=(0,s.useState)(!1),{login:N,user:j,loading:w}=(0,o.A)(),k=(0,l.useRouter)();(0,s.useEffect)(()=>{!w&&j&&k.push("/lobby")},[j,w,k]);let C=async r=>{r.preventDefault(),b(""),y(!0);try{await N(e,t)?k.push("/lobby"):b("Invalid email or password")}catch(e){b("Login failed. Please try again.")}finally{y(!1)}};return w?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-white"})}):j?null:(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsx)("div",{className:"text-center mb-8",children:(0,a.jsxs)(i(),{href:"/",className:"inline-flex items-center space-x-2 text-white hover:text-purple-400 transition-colors",children:[(0,a.jsx)(m.A,{className:"h-8 w-8"}),(0,a.jsx)("span",{className:"text-2xl font-bold",children:"BetOctave"})]})}),(0,a.jsxs)(u.Zp,{className:"bg-gray-800/50 border-gray-700 backdrop-blur-sm",children:[(0,a.jsxs)(u.aR,{className:"text-center",children:[(0,a.jsx)(u.ZB,{className:"text-2xl text-white",children:"Welcome Back"}),(0,a.jsx)(u.BT,{className:"text-gray-300",children:"Sign in to your account to continue playing"})]}),(0,a.jsxs)(u.Wu,{children:[(0,a.jsxs)("form",{onSubmit:C,className:"space-y-4",children:[g&&(0,a.jsx)("div",{className:"bg-red-500/10 border border-red-500/20 rounded-md p-3",children:(0,a.jsx)("p",{className:"text-red-400 text-sm",children:g})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{htmlFor:"email",className:"text-sm font-medium text-gray-300",children:"Email"}),(0,a.jsx)(c.p,{id:"email",type:"email",placeholder:"Enter your email",value:e,onChange:e=>r(e.target.value),required:!0,className:"bg-gray-700/50 border-gray-600 text-white placeholder:text-gray-400"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{htmlFor:"password",className:"text-sm font-medium text-gray-300",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.p,{id:"password",type:p?"text":"password",placeholder:"Enter your password",value:t,onChange:e=>n(e.target.value),required:!0,className:"bg-gray-700/50 border-gray-600 text-white placeholder:text-gray-400 pr-10"}),(0,a.jsx)("button",{type:"button",onClick:()=>h(!p),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300",children:p?(0,a.jsx)(f.A,{className:"h-4 w-4"}):(0,a.jsx)(x.A,{className:"h-4 w-4"})})]})]}),(0,a.jsx)(d.$,{type:"submit",className:"w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white",disabled:v,children:v?"Signing In...":"Sign In"})]}),(0,a.jsx)("div",{className:"mt-6 text-center",children:(0,a.jsxs)("p",{className:"text-gray-400",children:["Don't have an account?"," ",(0,a.jsx)(i(),{href:"/signup",className:"text-purple-400 hover:text-purple-300 font-medium",children:"Sign up"})]})})]})]}),(0,a.jsx)("div",{className:"mt-8 text-center",children:(0,a.jsx)(i(),{href:"/",className:"text-gray-400 hover:text-white transition-colors",children:"← Back to Home"})})]})})}},6960:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var a=t(7876),s=t(4232),l=t(2987),n=t(9518),i=t(684);let o=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,r)=>{let{className:t,variant:s,size:n,asChild:d=!1,...c}=e,u=d?l.DX:"button";return(0,a.jsx)(u,{className:(0,i.cn)(o({variant:s,size:n,className:t})),ref:r,...c})});d.displayName="Button"},7932:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var a=t(7876),s=t(4232),l=t(684);let n=s.forwardRef((e,r)=>{let{className:t,type:s,...n}=e;return(0,a.jsx)("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...n})});n.displayName="Input"},7984:(e,r,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/login",function(){return t(2982)}])},8638:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>i});var a=t(7876),s=t(4232),l=t(684);let n=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...s})});n.displayName="Card";let i=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...s})});i.displayName="CardHeader";let o=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...s})});o.displayName="CardTitle";let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",t),...s})});d.displayName="CardDescription";let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",t),...s})});c.displayName="CardContent",s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",t),...s})}).displayName="CardFooter"}},e=>{var r=r=>e(e.s=r);e.O(0,[370,636,593,792],()=>r(7984)),_N_E=e.O()}]);