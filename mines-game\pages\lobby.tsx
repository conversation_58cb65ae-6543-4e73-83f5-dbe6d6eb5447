import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { WalletModal } from '@/components/wallet/WalletModal';
import {
  Gem,
  LogOut,
  Wallet,
  Volume2,
  VolumeX,
  Search,
  TrendingUp,
  Users,
  Trophy,
  Clock,
  Star,
  Play,
  ChevronLeft,
  ChevronRight,
  Plus
} from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import { soundManager } from '@/lib/sounds';
import { Toaster } from '@/components/ui/toaster';
import { useToast } from '@/components/ui/use-toast';
import { GameConfig } from '@/types';

interface LobbyStats {
  playersOnline: number;
  totalBetsToday: number;
  biggestWinToday: number;
  totalGamesPlayed: number;
}

interface RecentWinner {
  id: string;
  username: string;
  game: string;
  multiplier: number;
  winAmount: number;
  timestamp: string;
}

export default function Lobby() {
  const { user, logout, loading: authLoading, refreshUser } = useAuth();
  const router = useRouter();
  const { toast } = useToast();

  // UI state
  const [showWallet, setShowWallet] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Data state
  const [games, setGames] = useState<GameConfig[]>([]);
  const [featuredGames, setFeaturedGames] = useState<GameConfig[]>([]);
  const [lobbyStats, setLobbyStats] = useState<LobbyStats>({
    playersOnline: 0,
    totalBetsToday: 0,
    biggestWinToday: 0,
    totalGamesPlayed: 0
  });
  const [recentWinners, setRecentWinners] = useState<RecentWinner[]>([]);
  const [loading, setLoading] = useState(true);

  // Carousel state
  const [currentFeaturedIndex, setCurrentFeaturedIndex] = useState(0);

  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login');
    }
  }, [user, authLoading, router]);

  useEffect(() => {
    soundManager.setEnabled(soundEnabled);
  }, [soundEnabled]);

  // Load games and lobby data
  useEffect(() => {
    if (user) {
      loadLobbyData();
    }
  }, [user]);

  // Auto-rotate featured games carousel
  useEffect(() => {
    if (featuredGames.length > 1) {
      const interval = setInterval(() => {
        setCurrentFeaturedIndex((prev) =>
          prev === featuredGames.length - 1 ? 0 : prev + 1
        );
      }, 5000);
      return () => clearInterval(interval);
    }
  }, [featuredGames.length]);

  const loadLobbyData = async () => {
    try {
      setLoading(true);

      // Load available games
      const gamesResponse = await fetch('/api/game/list?active=true');
      const gamesData = await gamesResponse.json();

      if (gamesData.success) {
        setGames(gamesData.games);
        setFeaturedGames(gamesData.games.filter((game: GameConfig) => game.isFeatured));
      }

      // Load lobby statistics
      const statsResponse = await fetch('/api/lobby/stats');
      const statsData = await statsResponse.json();

      if (statsData.success) {
        setLobbyStats({
          playersOnline: statsData.stats.playersOnline,
          totalBetsToday: statsData.stats.totalBetsToday,
          biggestWinToday: statsData.stats.biggestWinToday,
          totalGamesPlayed: statsData.stats.totalGamesPlayed
        });
        setRecentWinners(statsData.stats.recentWinners);
      } else {
        // Fallback to mock data if API fails
        setLobbyStats({
          playersOnline: Math.floor(Math.random() * 500) + 100,
          totalBetsToday: Math.floor(Math.random() * 10000) + 5000,
          biggestWinToday: Math.floor(Math.random() * 50000) + 10000,
          totalGamesPlayed: Math.floor(Math.random() * 100000) + 50000
        });
        setRecentWinners([]);
      }

    } catch (error) {
      console.error('Failed to load lobby data:', error);
      toast({
        title: "Error",
        description: "Failed to load lobby data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleGameSelect = (gameId: string) => {
    soundManager.play('click');

    if (gameId === 'mines') {
      router.push('/game/mines');
    } else if (gameId === 'dice') {
      router.push('/game/dice');
    } else {
      toast({
        title: "Coming Soon",
        description: `${gameId} will be available soon!`,
        variant: "info",
      });
    }
  };

  const handleAddFunds = async () => {
    try {
      const response = await fetch('/api/test/add-balance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ amount: 100 }),
      });

      const data = await response.json();

      if (data.success) {
        // Refresh user data to get updated balance
        await refreshUser();

        toast({
          title: "Funds Added!",
          description: "Successfully added $100 to your balance",
          variant: "default",
        });

        soundManager.play('win');
      } else {
        throw new Error(data.error || 'Failed to add funds');
      }
    } catch (error) {
      console.error('Failed to add funds:', error);
      toast({
        title: "Error",
        description: "Failed to add funds. Please try again.",
        variant: "destructive",
      });
    }
  };

  const filteredGames = games.filter(game => {
    const matchesSearch = game.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         game.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || game.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const nextFeatured = () => {
    setCurrentFeaturedIndex((prev) =>
      prev === featuredGames.length - 1 ? 0 : prev + 1
    );
  };

  const prevFeatured = () => {
    setCurrentFeaturedIndex((prev) =>
      prev === 0 ? featuredGames.length - 1 : prev - 1
    );
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-white"></div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <header className="bg-gray-800/50 border-b border-gray-700">
        <div className="container mx-auto px-4 py-3">
          <nav className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Gem className="h-6 w-6 text-purple-400" />
              <span className="text-xl font-bold text-white">BetOctave</span>
            </div>

            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={handleAddFunds}
                className="bg-green-600 hover:bg-green-700 text-white border-green-600"
              >
                <Plus className="h-4 w-4 sm:mr-2" />
                <span className="hidden sm:inline">Add Fund</span>
              </Button>

              <div className="bg-gray-700/50 px-3 py-1 rounded-md text-white text-sm">
                <span className="text-gray-300">{formatCurrency(user.usdt_balance)} </span>
                <span className="text-orange-400">₿</span>
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowWallet(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white border-blue-600"
              >
                <Wallet className="h-4 w-4 sm:mr-2" />
                <span className="hidden sm:inline">Wallet</span>
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setSoundEnabled(!soundEnabled)}
                className="border-gray-500 text-gray-400 hover:bg-gray-600 hover:text-white"
              >
                {soundEnabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={logout}
                className="border-gray-500 text-gray-400 hover:bg-gray-600 hover:text-white"
              >
                <LogOut className="h-4 w-4" />
              </Button>
            </div>
          </nav>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-6">
        <div className="grid lg:grid-cols-4 gap-6">
          {/* Left Sidebar - Stats & Recent Winners */}
          <div className="lg:col-span-1 space-y-6">
            {/* Live Statistics */}
            <Card className="bg-gray-800/80 border-gray-600">
              <CardHeader className="pb-3">
                <CardTitle className="text-white flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2 text-green-400" />
                  Live Stats
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-gray-300">
                    <Users className="h-4 w-4 mr-2" />
                    <span className="text-sm">Players Online</span>
                  </div>
                  <span className="text-green-400 font-semibold">{lobbyStats.playersOnline}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-gray-300">
                    <Trophy className="h-4 w-4 mr-2" />
                    <span className="text-sm">Biggest Win</span>
                  </div>
                  <span className="text-yellow-400 font-semibold">{formatCurrency(lobbyStats.biggestWinToday)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-gray-300">
                    <Clock className="h-4 w-4 mr-2" />
                    <span className="text-sm">Bets Today</span>
                  </div>
                  <span className="text-blue-400 font-semibold">{lobbyStats.totalBetsToday.toLocaleString()}</span>
                </div>
              </CardContent>
            </Card>

            {/* Recent Winners */}
            <Card className="bg-gray-800/80 border-gray-600">
              <CardHeader className="pb-3">
                <CardTitle className="text-white flex items-center">
                  <Trophy className="h-5 w-5 mr-2 text-yellow-400" />
                  Recent Winners
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {recentWinners.map((winner) => (
                  <div key={winner.id} className="flex items-center justify-between p-2 bg-gray-700/50 rounded-lg">
                    <div>
                      <div className="text-white text-sm font-medium">{winner.username}</div>
                      <div className="text-gray-400 text-xs">{winner.game}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-green-400 text-sm font-semibold">{winner.multiplier}x</div>
                      <div className="text-gray-300 text-xs">{formatCurrency(winner.winAmount)}</div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-3 space-y-6">
            {/* Featured Games Carousel */}
            {featuredGames.length > 0 && (
              <Card className="bg-gray-800/80 border-gray-600 overflow-hidden">
                <CardHeader className="pb-3">
                  <CardTitle className="text-white flex items-center">
                    <Star className="h-5 w-5 mr-2 text-yellow-400" />
                    Featured Games
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="relative">
                    <div className="overflow-hidden">
                      <div
                        className="flex transition-transform duration-500 ease-in-out"
                        style={{ transform: `translateX(-${currentFeaturedIndex * 100}%)` }}
                      >
                        {featuredGames.map((game) => (
                          <div key={game.id} className="w-full flex-shrink-0">
                            <div className="relative bg-gradient-to-r from-purple-600 to-pink-600 p-6 text-white">
                              <div className="flex items-center justify-between">
                                <div>
                                  <h3 className="text-2xl font-bold mb-2">{game.name}</h3>
                                  <p className="text-purple-100 mb-4">{game.description}</p>
                                  <div className="flex items-center space-x-4 mb-4">
                                    <span className="bg-white/20 px-2 py-1 rounded text-sm">
                                      Max {game.maxMultiplier}x
                                    </span>
                                    <span className="bg-white/20 px-2 py-1 rounded text-sm">
                                      Min {formatCurrency(game.minBet)}
                                    </span>
                                  </div>
                                  <Button
                                    onClick={() => handleGameSelect(game.id)}
                                    className="bg-white text-purple-600 hover:bg-gray-100"
                                  >
                                    <Play className="h-4 w-4 mr-2" />
                                    Play Now
                                  </Button>
                                </div>
                                <div className="text-6xl opacity-20">
                                  {game.icon}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {featuredGames.length > 1 && (
                      <>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={prevFeatured}
                          className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/20 hover:bg-black/40 text-white"
                        >
                          <ChevronLeft className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={nextFeatured}
                          className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/20 hover:bg-black/40 text-white"
                        >
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Search and Filter */}
            <Card className="bg-gray-800/80 border-gray-600">
              <CardContent className="p-4">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search games..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 bg-gray-700 border-gray-600 text-white placeholder-gray-400"
                    />
                  </div>
                  <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="w-full sm:w-auto">
                    <TabsList className="bg-gray-700 border-gray-600">
                      <TabsTrigger value="all" className="data-[state=active]:bg-purple-600">All</TabsTrigger>
                      <TabsTrigger value="originals" className="data-[state=active]:bg-purple-600">Originals</TabsTrigger>
                      <TabsTrigger value="slots" className="data-[state=active]:bg-purple-600">Slots</TabsTrigger>
                      <TabsTrigger value="live" className="data-[state=active]:bg-purple-600">Live</TabsTrigger>
                      <TabsTrigger value="table" className="data-[state=active]:bg-purple-600">Table</TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>
              </CardContent>
            </Card>

            {/* Games Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredGames.map((game) => (
                <Card
                  key={game.id}
                  className="bg-gray-800/80 border-gray-600 hover:border-purple-500 transition-all duration-200 cursor-pointer group"
                  onClick={() => handleGameSelect(game.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="text-3xl">{game.icon}</div>
                      <div className="flex items-center space-x-1">
                        {game.isFeatured && (
                          <Star className="h-4 w-4 text-yellow-400 fill-current" />
                        )}
                        {game.isNew && (
                          <span className="bg-green-500 text-white text-xs px-2 py-1 rounded">NEW</span>
                        )}
                      </div>
                    </div>

                    <h3 className="text-white font-semibold mb-2 group-hover:text-purple-400 transition-colors">
                      {game.name}
                    </h3>

                    <p className="text-gray-400 text-sm mb-3 line-clamp-2">
                      {game.description}
                    </p>

                    <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                      <span>Min: {formatCurrency(game.minBet)}</span>
                      <span>Max: {game.maxMultiplier}x</span>
                    </div>

                    <div className="flex flex-wrap gap-1 mb-3">
                      {game.features.slice(0, 2).map((feature, index) => (
                        <span
                          key={index}
                          className="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>

                    <Button
                      className="w-full bg-purple-600 hover:bg-purple-700 text-white group-hover:bg-purple-500"
                      size="sm"
                    >
                      <Play className="h-4 w-4 mr-2" />
                      {(game.id === 'mines' || game.id === 'dice') ? 'Play Now' : 'Coming Soon'}
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* No Games Found */}
            {filteredGames.length === 0 && (
              <Card className="bg-gray-800/80 border-gray-600">
                <CardContent className="p-8 text-center">
                  <div className="text-gray-400 mb-4">
                    <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <h3 className="text-lg font-semibold mb-2">No games found</h3>
                    <p>Try adjusting your search or filter criteria</p>
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSearchQuery('');
                      setSelectedCategory('all');
                    }}
                    className="border-gray-500 text-gray-400 hover:bg-gray-600 hover:text-white"
                  >
                    Clear Filters
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </main>

      {/* Modals */}
      <WalletModal
        user={user}
        isOpen={showWallet}
        onClose={() => setShowWallet(false)}
        onBalanceUpdate={() => setShowWallet(false)}
      />

      {/* Toast Notifications */}
      <Toaster />
    </div>
  );
}