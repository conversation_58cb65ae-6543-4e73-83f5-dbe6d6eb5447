/**
 * Quick test for crash game multiplier calculation
 */

const fetch = require('node-fetch');

let authCookie = '';

async function makeRequest(endpoint, options = {}) {
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers
  };
  
  if (authCookie) {
    headers['Cookie'] = authCookie;
  }

  const response = await fetch(`http://localhost:3000${endpoint}`, {
    ...options,
    headers,
    credentials: 'include'
  });

  // Extract cookies from response
  const setCookieHeader = response.headers.get('set-cookie');
  if (setCookieHeader) {
    authCookie = setCookieHeader;
  }

  return response;
}

async function testCrashMultiplier() {
  console.log('🧪 Testing Crash Game Multiplier Calculation...');
  
  try {
    // Login
    console.log('🔐 Logging in...');
    const loginResponse = await makeRequest('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'TestPass123'
      })
    });
    
    const loginResult = await loginResponse.json();
    if (!loginResult.success) {
      throw new Error('Login failed: ' + loginResult.error);
    }
    console.log('✅ Logged in successfully');
    
    // Create crash game
    console.log('🎮 Creating crash game...');
    const gameResponse = await makeRequest('/api/game/start', {
      method: 'POST',
      body: JSON.stringify({
        game_type: 'crash',
        bet_amount: 1
      })
    });
    
    const gameResult = await gameResponse.json();
    if (!gameResult.success) {
      throw new Error('Game creation failed: ' + gameResult.error);
    }
    
    console.log('✅ Game created successfully!');
    console.log('   Game ID:', gameResult.game.id);
    console.log('   Crash Point:', gameResult.game.crash_point + 'x');
    console.log('   Phase:', gameResult.game.phase);
    
    // Wait a few seconds and try to cash out
    console.log('\n⏱️  Waiting 3 seconds...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('💰 Attempting to cash out...');
    const cashoutResponse = await makeRequest('/api/game/cashout', {
      method: 'POST',
      body: JSON.stringify({
        game_id: gameResult.game.id,
        game_type: 'crash'
      })
    });
    
    const cashoutResult = await cashoutResponse.json();
    
    if (cashoutResult.success) {
      console.log('✅ Cash out successful!');
      console.log('   Multiplier:', cashoutResult.multiplier + 'x');
      console.log('   Profit:', '$' + cashoutResult.profit.toFixed(2));
    } else {
      console.log('❌ Cash out failed:', cashoutResult.error);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testCrashMultiplier();
