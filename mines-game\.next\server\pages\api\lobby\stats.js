"use strict";(()=>{var e={};e.id=869,e.ids=[869],e.modules={829:e=>{e.exports=require("jsonwebtoken")},3139:e=>{e.exports=import("bcryptjs")},3873:e=>{e.exports=require("path")},4507:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>i});var s=r(9103),n=r(3546),o=e([s]);s=(o.then?(await o)():o)[0];let i=(0,s.ru)(async(e,t,r)=>{if((0,n.s4)(),"GET"!==e.method)return t.status(405).json({success:!1,error:"Method not allowed"});try{let e=(0,n.C3)(),r=new Date;r.setHours(0,0,0,0);let a=r.toISOString(),s=e.prepare(`
      SELECT COUNT(*) as count
      FROM games
      WHERE created_at >= ?
    `).get(a),o=e.prepare(`
      SELECT COALESCE(SUM(bet_amount), 0) as total
      FROM games
      WHERE created_at >= ?
    `).get(a),i=e.prepare(`
      SELECT COALESCE(MAX(profit), 0) as max_profit
      FROM games
      WHERE created_at >= ? AND profit > 0
    `).get(a),u=e.prepare(`
      SELECT
        g.id,
        u.username,
        g.game_type,
        g.current_multiplier,
        g.profit,
        g.created_at
      FROM games g
      JOIN users u ON g.user_id = u.id
      WHERE g.profit > 0 AND g.status IN ('won', 'cashed_out')
      ORDER BY g.created_at DESC
      LIMIT 10
    `).all().map((e,t)=>({id:e.id.toString(),username:`${e.username.substring(0,6)}***`,game:e.game_type.charAt(0).toUpperCase()+e.game_type.slice(1),multiplier:parseFloat(e.current_multiplier.toFixed(2)),winAmount:parseFloat(e.profit.toFixed(2)),timestamp:e.created_at})),c=Math.floor(100*Math.random())-50,l={playersOnline:Math.max(50,150+c),totalBetsToday:Math.floor(o.total),biggestWinToday:parseFloat(i.max_profit.toFixed(2)),totalGamesPlayed:s.count,recentWinners:u};return t.status(200).json({success:!0,stats:l})}catch(e){return console.error("Failed to get lobby stats:",e),t.status(500).json({success:!1,error:"Failed to get lobby statistics"})}});a()}catch(e){a(e)}})},4912:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{config:()=>l,default:()=>c,routeModule:()=>d});var s=r(3480),n=r(8667),o=r(6435),i=r(4507),u=e([i]);i=(u.then?(await u)():u)[0];let c=(0,o.M)(i,"default"),l=(0,o.M)(i,"config"),d=new s.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/lobby/stats",pathname:"/api/lobby/stats",bundlePath:"",filename:""},userland:i});a()}catch(e){a(e)}})},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},7550:e=>{e.exports=require("better-sqlite3")},9021:e=>{e.exports=require("fs")},9103:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.d(t,{DY:()=>m,Eb:()=>h,Lx:()=>f,OB:()=>y,Tf:()=>E,VX:()=>g,b9:()=>d,ru:()=>p});var s=r(829),n=r.n(s),o=r(3139),i=r(3546),u=e([o]);o=(u.then?(await u)():u)[0];let w="your-super-secret-jwt-key-change-this-in-production-make-it-very-long-and-random";async function c(e){return o.default.hash(e,12)}async function l(e,t){return o.default.compare(e,t)}function d(e){let t=function(e){let t=e.headers.authorization;if(t&&t.startsWith("Bearer "))return t.substring(7);let r=e.cookies.token;return r||null}(e);if(!t)return null;let r=function(e){try{return n().verify(e,w)}catch(e){return null}}(t);return r&&r.userId?i.Gy.findById(r.userId):null}function p(e){return async(t,r)=>{try{let a=d(t);if(!a)return r.status(401).json({success:!1,error:"Authentication required"});await e(t,r,a)}catch(e){console.error("Auth middleware error:",e),r.status(500).json({success:!1,error:"Internal server error"})}}}async function m(e,t,r){try{let a=!e||e.length<3||e.length>20?"Username must be between 3 and 20 characters":/^[a-zA-Z0-9_]+$/.test(e)?t&&/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)?!r||r.length<8?"Password must be at least 8 characters long":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(r)?null:"Password must contain at least one uppercase letter, one lowercase letter, and one number":"Please provide a valid email address":"Username can only contain letters, numbers, and underscores";if(a)return{success:!1,error:a};if(i.Gy.findByEmail(t))return{success:!1,error:"Email already registered"};if(i.Gy.findByUsername(e))return{success:!1,error:"Username already taken"};let s=await c(r),{password_hash:n,...o}=i.Gy.create(e,t,s);return{success:!0,user:o}}catch(e){return console.error("Registration error:",e),{success:!1,error:"Failed to register user"}}}async function f(e,t){try{let r=e&&t?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)?null:"Please provide a valid email address":"Email and password are required";if(r)return{success:!1,error:r};let a=i.Gy.findByEmail(e);if(!a||!await l(t,a.password_hash))return{success:!1,error:"Invalid email or password"};let s=function(e){let t={userId:e.id,username:e.username,email:e.email};return n().sign(t,w,{expiresIn:"7d"})}(a),{password_hash:o,...u}=a;return{success:!0,user:u,token:s}}catch(e){return console.error("Login error:",e),{success:!1,error:"Failed to login"}}}function g(e,t){e.setHeader("Set-Cookie",[`token=${t}; HttpOnly; Path=/; Max-Age=604800; SameSite=Strict; Secure`])}function y(e){e.setHeader("Set-Cookie",["token=; HttpOnly; Path=/; Max-Age=0; SameSite=Strict"])}let b=new Map;function h(e,t=5,r=9e5){let a=Date.now(),s=b.get(e);return!s||a>s.resetTime?(b.set(e,{count:1,resetTime:a+r}),!0):!(s.count>=t)&&(s.count++,!0)}function E(e){let t=e.headers["x-forwarded-for"];return(t?Array.isArray(t)?t[0]:t.split(",")[0]:e.socket.remoteAddress)||"unknown"}a()}catch(e){a(e)}})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[405],()=>r(4912));module.exports=a})();