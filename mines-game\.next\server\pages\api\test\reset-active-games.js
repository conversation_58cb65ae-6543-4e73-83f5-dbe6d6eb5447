"use strict";(()=>{var e={};e.id=808,e.ids=[808],e.modules={3873:e=>{e.exports=require("path")},4165:(e,t,s)=>{s.r(t),s.d(t,{config:()=>l,default:()=>c,routeModule:()=>d});var r={};s.r(r),s.d(r,{default:()=>u});var a=s(3480),o=s(8667),i=s(6435),n=s(3546);async function u(e,t){if("POST"!==e.method)return t.status(405).json({error:"Method not allowed"});try{await (0,n.s4)();let e=s(3546).C3().prepare("UPDATE games SET status = ? WHERE status = ?").run("lost","active");return console.log(`🔄 Reset ${e.changes} active games to lost status`),t.status(200).json({success:!0,message:`Reset ${e.changes} active games`,gamesReset:e.changes})}catch(e){return console.error("Error resetting active games:",e),t.status(500).json({success:!1,error:"Internal server error"})}}let c=(0,i.M)(r,"default"),l=(0,i.M)(r,"config"),d=new a.PagesAPIRouteModule({definition:{kind:o.A.PAGES_API,page:"/api/test/reset-active-games",pathname:"/api/test/reset-active-games",bundlePath:"",filename:""},userland:r})},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},7550:e=>{e.exports=require("better-sqlite3")},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[405],()=>s(4165));module.exports=r})();