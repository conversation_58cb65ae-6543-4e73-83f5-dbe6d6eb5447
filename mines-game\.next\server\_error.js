/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "/_error";
exports.ids = ["/_error"];
exports.modules = {

/***/ "./components/ui/toast.tsx":
/*!*********************************!*\
  !*** ./components/ui/toast.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-toast */ \"@radix-ui/react-toast\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ \"class-variance-authority\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"__barrel_optimize__?names=X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__]);\n([_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 14,\n        columnNumber: 3\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\", {\n    variants: {\n        variant: {\n            default: \"border bg-background text-foreground\",\n            destructive: \"destructive border-destructive bg-destructive text-destructive-foreground\",\n            success: \"border-green-500 bg-green-500/10 text-green-400\",\n            warning: \"border-yellow-500 bg-yellow-500/10 text-yellow-400\",\n            info: \"border-blue-500 bg-blue-500/10 text-blue-400\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__.X, {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 87,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 78,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 96,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 108,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Description.displayName;\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/ui/toast.tsx\n");

/***/ }),

/***/ "./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Check if user is authenticated on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            const response = await fetch(_lib_utils__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.AUTH.ME, {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success && data.user) {\n                    setUser(data.user);\n                }\n            }\n        } catch (error) {\n            console.error('Auth check failed:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async (email, password)=>{\n        try {\n            setLoading(true);\n            const response = await fetch(_lib_utils__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.AUTH.LOGIN, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (data.success && data.user) {\n                setUser(data.user);\n                return true;\n            } else {\n                console.error('Login failed:', data.error);\n                return false;\n            }\n        } catch (error) {\n            console.error('Login error:', error);\n            return false;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signup = async (username, email, password)=>{\n        try {\n            setLoading(true);\n            const response = await fetch(_lib_utils__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.AUTH.SIGNUP, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    username,\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (data.success && data.user) {\n                setUser(data.user);\n                return true;\n            } else {\n                console.error('Signup failed:', data.error);\n                return false;\n            }\n        } catch (error) {\n            console.error('Signup error:', error);\n            return false;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await fetch(_lib_utils__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.AUTH.LOGOUT, {\n                method: 'POST',\n                credentials: 'include'\n            });\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            setUser(null);\n        }\n    };\n    const value = {\n        user,\n        login,\n        signup,\n        logout,\n        loading\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 118,\n        columnNumber: 10\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./contexts/AuthContext.tsx\n");

/***/ }),

/***/ "./contexts/CrashGameContext.tsx":
/*!***************************************!*\
  !*** ./contexts/CrashGameContext.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CrashGameProvider: () => (/* binding */ CrashGameProvider),\n/* harmony export */   useCrashGame: () => (/* binding */ useCrashGame)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _UniversalGameContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./UniversalGameContext */ \"./contexts/UniversalGameContext.tsx\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthContext */ \"./contexts/AuthContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_UniversalGameContext__WEBPACK_IMPORTED_MODULE_2__, _AuthContext__WEBPACK_IMPORTED_MODULE_3__]);\n([_UniversalGameContext__WEBPACK_IMPORTED_MODULE_2__, _AuthContext__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst CrashGameContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction CrashGameProvider({ children }) {\n    const universalGame = (0,_UniversalGameContext__WEBPACK_IMPORTED_MODULE_2__.useUniversalGame)();\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // Real-time state for crash game\n    const [currentMultiplier, setCurrentMultiplier] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.0);\n    const [timeElapsed, setTimeElapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [roundPhase, setRoundPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('betting');\n    const [timeUntilNextRound, setTimeUntilNextRound] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(5000);\n    const [currentCrashPoint, setCurrentCrashPoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Refs for intervals\n    const gameLoopRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const roundTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const roundStartTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Ensure we're working with a crash game\n    const crashGame = universalGame.currentGameType === 'crash' ? universalGame.currentGame : null;\n    const crashHistory = universalGame.gameHistory.filter((game)=>game.game_type === 'crash');\n    /**\n   * Start a new crash game\n   */ const startGame = async (betAmount, autoCashOut)=>{\n        return universalGame.startGame('crash', {\n            bet_amount: betAmount,\n            auto_cash_out: autoCashOut\n        });\n    };\n    /**\n   * Cash out the current crash game\n   */ const cashOut = async ()=>{\n        try {\n            const result = await universalGame.cashOut();\n            return {\n                success: result.success,\n                profit: result.profit,\n                multiplier: currentMultiplier\n            };\n        } catch (error) {\n            console.error('Cash out error:', error);\n            return {\n                success: false,\n                profit: 0,\n                multiplier: currentMultiplier\n            };\n        }\n    };\n    /**\n   * Reset the game state\n   */ const resetGame = ()=>{\n        universalGame.resetGame();\n        setCurrentMultiplier(1.0);\n        setTimeElapsed(0);\n        setRoundPhase('waiting');\n        setTimeUntilNextRound(0);\n    };\n    /**\n   * Switch to crash game mode\n   */ const switchToCrash = ()=>{\n        universalGame.switchGame('crash');\n    };\n    /**\n   * Load crash game history\n   */ const loadGameHistory = async ()=>{\n        await universalGame.loadGameHistory('crash');\n    };\n    /**\n   * Check if player can cash out\n   */ const canCashOut = ()=>{\n        return crashGame?.status === 'active' && roundPhase === 'flying' && !crashGame?.cashed_out;\n    };\n    /**\n   * Check if player can place a bet\n   */ const canPlaceBet = ()=>{\n        return roundPhase === 'betting' && !crashGame;\n    };\n    /**\n   * Get current multiplier\n   */ const getCurrentMultiplier = ()=>{\n        return currentMultiplier;\n    };\n    /**\n   * Get time elapsed in current round\n   */ const getTimeElapsed = ()=>{\n        return timeElapsed;\n    };\n    /**\n   * Get current round phase\n   */ const getRoundPhase = ()=>{\n        return roundPhase;\n    };\n    /**\n   * Get time until next round\n   */ const getTimeUntilNextRound = ()=>{\n        return timeUntilNextRound;\n    };\n    /**\n   * Get crash game statistics\n   */ const getCrashStats = ()=>{\n        if (!crashGame) return null;\n        return {\n            betAmount: crashGame.bet_amount,\n            currentMultiplier: currentMultiplier,\n            potentialPayout: crashGame.bet_amount * currentMultiplier,\n            phase: roundPhase,\n            timeElapsed: timeElapsed,\n            autoCashOut: crashGame.auto_cash_out,\n            profit: crashGame.profit,\n            status: crashGame.status\n        };\n    };\n    /**\n   * Generate a random crash point for the round\n   */ const generateCrashPoint = ()=>{\n        // Simple random crash point between 1.01x and 50x for testing\n        // Most crashes should be low, some high\n        const random = Math.random();\n        if (random < 0.5) {\n            // 50% chance of crash between 1.01x and 2x\n            return 1.01 + Math.random() * 0.99;\n        } else if (random < 0.8) {\n            // 30% chance of crash between 2x and 5x\n            return 2 + Math.random() * 3;\n        } else if (random < 0.95) {\n            // 15% chance of crash between 5x and 10x\n            return 5 + Math.random() * 5;\n        } else {\n            // 5% chance of crash between 10x and 50x\n            return 10 + Math.random() * 40;\n        }\n    };\n    /**\n   * Start the game loop for real-time updates\n   */ const startGameLoop = ()=>{\n        if (gameLoopRef.current) {\n            clearInterval(gameLoopRef.current);\n        }\n        // For active games, use the game's crash point and start time\n        let crashPoint = currentCrashPoint;\n        let gameStartTime = roundStartTime.current;\n        if (crashGame && crashGame.crash_point) {\n            crashPoint = crashGame.crash_point;\n            gameStartTime = new Date(crashGame.created_at).getTime();\n            setCurrentCrashPoint(crashPoint);\n            roundStartTime.current = gameStartTime;\n            console.log(`🚀 Resuming active game! Crash point: ${crashPoint.toFixed(2)}x`);\n        } else if (!crashPoint) {\n            // Generate crash point for demo round (when no active game)\n            crashPoint = generateCrashPoint();\n            setCurrentCrashPoint(crashPoint);\n            roundStartTime.current = Date.now();\n            console.log(`🚀 New demo round started! Crash point: ${crashPoint.toFixed(2)}x`);\n        }\n        gameLoopRef.current = setInterval(()=>{\n            if (roundPhase === 'flying') {\n                const now = Date.now();\n                const elapsed = now - (gameStartTime || now);\n                setTimeElapsed(elapsed);\n                // Calculate new multiplier using exponential growth\n                const newMultiplier = Math.pow(1.002, elapsed);\n                setCurrentMultiplier(Math.round(newMultiplier * 100) / 100);\n                // Check if we've hit the crash point\n                if (newMultiplier >= crashPoint) {\n                    console.log(`💥 CRASHED at ${newMultiplier.toFixed(2)}x (target: ${crashPoint.toFixed(2)}x)`);\n                    setCurrentMultiplier(crashPoint);\n                    setRoundPhase('crashed');\n                    stopGameLoop();\n                    // If there's an active game, end it as lost\n                    if (crashGame && !crashGame.cashed_out) {\n                        // The game crashed, player loses\n                        console.log('💥 Player lost - game crashed before cash out');\n                    }\n                    // Start waiting phase after 3 seconds\n                    setTimeout(()=>{\n                        startWaitingPhase();\n                    }, 3000);\n                }\n                // Check for auto cash out (if there's an active game)\n                if (crashGame?.auto_cash_out && newMultiplier >= crashGame.auto_cash_out && !crashGame.cashed_out) {\n                    cashOut();\n                }\n            }\n        }, 50); // Update every 50ms for smooth animation\n    };\n    /**\n   * Stop the game loop\n   */ const stopGameLoop = ()=>{\n        if (gameLoopRef.current) {\n            clearInterval(gameLoopRef.current);\n            gameLoopRef.current = null;\n        }\n    };\n    /**\n   * Start waiting phase after crash\n   */ const startWaitingPhase = ()=>{\n        setRoundPhase('waiting');\n        setTimeUntilNextRound(3000);\n        console.log('⏳ Waiting phase started - 3 seconds until next round');\n        // Countdown timer for waiting phase\n        const waitingTimer = setInterval(()=>{\n            setTimeUntilNextRound((prev)=>{\n                const newTime = prev - 100;\n                if (newTime <= 0) {\n                    clearInterval(waitingTimer);\n                    startBettingPhase();\n                    return 0;\n                }\n                return newTime;\n            });\n        }, 100);\n    };\n    /**\n   * Start betting phase\n   */ const startBettingPhase = ()=>{\n        setRoundPhase('betting');\n        setTimeUntilNextRound(5000);\n        setCurrentMultiplier(1.0);\n        setTimeElapsed(0);\n        setCurrentCrashPoint(null);\n        console.log('📝 Betting phase started - 5 seconds to place bets!');\n        // Countdown timer for betting phase\n        const bettingTimer = setInterval(()=>{\n            setTimeUntilNextRound((prev)=>{\n                const newTime = prev - 100;\n                if (newTime <= 0) {\n                    clearInterval(bettingTimer);\n                    // Check if there are any active bets before starting the flying phase\n                    if (crashGame) {\n                        // There's an active bet, start the flying phase\n                        console.log('🚀 Active bet found, starting flying phase!');\n                        startFlyingPhase();\n                    } else {\n                        // No active bets, skip this round and start a new betting phase\n                        console.log('⏭️ No bets placed, skipping round and starting new betting phase');\n                        setTimeout(()=>{\n                            startBettingPhase();\n                        }, 1000); // Wait 1 second before starting new betting phase\n                    }\n                    return 0;\n                }\n                return newTime;\n            });\n        }, 100);\n    };\n    /**\n   * Start flying phase\n   */ const startFlyingPhase = ()=>{\n        setRoundPhase('flying');\n        setTimeUntilNextRound(0);\n        console.log('🚀 Flying phase started!');\n        startGameLoop();\n    };\n    /**\n   * Initialize crash game rounds\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CrashGameProvider.useEffect\": ()=>{\n            // Only start the round cycle if there's no active game\n            if (!crashGame) {\n                startBettingPhase();\n            } else {\n                // If there's an active game, start the flying phase\n                setRoundPhase('flying');\n                setTimeUntilNextRound(0);\n                startGameLoop();\n            }\n            return ({\n                \"CrashGameProvider.useEffect\": ()=>{\n                    stopGameLoop();\n                    if (roundTimerRef.current) {\n                        clearTimeout(roundTimerRef.current);\n                    }\n                }\n            })[\"CrashGameProvider.useEffect\"];\n        }\n    }[\"CrashGameProvider.useEffect\"], [\n        crashGame\n    ]);\n    /**\n   * Handle game state changes\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CrashGameProvider.useEffect\": ()=>{\n            if (crashGame) {\n                if (crashGame.phase) {\n                    setRoundPhase(crashGame.phase);\n                }\n                if (crashGame.time_elapsed !== undefined) {\n                    setTimeElapsed(crashGame.time_elapsed);\n                }\n                if (crashGame.current_multiplier) {\n                    setCurrentMultiplier(crashGame.current_multiplier);\n                }\n            }\n        }\n    }[\"CrashGameProvider.useEffect\"], [\n        crashGame\n    ]);\n    const value = {\n        gameState: crashGame,\n        gameHistory: crashHistory,\n        loading: universalGame.loading,\n        error: universalGame.error,\n        startGame,\n        cashOut,\n        resetGame,\n        makeMove: cashOut,\n        // Crash-specific methods\n        switchToCrash,\n        loadGameHistory,\n        canCashOut,\n        canPlaceBet,\n        getCurrentMultiplier,\n        getTimeElapsed,\n        getRoundPhase,\n        getTimeUntilNextRound,\n        getCrashStats\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CrashGameContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\contexts\\\\CrashGameContext.tsx\",\n        lineNumber: 385,\n        columnNumber: 5\n    }, this);\n}\nfunction useCrashGame() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CrashGameContext);\n    if (context === undefined) {\n        throw new Error('useCrashGame must be used within a CrashGameProvider');\n    }\n    return context;\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./contexts/CrashGameContext.tsx\n");

/***/ }),

/***/ "./contexts/DiceGameContext.tsx":
/*!**************************************!*\
  !*** ./contexts/DiceGameContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiceGameProvider: () => (/* binding */ DiceGameProvider),\n/* harmony export */   useDiceGame: () => (/* binding */ useDiceGame)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _UniversalGameContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./UniversalGameContext */ \"./contexts/UniversalGameContext.tsx\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthContext */ \"./contexts/AuthContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_UniversalGameContext__WEBPACK_IMPORTED_MODULE_2__, _AuthContext__WEBPACK_IMPORTED_MODULE_3__]);\n([_UniversalGameContext__WEBPACK_IMPORTED_MODULE_2__, _AuthContext__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst DiceGameContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction DiceGameProvider({ children }) {\n    const universalGame = (0,_UniversalGameContext__WEBPACK_IMPORTED_MODULE_2__.useUniversalGame)();\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // Ensure we're working with a dice game\n    const diceGame = universalGame.currentGameType === 'dice' ? universalGame.currentGame : null;\n    const diceHistory = universalGame.gameHistory.filter((game)=>game.game_type === 'dice');\n    /**\n   * Start a new dice game\n   */ const startGame = async (betAmount, targetNumber, rollUnder)=>{\n        return universalGame.startGame('dice', {\n            bet_amount: betAmount,\n            target_number: targetNumber,\n            roll_under: rollUnder\n        });\n    };\n    /**\n   * Roll the dice\n   */ const rollDice = async ()=>{\n        if (!diceGame || diceGame.status !== 'active') {\n            throw new Error('No active dice game');\n        }\n        try {\n            const result = await universalGame.makeMove('dice', {\n                game_id: diceGame.id\n            });\n            if (result.success && result.gameState) {\n                const updatedGame = result.gameState;\n                return {\n                    result: updatedGame.result || 0,\n                    won: updatedGame.status === 'won',\n                    multiplier: updatedGame.current_multiplier,\n                    profit: updatedGame.profit\n                };\n            } else {\n                throw new Error(result.error || 'Failed to roll dice');\n            }\n        } catch (error) {\n            console.error('Error rolling dice:', error);\n            throw error;\n        }\n    };\n    /**\n   * Reset game state\n   */ const resetGame = ()=>{\n        universalGame.resetGame();\n    };\n    /**\n   * Switch to dice game type\n   */ const switchToDice = ()=>{\n        universalGame.switchGame('dice');\n    };\n    /**\n   * Load game history\n   */ const loadGameHistory = async ()=>{\n        await universalGame.loadGameHistory();\n    };\n    /**\n   * Check if player can roll dice\n   */ const canRollDice = ()=>{\n        return diceGame?.status === 'active' && diceGame.result === undefined;\n    };\n    /**\n   * Get dice game statistics\n   */ const getDiceStats = ()=>{\n        if (!diceGame) return null;\n        const winChance = diceGame.roll_under ? diceGame.target_number - 1 : 100 - diceGame.target_number;\n        return {\n            targetNumber: diceGame.target_number,\n            rollUnder: diceGame.roll_under,\n            winChance: winChance,\n            multiplier: diceGame.current_multiplier,\n            result: diceGame.result,\n            profit: diceGame.profit,\n            status: diceGame.status\n        };\n    };\n    /**\n   * Calculate win chance for given parameters\n   */ const calculateWinChance = (targetNumber, rollUnder)=>{\n        if (rollUnder) {\n            return targetNumber - 1;\n        } else {\n            return 100 - targetNumber;\n        }\n    };\n    /**\n   * Calculate multiplier for given parameters\n   */ const calculateMultiplier = (targetNumber, rollUnder)=>{\n        const winChance = calculateWinChance(targetNumber, rollUnder);\n        const houseEdge = 0.01; // 1%\n        const multiplier = (100 - houseEdge * 100) / winChance;\n        return Math.max(1.01, Math.round(multiplier * 10000) / 10000);\n    };\n    const value = {\n        gameState: diceGame,\n        gameHistory: diceHistory,\n        loading: universalGame.loading,\n        error: universalGame.error,\n        startGame,\n        rollDice,\n        resetGame,\n        makeMove: rollDice,\n        // Dice-specific methods\n        switchToDice,\n        loadGameHistory,\n        canRollDice,\n        getDiceStats,\n        calculateWinChance,\n        calculateMultiplier\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DiceGameContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\contexts\\\\DiceGameContext.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, this);\n}\nfunction useDiceGame() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(DiceGameContext);\n    if (context === undefined) {\n        throw new Error('useDiceGame must be used within a DiceGameProvider');\n    }\n    return context;\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./contexts/DiceGameContext.tsx\n");

/***/ }),

/***/ "./contexts/MinesGameContext.tsx":
/*!***************************************!*\
  !*** ./contexts/MinesGameContext.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MinesGameProvider: () => (/* binding */ MinesGameProvider),\n/* harmony export */   useGame: () => (/* binding */ useGame),\n/* harmony export */   useMinesGame: () => (/* binding */ useMinesGame)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _UniversalGameContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./UniversalGameContext */ \"./contexts/UniversalGameContext.tsx\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthContext */ \"./contexts/AuthContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_UniversalGameContext__WEBPACK_IMPORTED_MODULE_2__, _AuthContext__WEBPACK_IMPORTED_MODULE_3__]);\n([_UniversalGameContext__WEBPACK_IMPORTED_MODULE_2__, _AuthContext__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst MinesGameContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction MinesGameProvider({ children }) {\n    const universalGame = (0,_UniversalGameContext__WEBPACK_IMPORTED_MODULE_2__.useUniversalGame)();\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // Ensure we're working with a mines game\n    const minesGame = universalGame.currentGameType === 'mines' ? universalGame.currentGame : null;\n    const minesHistory = universalGame.gameHistory.filter((game)=>game.game_type === 'mines');\n    /**\n   * Start a new mines game\n   */ const startGame = async (betAmount, mineCount)=>{\n        return universalGame.startGame('mines', {\n            bet_amount: betAmount,\n            mine_count: mineCount\n        });\n    };\n    /**\n   * Reveal a cell in the mines game\n   */ const revealCell = async (cellIndex)=>{\n        try {\n            console.log('🎯 MinesGameContext: Attempting to reveal cell', cellIndex);\n            console.log('🎯 MinesGameContext: Current minesGame state:', minesGame);\n            console.log('🎯 MinesGameContext: Universal game active:', universalGame.isGameActive());\n            const result = await universalGame.makeMove({\n                cellIndex\n            });\n            console.log('🎯 MinesGameContext: Move result:', result);\n            return {\n                hit: result.hit || false,\n                multiplier: result.multiplier || minesGame?.current_multiplier || 1,\n                gameOver: result.gameOver || false,\n                profit: result.profit\n            };\n        } catch (error) {\n            console.error('🎯 MinesGameContext: Reveal cell error:', error);\n            throw error;\n        }\n    };\n    /**\n   * Cash out the current mines game\n   */ const cashOut = async ()=>{\n        return universalGame.cashOut();\n    };\n    /**\n   * Reset the game state\n   */ const resetGame = ()=>{\n        universalGame.resetGame();\n    };\n    /**\n   * Switch to mines game mode\n   */ const switchToMines = ()=>{\n        universalGame.switchGame('mines');\n    };\n    /**\n   * Load mines game history\n   */ const loadGameHistory = async ()=>{\n        await universalGame.loadGameHistory('mines');\n    };\n    /**\n   * Check if player can cash out\n   */ const canCashOut = ()=>{\n        return minesGame?.status === 'active' && (minesGame?.revealed_cells?.length || 0) > 0;\n    };\n    /**\n   * Get safe cells remaining\n   */ const getSafeCellsRemaining = ()=>{\n        if (!minesGame) return 0;\n        const totalSafeCells = (minesGame.grid_size || 25) - minesGame.mine_count;\n        return totalSafeCells - minesGame.revealed_cells.length;\n    };\n    /**\n   * Get next multiplier if a safe cell is revealed\n   */ const getNextMultiplier = ()=>{\n        if (!minesGame || minesGame.status !== 'active') {\n            return minesGame?.current_multiplier || 1.0;\n        }\n        // This would need to be calculated by the game provider\n        // For now, return current multiplier\n        return minesGame.current_multiplier;\n    };\n    /**\n   * Check if a cell is revealed\n   */ const isCellRevealed = (cellIndex)=>{\n        return minesGame?.revealed_cells?.includes(cellIndex) || false;\n    };\n    /**\n   * Check if a cell is a mine (only visible when game is over)\n   */ const isCellMine = (cellIndex)=>{\n        if (!minesGame || minesGame.status === 'active') return false;\n        return minesGame.mine_positions?.includes(cellIndex) || false;\n    };\n    /**\n   * Get game statistics\n   */ const getGameStats = ()=>{\n        if (!minesGame) return null;\n        return {\n            gridSize: minesGame.grid_size || 25,\n            mineCount: minesGame.mine_count,\n            revealedCells: minesGame.revealed_cells.length,\n            safeCellsRemaining: getSafeCellsRemaining(),\n            currentMultiplier: minesGame.current_multiplier,\n            profit: minesGame.profit,\n            status: minesGame.status\n        };\n    };\n    const value = {\n        gameState: minesGame,\n        gameHistory: minesHistory,\n        loading: universalGame.loading,\n        error: universalGame.error,\n        startGame,\n        revealCell,\n        cashOut,\n        resetGame,\n        makeMove: revealCell,\n        // Mines-specific methods\n        switchToMines,\n        loadGameHistory,\n        canCashOut,\n        getSafeCellsRemaining,\n        getNextMultiplier,\n        isCellRevealed,\n        isCellMine,\n        getGameStats\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MinesGameContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\contexts\\\\MinesGameContext.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, this);\n}\nfunction useMinesGame() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(MinesGameContext);\n    if (context === undefined) {\n        throw new Error('useMinesGame must be used within a MinesGameProvider');\n    }\n    return context;\n}\n// For backward compatibility, also export as useGame\nconst useGame = useMinesGame;\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./contexts/MinesGameContext.tsx\n");

/***/ }),

/***/ "./contexts/UniversalGameContext.tsx":
/*!*******************************************!*\
  !*** ./contexts/UniversalGameContext.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UniversalGameProvider: () => (/* binding */ UniversalGameProvider),\n/* harmony export */   useUniversalGame: () => (/* binding */ useUniversalGame)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"./contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_games_GameFactory__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/games/GameFactory */ \"./lib/games/GameFactory.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_AuthContext__WEBPACK_IMPORTED_MODULE_2__]);\n_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst UniversalGameContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction UniversalGameProvider({ children }) {\n    const [currentGame, setCurrentGame] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentGameType, setCurrentGameType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gameHistory, setGameHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    // Initialize game factory and load active game when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UniversalGameProvider.useEffect\": ()=>{\n            if (user) {\n                initializeAndLoadGame();\n            } else {\n                resetGame();\n            }\n        }\n    }[\"UniversalGameProvider.useEffect\"], [\n        user\n    ]);\n    /**\n   * Initialize game factory and load any active game\n   */ const initializeAndLoadGame = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Ensure game factory is initialized\n            await _lib_games_GameFactory__WEBPACK_IMPORTED_MODULE_3__.gameFactory.initialize();\n            // Load any active game\n            await loadActiveGame();\n        } catch (err) {\n            console.error('Failed to initialize games:', err);\n            setError('Failed to initialize games');\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\n   * Load active game from server\n   */ const loadActiveGame = async ()=>{\n        try {\n            const response = await fetch('/api/game/active', {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success && data.game) {\n                    setCurrentGame(data.game);\n                    setCurrentGameType(data.game.game_type);\n                }\n            }\n        } catch (err) {\n            console.error('Failed to load active game:', err);\n        }\n    };\n    /**\n   * Start a new game\n   */ const startGame = async (gameType, params)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Ensure we have the game type set\n            if (currentGameType !== gameType) {\n                setCurrentGameType(gameType);\n            }\n            const requestBody = {\n                game_type: gameType,\n                ...params\n            };\n            console.log('🌍 Frontend sending request:', JSON.stringify(requestBody, null, 2));\n            const response = await fetch('/api/game/start', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify(requestBody)\n            });\n            const data = await response.json();\n            if (data.success && data.game) {\n                setCurrentGame(data.game);\n                return true;\n            } else {\n                console.error('Game start failed:', data.error);\n                setError(data.error || 'Failed to start game');\n                return false;\n            }\n        } catch (err) {\n            console.error('Start game error:', err);\n            setError('Failed to start game');\n            return false;\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\n   * Make a move in the current game\n   */ const makeMove = async (params)=>{\n        console.log('🎯 UniversalGameContext: makeMove called with params:', params);\n        console.log('🎯 UniversalGameContext: currentGame:', currentGame);\n        console.log('🎯 UniversalGameContext: currentGameType:', currentGameType);\n        // First try to reload active game if we don't have one\n        if (!currentGame || !currentGameType) {\n            console.log('🎯 UniversalGameContext: No current game, attempting to reload...');\n            await loadActiveGame();\n            // If still no game after reload, this might be a timing issue\n            if (!currentGame || !currentGameType) {\n                console.warn('🎯 UniversalGameContext: No active game found after reload');\n                // Return a failure result instead of throwing\n                return {\n                    success: false,\n                    hit: true,\n                    gameOver: true,\n                    error: 'No active game'\n                };\n            }\n        }\n        try {\n            setLoading(true);\n            setError(null);\n            console.log('🎯 UniversalGameContext: Making API call to /api/game/move');\n            const response = await fetch('/api/game/move', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    game_id: currentGame.id,\n                    game_type: currentGameType,\n                    ...params\n                })\n            });\n            const data = await response.json();\n            console.log('🎯 UniversalGameContext: API response:', data);\n            if (data.success) {\n                // Update current game state\n                if (data.gameState) {\n                    setCurrentGame(data.gameState);\n                }\n                // If game is over, refresh history and clear current game\n                if (data.gameOver) {\n                    setTimeout(()=>{\n                        loadGameHistory(currentGameType);\n                        if (data.gameState?.status !== 'active') {\n                            setCurrentGame(null);\n                        }\n                    }, 2000);\n                }\n                return data;\n            } else {\n                console.error('🎯 UniversalGameContext: Move failed:', data.error);\n                setError(data.error || 'Move failed');\n                // Return failure result instead of throwing\n                return {\n                    success: false,\n                    hit: true,\n                    gameOver: true,\n                    error: data.error || 'Move failed'\n                };\n            }\n        } catch (err) {\n            console.error('🎯 UniversalGameContext: Make move error:', err);\n            setError(err instanceof Error ? err.message : 'Move failed');\n            // Return failure result instead of throwing\n            return {\n                success: false,\n                hit: true,\n                gameOver: true,\n                error: err instanceof Error ? err.message : 'Move failed'\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\n   * Cash out current game\n   */ const cashOut = async ()=>{\n        if (!currentGame) {\n            throw new Error('No active game');\n        }\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await fetch('/api/game/cashout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    game_id: currentGame.id,\n                    game_type: currentGameType\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Update game state\n                if (data.gameState) {\n                    setCurrentGame(data.gameState);\n                }\n                // Refresh history and clear current game\n                setTimeout(()=>{\n                    if (currentGameType) {\n                        loadGameHistory(currentGameType);\n                    }\n                    setCurrentGame(null);\n                }, 2000);\n                return {\n                    success: true,\n                    profit: data.profit || 0\n                };\n            } else {\n                setError(data.error || 'Cash out failed');\n                return {\n                    success: false,\n                    profit: 0\n                };\n            }\n        } catch (err) {\n            console.error('Cash out error:', err);\n            setError('Cash out failed');\n            return {\n                success: false,\n                profit: 0\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\n   * Reset current game state\n   */ const resetGame = ()=>{\n        setCurrentGame(null);\n        setCurrentGameType(null);\n        setGameHistory([]);\n        setError(null);\n    };\n    /**\n   * Force reset any active games (cancels active game and refunds bet)\n   */ const forceResetActiveGame = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch('/api/game/reset-active', {\n                method: 'POST',\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    resetGame();\n                    await loadActiveGame(); // Reload to confirm reset\n                    return true;\n                }\n            }\n            return false;\n        } catch (err) {\n            console.error('Failed to reset active game:', err);\n            return false;\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\n   * Switch to a different game type\n   */ const switchGame = (gameType)=>{\n        if (currentGame?.status === 'active') {\n            console.warn('Cannot switch games while a game is active');\n            return;\n        }\n        setCurrentGameType(gameType);\n        setCurrentGame(null);\n        setError(null);\n        // Load history for the new game type\n        loadGameHistory(gameType);\n    };\n    /**\n   * Load game history\n   */ const loadGameHistory = async (gameType)=>{\n        try {\n            const typeParam = gameType || currentGameType;\n            const url = typeParam ? `/api/game/history?game_type=${typeParam}` : '/api/game/history';\n            const response = await fetch(url, {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success && data.games) {\n                    setGameHistory(data.games);\n                }\n            }\n        } catch (err) {\n            console.error('Failed to load game history:', err);\n        }\n    };\n    /**\n   * Get game configuration\n   */ const getGameConfig = (gameType)=>{\n        return _lib_games_GameFactory__WEBPACK_IMPORTED_MODULE_3__.gameFactory.getGameConfig(gameType);\n    };\n    /**\n   * Check if current game is active\n   */ const isGameActive = ()=>{\n        return currentGame?.status === 'active';\n    };\n    /**\n   * Check if player can cash out\n   */ const canCashOut = ()=>{\n        if (!currentGame || !currentGameType) return false;\n        const provider = _lib_games_GameFactory__WEBPACK_IMPORTED_MODULE_3__.gameFactory.getGameProvider(currentGameType);\n        if (!provider) return false;\n        // For mines game, check if any cells are revealed\n        if (currentGameType === 'mines') {\n            return isGameActive() && currentGame.revealed_cells?.length > 0;\n        }\n        // Default: can cash out if game is active and has positive multiplier\n        return isGameActive() && currentGame.current_multiplier > 1;\n    };\n    const value = {\n        currentGame,\n        currentGameType,\n        gameHistory,\n        loading,\n        error,\n        startGame,\n        makeMove,\n        cashOut,\n        resetGame,\n        forceResetActiveGame,\n        switchGame,\n        loadGameHistory,\n        getGameConfig,\n        isGameActive,\n        canCashOut\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UniversalGameContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\contexts\\\\UniversalGameContext.tsx\",\n        lineNumber: 436,\n        columnNumber: 5\n    }, this);\n}\nfunction useUniversalGame() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UniversalGameContext);\n    if (context === undefined) {\n        throw new Error('useUniversalGame must be used within a UniversalGameProvider');\n    }\n    return context;\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb250ZXh0cy9Vbml2ZXJzYWxHYW1lQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQXlGO0FBRWpEO0FBQ2M7QUFnQ3RELE1BQU1PLHFDQUF1Qk4sb0RBQWFBLENBQXVDTztBQU0xRSxTQUFTQyxzQkFBc0IsRUFBRUMsUUFBUSxFQUE4QjtJQUM1RSxNQUFNLENBQUNDLGFBQWFDLGVBQWUsR0FBR1IsK0NBQVFBLENBQW1CO0lBQ2pFLE1BQU0sQ0FBQ1MsaUJBQWlCQyxtQkFBbUIsR0FBR1YsK0NBQVFBLENBQWtCO0lBQ3hFLE1BQU0sQ0FBQ1csYUFBYUMsZUFBZSxHQUFHWiwrQ0FBUUEsQ0FBYyxFQUFFO0lBQzlELE1BQU0sQ0FBQ2EsU0FBU0MsV0FBVyxHQUFHZCwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNlLE9BQU9DLFNBQVMsR0FBR2hCLCtDQUFRQSxDQUFnQjtJQUVsRCxNQUFNLEVBQUVpQixJQUFJLEVBQUUsR0FBR2hCLHFEQUFPQTtJQUV4QixpRUFBaUU7SUFDakVGLGdEQUFTQTsyQ0FBQztZQUNSLElBQUlrQixNQUFNO2dCQUNSQztZQUNGLE9BQU87Z0JBQ0xDO1lBQ0Y7UUFDRjswQ0FBRztRQUFDRjtLQUFLO0lBRVQ7O0dBRUMsR0FDRCxNQUFNQyx3QkFBd0I7UUFDNUIsSUFBSTtZQUNGSixXQUFXO1lBQ1hFLFNBQVM7WUFFVCxxQ0FBcUM7WUFDckMsTUFBTWQsK0RBQVdBLENBQUNrQixVQUFVO1lBRTVCLHVCQUF1QjtZQUN2QixNQUFNQztRQUVSLEVBQUUsT0FBT0MsS0FBSztZQUNaQyxRQUFRUixLQUFLLENBQUMsK0JBQStCTztZQUM3Q04sU0FBUztRQUNYLFNBQVU7WUFDUkYsV0FBVztRQUNiO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU1PLGlCQUFpQjtRQUNyQixJQUFJO1lBQ0YsTUFBTUcsV0FBVyxNQUFNQyxNQUFNLG9CQUFvQjtnQkFDL0NDLGFBQWE7WUFDZjtZQUVBLElBQUlGLFNBQVNHLEVBQUUsRUFBRTtnQkFDZixNQUFNQyxPQUFPLE1BQU1KLFNBQVNLLElBQUk7Z0JBQ2hDLElBQUlELEtBQUtFLE9BQU8sSUFBSUYsS0FBS0csSUFBSSxFQUFFO29CQUM3QnZCLGVBQWVvQixLQUFLRyxJQUFJO29CQUN4QnJCLG1CQUFtQmtCLEtBQUtHLElBQUksQ0FBQ0MsU0FBUztnQkFDeEM7WUFDRjtRQUNGLEVBQUUsT0FBT1YsS0FBSztZQUNaQyxRQUFRUixLQUFLLENBQUMsK0JBQStCTztRQUMvQztJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNVyxZQUFZLE9BQU9DLFVBQW9CQztRQUMzQyxJQUFJO1lBQ0ZyQixXQUFXO1lBQ1hFLFNBQVM7WUFFVCxtQ0FBbUM7WUFDbkMsSUFBSVAsb0JBQW9CeUIsVUFBVTtnQkFDaEN4QixtQkFBbUJ3QjtZQUNyQjtZQUVBLE1BQU1FLGNBQWM7Z0JBQ2xCSixXQUFXRTtnQkFDWCxHQUFHQyxNQUFNO1lBQ1g7WUFDQVosUUFBUWMsR0FBRyxDQUFDLGdDQUFnQ0MsS0FBS0MsU0FBUyxDQUFDSCxhQUFhLE1BQU07WUFFOUUsTUFBTVosV0FBVyxNQUFNQyxNQUFNLG1CQUFtQjtnQkFDOUNlLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQWYsYUFBYTtnQkFDYmdCLE1BQU1KLEtBQUtDLFNBQVMsQ0FBQ0g7WUFDdkI7WUFFQSxNQUFNUixPQUFPLE1BQU1KLFNBQVNLLElBQUk7WUFFaEMsSUFBSUQsS0FBS0UsT0FBTyxJQUFJRixLQUFLRyxJQUFJLEVBQUU7Z0JBQzdCdkIsZUFBZW9CLEtBQUtHLElBQUk7Z0JBQ3hCLE9BQU87WUFDVCxPQUFPO2dCQUNMUixRQUFRUixLQUFLLENBQUMsc0JBQXNCYSxLQUFLYixLQUFLO2dCQUM5Q0MsU0FBU1ksS0FBS2IsS0FBSyxJQUFJO2dCQUN2QixPQUFPO1lBQ1Q7UUFDRixFQUFFLE9BQU9PLEtBQUs7WUFDWkMsUUFBUVIsS0FBSyxDQUFDLHFCQUFxQk87WUFDbkNOLFNBQVM7WUFDVCxPQUFPO1FBQ1QsU0FBVTtZQUNSRixXQUFXO1FBQ2I7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTTZCLFdBQVcsT0FBT1I7UUFDdEJaLFFBQVFjLEdBQUcsQ0FBQyx5REFBeURGO1FBQ3JFWixRQUFRYyxHQUFHLENBQUMseUNBQXlDOUI7UUFDckRnQixRQUFRYyxHQUFHLENBQUMsNkNBQTZDNUI7UUFFekQsdURBQXVEO1FBQ3ZELElBQUksQ0FBQ0YsZUFBZSxDQUFDRSxpQkFBaUI7WUFDcENjLFFBQVFjLEdBQUcsQ0FBQztZQUNaLE1BQU1oQjtZQUVOLDhEQUE4RDtZQUM5RCxJQUFJLENBQUNkLGVBQWUsQ0FBQ0UsaUJBQWlCO2dCQUNwQ2MsUUFBUXFCLElBQUksQ0FBQztnQkFDYiw4Q0FBOEM7Z0JBQzlDLE9BQU87b0JBQ0xkLFNBQVM7b0JBQ1RlLEtBQUs7b0JBQ0xDLFVBQVU7b0JBQ1YvQixPQUFPO2dCQUNUO1lBQ0Y7UUFDRjtRQUVBLElBQUk7WUFDRkQsV0FBVztZQUNYRSxTQUFTO1lBRVRPLFFBQVFjLEdBQUcsQ0FBQztZQUNaLE1BQU1iLFdBQVcsTUFBTUMsTUFBTSxrQkFBa0I7Z0JBQzdDZSxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FmLGFBQWE7Z0JBQ2JnQixNQUFNSixLQUFLQyxTQUFTLENBQUM7b0JBQ25CUSxTQUFTeEMsWUFBWXlDLEVBQUU7b0JBQ3ZCaEIsV0FBV3ZCO29CQUNYLEdBQUcwQixNQUFNO2dCQUNYO1lBQ0Y7WUFFQSxNQUFNUCxPQUFPLE1BQU1KLFNBQVNLLElBQUk7WUFDaENOLFFBQVFjLEdBQUcsQ0FBQywwQ0FBMENUO1lBRXRELElBQUlBLEtBQUtFLE9BQU8sRUFBRTtnQkFDaEIsNEJBQTRCO2dCQUM1QixJQUFJRixLQUFLcUIsU0FBUyxFQUFFO29CQUNsQnpDLGVBQWVvQixLQUFLcUIsU0FBUztnQkFDL0I7Z0JBRUEsMERBQTBEO2dCQUMxRCxJQUFJckIsS0FBS2tCLFFBQVEsRUFBRTtvQkFDakJJLFdBQVc7d0JBQ1RDLGdCQUFnQjFDO3dCQUNoQixJQUFJbUIsS0FBS3FCLFNBQVMsRUFBRUcsV0FBVyxVQUFVOzRCQUN2QzVDLGVBQWU7d0JBQ2pCO29CQUNGLEdBQUc7Z0JBQ0w7Z0JBRUEsT0FBT29CO1lBQ1QsT0FBTztnQkFDTEwsUUFBUVIsS0FBSyxDQUFDLHlDQUF5Q2EsS0FBS2IsS0FBSztnQkFDakVDLFNBQVNZLEtBQUtiLEtBQUssSUFBSTtnQkFFdkIsNENBQTRDO2dCQUM1QyxPQUFPO29CQUNMZSxTQUFTO29CQUNUZSxLQUFLO29CQUNMQyxVQUFVO29CQUNWL0IsT0FBT2EsS0FBS2IsS0FBSyxJQUFJO2dCQUN2QjtZQUNGO1FBQ0YsRUFBRSxPQUFPTyxLQUFLO1lBQ1pDLFFBQVFSLEtBQUssQ0FBQyw2Q0FBNkNPO1lBQzNETixTQUFTTSxlQUFlK0IsUUFBUS9CLElBQUlnQyxPQUFPLEdBQUc7WUFFOUMsNENBQTRDO1lBQzVDLE9BQU87Z0JBQ0x4QixTQUFTO2dCQUNUZSxLQUFLO2dCQUNMQyxVQUFVO2dCQUNWL0IsT0FBT08sZUFBZStCLFFBQVEvQixJQUFJZ0MsT0FBTyxHQUFHO1lBQzlDO1FBQ0YsU0FBVTtZQUNSeEMsV0FBVztRQUNiO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU15QyxVQUFVO1FBQ2QsSUFBSSxDQUFDaEQsYUFBYTtZQUNoQixNQUFNLElBQUk4QyxNQUFNO1FBQ2xCO1FBRUEsSUFBSTtZQUNGdkMsV0FBVztZQUNYRSxTQUFTO1lBRVQsTUFBTVEsV0FBVyxNQUFNQyxNQUFNLHFCQUFxQjtnQkFDaERlLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQWYsYUFBYTtnQkFDYmdCLE1BQU1KLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkJRLFNBQVN4QyxZQUFZeUMsRUFBRTtvQkFDdkJoQixXQUFXdkI7Z0JBQ2I7WUFDRjtZQUVBLE1BQU1tQixPQUFPLE1BQU1KLFNBQVNLLElBQUk7WUFFaEMsSUFBSUQsS0FBS0UsT0FBTyxFQUFFO2dCQUNoQixvQkFBb0I7Z0JBQ3BCLElBQUlGLEtBQUtxQixTQUFTLEVBQUU7b0JBQ2xCekMsZUFBZW9CLEtBQUtxQixTQUFTO2dCQUMvQjtnQkFFQSx5Q0FBeUM7Z0JBQ3pDQyxXQUFXO29CQUNULElBQUl6QyxpQkFBaUI7d0JBQ25CMEMsZ0JBQWdCMUM7b0JBQ2xCO29CQUNBRCxlQUFlO2dCQUNqQixHQUFHO2dCQUVILE9BQU87b0JBQ0xzQixTQUFTO29CQUNUMEIsUUFBUTVCLEtBQUs0QixNQUFNLElBQUk7Z0JBQ3pCO1lBQ0YsT0FBTztnQkFDTHhDLFNBQVNZLEtBQUtiLEtBQUssSUFBSTtnQkFDdkIsT0FBTztvQkFDTGUsU0FBUztvQkFDVDBCLFFBQVE7Z0JBQ1Y7WUFDRjtRQUNGLEVBQUUsT0FBT2xDLEtBQUs7WUFDWkMsUUFBUVIsS0FBSyxDQUFDLG1CQUFtQk87WUFDakNOLFNBQVM7WUFDVCxPQUFPO2dCQUNMYyxTQUFTO2dCQUNUMEIsUUFBUTtZQUNWO1FBQ0YsU0FBVTtZQUNSMUMsV0FBVztRQUNiO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU1LLFlBQVk7UUFDaEJYLGVBQWU7UUFDZkUsbUJBQW1CO1FBQ25CRSxlQUFlLEVBQUU7UUFDakJJLFNBQVM7SUFDWDtJQUVBOztHQUVDLEdBQ0QsTUFBTXlDLHVCQUF1QjtRQUMzQixJQUFJO1lBQ0YzQyxXQUFXO1lBQ1gsTUFBTVUsV0FBVyxNQUFNQyxNQUFNLDBCQUEwQjtnQkFDckRlLFFBQVE7Z0JBQ1JkLGFBQWE7WUFDZjtZQUVBLElBQUlGLFNBQVNHLEVBQUUsRUFBRTtnQkFDZixNQUFNQyxPQUFPLE1BQU1KLFNBQVNLLElBQUk7Z0JBQ2hDLElBQUlELEtBQUtFLE9BQU8sRUFBRTtvQkFDaEJYO29CQUNBLE1BQU1FLGtCQUFrQiwwQkFBMEI7b0JBQ2xELE9BQU87Z0JBQ1Q7WUFDRjtZQUNBLE9BQU87UUFDVCxFQUFFLE9BQU9DLEtBQUs7WUFDWkMsUUFBUVIsS0FBSyxDQUFDLGdDQUFnQ087WUFDOUMsT0FBTztRQUNULFNBQVU7WUFDUlIsV0FBVztRQUNiO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU00QyxhQUFhLENBQUN4QjtRQUNsQixJQUFJM0IsYUFBYTZDLFdBQVcsVUFBVTtZQUNwQzdCLFFBQVFxQixJQUFJLENBQUM7WUFDYjtRQUNGO1FBRUFsQyxtQkFBbUJ3QjtRQUNuQjFCLGVBQWU7UUFDZlEsU0FBUztRQUVULHFDQUFxQztRQUNyQ21DLGdCQUFnQmpCO0lBQ2xCO0lBRUE7O0dBRUMsR0FDRCxNQUFNaUIsa0JBQWtCLE9BQU9qQjtRQUM3QixJQUFJO1lBQ0YsTUFBTXlCLFlBQVl6QixZQUFZekI7WUFDOUIsTUFBTW1ELE1BQU1ELFlBQ1IsQ0FBQyw0QkFBNEIsRUFBRUEsV0FBVyxHQUMxQztZQUVKLE1BQU1uQyxXQUFXLE1BQU1DLE1BQU1tQyxLQUFLO2dCQUNoQ2xDLGFBQWE7WUFDZjtZQUVBLElBQUlGLFNBQVNHLEVBQUUsRUFBRTtnQkFDZixNQUFNQyxPQUFPLE1BQU1KLFNBQVNLLElBQUk7Z0JBQ2hDLElBQUlELEtBQUtFLE9BQU8sSUFBSUYsS0FBS2lDLEtBQUssRUFBRTtvQkFDOUJqRCxlQUFlZ0IsS0FBS2lDLEtBQUs7Z0JBQzNCO1lBQ0Y7UUFDRixFQUFFLE9BQU92QyxLQUFLO1lBQ1pDLFFBQVFSLEtBQUssQ0FBQyxnQ0FBZ0NPO1FBQ2hEO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU13QyxnQkFBZ0IsQ0FBQzVCO1FBQ3JCLE9BQU9oQywrREFBV0EsQ0FBQzRELGFBQWEsQ0FBQzVCO0lBQ25DO0lBRUE7O0dBRUMsR0FDRCxNQUFNNkIsZUFBZTtRQUNuQixPQUFPeEQsYUFBYTZDLFdBQVc7SUFDakM7SUFFQTs7R0FFQyxHQUNELE1BQU1ZLGFBQWE7UUFDakIsSUFBSSxDQUFDekQsZUFBZSxDQUFDRSxpQkFBaUIsT0FBTztRQUU3QyxNQUFNd0QsV0FBVy9ELCtEQUFXQSxDQUFDZ0UsZUFBZSxDQUFDekQ7UUFDN0MsSUFBSSxDQUFDd0QsVUFBVSxPQUFPO1FBRXRCLGtEQUFrRDtRQUNsRCxJQUFJeEQsb0JBQW9CLFNBQVM7WUFDL0IsT0FBT3NELGtCQUFrQixZQUFxQkksY0FBYyxFQUFFQyxTQUFTO1FBQ3pFO1FBRUEsc0VBQXNFO1FBQ3RFLE9BQU9MLGtCQUFrQnhELFlBQVk4RCxrQkFBa0IsR0FBRztJQUM1RDtJQUVBLE1BQU1DLFFBQWtDO1FBQ3RDL0Q7UUFDQUU7UUFDQUU7UUFDQUU7UUFDQUU7UUFDQWtCO1FBQ0FVO1FBQ0FZO1FBQ0FwQztRQUNBc0M7UUFDQUM7UUFDQVA7UUFDQVc7UUFDQUM7UUFDQUM7SUFDRjtJQUVBLHFCQUNFLDhEQUFDN0QscUJBQXFCb0UsUUFBUTtRQUFDRCxPQUFPQTtrQkFDbkNoRTs7Ozs7O0FBR1A7QUFFTyxTQUFTa0U7SUFDZCxNQUFNQyxVQUFVM0UsaURBQVVBLENBQUNLO0lBQzNCLElBQUlzRSxZQUFZckUsV0FBVztRQUN6QixNQUFNLElBQUlpRCxNQUFNO0lBQ2xCO0lBQ0EsT0FBT29CO0FBQ1QiLCJzb3VyY2VzIjpbIkU6XFwxMTFcXFBST0pFQ1RcXG1pbmVzLWdhbWVcXGNvbnRleHRzXFxVbml2ZXJzYWxHYW1lQ29udGV4dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZUVmZmVjdCwgdXNlU3RhdGUsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IEdhbWVUeXBlLCBHYW1lU3RhdGUsIEJhc2VHYW1lQ29udGV4dFR5cGUgfSBmcm9tICdAL3R5cGVzJztcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tICcuL0F1dGhDb250ZXh0JztcbmltcG9ydCB7IGdhbWVGYWN0b3J5IH0gZnJvbSAnQC9saWIvZ2FtZXMvR2FtZUZhY3RvcnknO1xuaW1wb3J0IHsgQVBJX0VORFBPSU5UUyB9IGZyb20gJ0AvbGliL3V0aWxzJztcblxuaW50ZXJmYWNlIFVuaXZlcnNhbEdhbWVDb250ZXh0VHlwZSB7XG4gIC8vIEN1cnJlbnQgYWN0aXZlIGdhbWVcbiAgY3VycmVudEdhbWU6IEdhbWVTdGF0ZSB8IG51bGw7XG4gIGN1cnJlbnRHYW1lVHlwZTogR2FtZVR5cGUgfCBudWxsO1xuXG4gIC8vIEdhbWUgaGlzdG9yeVxuICBnYW1lSGlzdG9yeTogR2FtZVN0YXRlW107XG5cbiAgLy8gTG9hZGluZyBhbmQgZXJyb3Igc3RhdGVzXG4gIGxvYWRpbmc6IGJvb2xlYW47XG4gIGVycm9yOiBzdHJpbmcgfCBudWxsO1xuXG4gIC8vIFVuaXZlcnNhbCBnYW1lIGFjdGlvbnNcbiAgc3RhcnRHYW1lOiAoZ2FtZVR5cGU6IEdhbWVUeXBlLCBwYXJhbXM6IGFueSkgPT4gUHJvbWlzZTxib29sZWFuPjtcbiAgbWFrZU1vdmU6IChwYXJhbXM6IGFueSkgPT4gUHJvbWlzZTxhbnk+O1xuICBjYXNoT3V0OiAoKSA9PiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbjsgcHJvZml0OiBudW1iZXIgfT47XG4gIHJlc2V0R2FtZTogKCkgPT4gdm9pZDtcbiAgZm9yY2VSZXNldEFjdGl2ZUdhbWU6ICgpID0+IFByb21pc2U8Ym9vbGVhbj47XG5cbiAgLy8gR2FtZSBtYW5hZ2VtZW50XG4gIHN3aXRjaEdhbWU6IChnYW1lVHlwZTogR2FtZVR5cGUpID0+IHZvaWQ7XG4gIGxvYWRHYW1lSGlzdG9yeTogKGdhbWVUeXBlPzogR2FtZVR5cGUpID0+IFByb21pc2U8dm9pZD47XG5cbiAgLy8gVXRpbGl0eSBmdW5jdGlvbnNcbiAgZ2V0R2FtZUNvbmZpZzogKGdhbWVUeXBlOiBHYW1lVHlwZSkgPT4gYW55O1xuICBpc0dhbWVBY3RpdmU6ICgpID0+IGJvb2xlYW47XG4gIGNhbkNhc2hPdXQ6ICgpID0+IGJvb2xlYW47XG59XG5cbmNvbnN0IFVuaXZlcnNhbEdhbWVDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxVbml2ZXJzYWxHYW1lQ29udGV4dFR5cGUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZCk7XG5cbmludGVyZmFjZSBVbml2ZXJzYWxHYW1lUHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGU7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBVbml2ZXJzYWxHYW1lUHJvdmlkZXIoeyBjaGlsZHJlbiB9OiBVbml2ZXJzYWxHYW1lUHJvdmlkZXJQcm9wcykge1xuICBjb25zdCBbY3VycmVudEdhbWUsIHNldEN1cnJlbnRHYW1lXSA9IHVzZVN0YXRlPEdhbWVTdGF0ZSB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbY3VycmVudEdhbWVUeXBlLCBzZXRDdXJyZW50R2FtZVR5cGVdID0gdXNlU3RhdGU8R2FtZVR5cGUgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2dhbWVIaXN0b3J5LCBzZXRHYW1lSGlzdG9yeV0gPSB1c2VTdGF0ZTxHYW1lU3RhdGVbXT4oW10pO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG5cbiAgY29uc3QgeyB1c2VyIH0gPSB1c2VBdXRoKCk7XG5cbiAgLy8gSW5pdGlhbGl6ZSBnYW1lIGZhY3RvcnkgYW5kIGxvYWQgYWN0aXZlIGdhbWUgd2hlbiB1c2VyIGNoYW5nZXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAodXNlcikge1xuICAgICAgaW5pdGlhbGl6ZUFuZExvYWRHYW1lKCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHJlc2V0R2FtZSgpO1xuICAgIH1cbiAgfSwgW3VzZXJdKTtcblxuICAvKipcbiAgICogSW5pdGlhbGl6ZSBnYW1lIGZhY3RvcnkgYW5kIGxvYWQgYW55IGFjdGl2ZSBnYW1lXG4gICAqL1xuICBjb25zdCBpbml0aWFsaXplQW5kTG9hZEdhbWUgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICBzZXRFcnJvcihudWxsKTtcblxuICAgICAgLy8gRW5zdXJlIGdhbWUgZmFjdG9yeSBpcyBpbml0aWFsaXplZFxuICAgICAgYXdhaXQgZ2FtZUZhY3RvcnkuaW5pdGlhbGl6ZSgpO1xuXG4gICAgICAvLyBMb2FkIGFueSBhY3RpdmUgZ2FtZVxuICAgICAgYXdhaXQgbG9hZEFjdGl2ZUdhbWUoKTtcblxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGluaXRpYWxpemUgZ2FtZXM6JywgZXJyKTtcbiAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gaW5pdGlhbGl6ZSBnYW1lcycpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLyoqXG4gICAqIExvYWQgYWN0aXZlIGdhbWUgZnJvbSBzZXJ2ZXJcbiAgICovXG4gIGNvbnN0IGxvYWRBY3RpdmVHYW1lID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2dhbWUvYWN0aXZlJywge1xuICAgICAgICBjcmVkZW50aWFsczogJ2luY2x1ZGUnLFxuICAgICAgfSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBpZiAoZGF0YS5zdWNjZXNzICYmIGRhdGEuZ2FtZSkge1xuICAgICAgICAgIHNldEN1cnJlbnRHYW1lKGRhdGEuZ2FtZSk7XG4gICAgICAgICAgc2V0Q3VycmVudEdhbWVUeXBlKGRhdGEuZ2FtZS5nYW1lX3R5cGUpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBhY3RpdmUgZ2FtZTonLCBlcnIpO1xuICAgIH1cbiAgfTtcblxuICAvKipcbiAgICogU3RhcnQgYSBuZXcgZ2FtZVxuICAgKi9cbiAgY29uc3Qgc3RhcnRHYW1lID0gYXN5bmMgKGdhbWVUeXBlOiBHYW1lVHlwZSwgcGFyYW1zOiBhbnkpOiBQcm9taXNlPGJvb2xlYW4+ID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgICAvLyBFbnN1cmUgd2UgaGF2ZSB0aGUgZ2FtZSB0eXBlIHNldFxuICAgICAgaWYgKGN1cnJlbnRHYW1lVHlwZSAhPT0gZ2FtZVR5cGUpIHtcbiAgICAgICAgc2V0Q3VycmVudEdhbWVUeXBlKGdhbWVUeXBlKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgcmVxdWVzdEJvZHkgPSB7XG4gICAgICAgIGdhbWVfdHlwZTogZ2FtZVR5cGUsXG4gICAgICAgIC4uLnBhcmFtc1xuICAgICAgfTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn4yNIEZyb250ZW5kIHNlbmRpbmcgcmVxdWVzdDonLCBKU09OLnN0cmluZ2lmeShyZXF1ZXN0Qm9keSwgbnVsbCwgMikpO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2dhbWUvc3RhcnQnLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgY3JlZGVudGlhbHM6ICdpbmNsdWRlJyxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkocmVxdWVzdEJvZHkpLFxuICAgICAgfSk7XG5cbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgIGlmIChkYXRhLnN1Y2Nlc3MgJiYgZGF0YS5nYW1lKSB7XG4gICAgICAgIHNldEN1cnJlbnRHYW1lKGRhdGEuZ2FtZSk7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignR2FtZSBzdGFydCBmYWlsZWQ6JywgZGF0YS5lcnJvcik7XG4gICAgICAgIHNldEVycm9yKGRhdGEuZXJyb3IgfHwgJ0ZhaWxlZCB0byBzdGFydCBnYW1lJyk7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1N0YXJ0IGdhbWUgZXJyb3I6JywgZXJyKTtcbiAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gc3RhcnQgZ2FtZScpO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLyoqXG4gICAqIE1ha2UgYSBtb3ZlIGluIHRoZSBjdXJyZW50IGdhbWVcbiAgICovXG4gIGNvbnN0IG1ha2VNb3ZlID0gYXN5bmMgKHBhcmFtczogYW55KTogUHJvbWlzZTxhbnk+ID0+IHtcbiAgICBjb25zb2xlLmxvZygn8J+OryBVbml2ZXJzYWxHYW1lQ29udGV4dDogbWFrZU1vdmUgY2FsbGVkIHdpdGggcGFyYW1zOicsIHBhcmFtcyk7XG4gICAgY29uc29sZS5sb2coJ/Cfjq8gVW5pdmVyc2FsR2FtZUNvbnRleHQ6IGN1cnJlbnRHYW1lOicsIGN1cnJlbnRHYW1lKTtcbiAgICBjb25zb2xlLmxvZygn8J+OryBVbml2ZXJzYWxHYW1lQ29udGV4dDogY3VycmVudEdhbWVUeXBlOicsIGN1cnJlbnRHYW1lVHlwZSk7XG5cbiAgICAvLyBGaXJzdCB0cnkgdG8gcmVsb2FkIGFjdGl2ZSBnYW1lIGlmIHdlIGRvbid0IGhhdmUgb25lXG4gICAgaWYgKCFjdXJyZW50R2FtZSB8fCAhY3VycmVudEdhbWVUeXBlKSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+OryBVbml2ZXJzYWxHYW1lQ29udGV4dDogTm8gY3VycmVudCBnYW1lLCBhdHRlbXB0aW5nIHRvIHJlbG9hZC4uLicpO1xuICAgICAgYXdhaXQgbG9hZEFjdGl2ZUdhbWUoKTtcblxuICAgICAgLy8gSWYgc3RpbGwgbm8gZ2FtZSBhZnRlciByZWxvYWQsIHRoaXMgbWlnaHQgYmUgYSB0aW1pbmcgaXNzdWVcbiAgICAgIGlmICghY3VycmVudEdhbWUgfHwgIWN1cnJlbnRHYW1lVHlwZSkge1xuICAgICAgICBjb25zb2xlLndhcm4oJ/Cfjq8gVW5pdmVyc2FsR2FtZUNvbnRleHQ6IE5vIGFjdGl2ZSBnYW1lIGZvdW5kIGFmdGVyIHJlbG9hZCcpO1xuICAgICAgICAvLyBSZXR1cm4gYSBmYWlsdXJlIHJlc3VsdCBpbnN0ZWFkIG9mIHRocm93aW5nXG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgaGl0OiB0cnVlLCAvLyBUcmVhdCBhcyBsb3NzXG4gICAgICAgICAgZ2FtZU92ZXI6IHRydWUsXG4gICAgICAgICAgZXJyb3I6ICdObyBhY3RpdmUgZ2FtZSdcbiAgICAgICAgfTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgICBjb25zb2xlLmxvZygn8J+OryBVbml2ZXJzYWxHYW1lQ29udGV4dDogTWFraW5nIEFQSSBjYWxsIHRvIC9hcGkvZ2FtZS9tb3ZlJyk7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2dhbWUvbW92ZScsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBjcmVkZW50aWFsczogJ2luY2x1ZGUnLFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgZ2FtZV9pZDogY3VycmVudEdhbWUuaWQsXG4gICAgICAgICAgZ2FtZV90eXBlOiBjdXJyZW50R2FtZVR5cGUsXG4gICAgICAgICAgLi4ucGFyYW1zXG4gICAgICAgIH0pLFxuICAgICAgfSk7XG5cbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICBjb25zb2xlLmxvZygn8J+OryBVbml2ZXJzYWxHYW1lQ29udGV4dDogQVBJIHJlc3BvbnNlOicsIGRhdGEpO1xuXG4gICAgICBpZiAoZGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIC8vIFVwZGF0ZSBjdXJyZW50IGdhbWUgc3RhdGVcbiAgICAgICAgaWYgKGRhdGEuZ2FtZVN0YXRlKSB7XG4gICAgICAgICAgc2V0Q3VycmVudEdhbWUoZGF0YS5nYW1lU3RhdGUpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gSWYgZ2FtZSBpcyBvdmVyLCByZWZyZXNoIGhpc3RvcnkgYW5kIGNsZWFyIGN1cnJlbnQgZ2FtZVxuICAgICAgICBpZiAoZGF0YS5nYW1lT3Zlcikge1xuICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgbG9hZEdhbWVIaXN0b3J5KGN1cnJlbnRHYW1lVHlwZSk7XG4gICAgICAgICAgICBpZiAoZGF0YS5nYW1lU3RhdGU/LnN0YXR1cyAhPT0gJ2FjdGl2ZScpIHtcbiAgICAgICAgICAgICAgc2V0Q3VycmVudEdhbWUobnVsbCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSwgMjAwMCk7XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4gZGF0YTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ/Cfjq8gVW5pdmVyc2FsR2FtZUNvbnRleHQ6IE1vdmUgZmFpbGVkOicsIGRhdGEuZXJyb3IpO1xuICAgICAgICBzZXRFcnJvcihkYXRhLmVycm9yIHx8ICdNb3ZlIGZhaWxlZCcpO1xuXG4gICAgICAgIC8vIFJldHVybiBmYWlsdXJlIHJlc3VsdCBpbnN0ZWFkIG9mIHRocm93aW5nXG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgaGl0OiB0cnVlLCAvLyBUcmVhdCBhcyBsb3NzXG4gICAgICAgICAgZ2FtZU92ZXI6IHRydWUsXG4gICAgICAgICAgZXJyb3I6IGRhdGEuZXJyb3IgfHwgJ01vdmUgZmFpbGVkJ1xuICAgICAgICB9O1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcign8J+OryBVbml2ZXJzYWxHYW1lQ29udGV4dDogTWFrZSBtb3ZlIGVycm9yOicsIGVycik7XG4gICAgICBzZXRFcnJvcihlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ01vdmUgZmFpbGVkJyk7XG5cbiAgICAgIC8vIFJldHVybiBmYWlsdXJlIHJlc3VsdCBpbnN0ZWFkIG9mIHRocm93aW5nXG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgaGl0OiB0cnVlLCAvLyBUcmVhdCBhcyBsb3NzXG4gICAgICAgIGdhbWVPdmVyOiB0cnVlLFxuICAgICAgICBlcnJvcjogZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdNb3ZlIGZhaWxlZCdcbiAgICAgIH07XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvKipcbiAgICogQ2FzaCBvdXQgY3VycmVudCBnYW1lXG4gICAqL1xuICBjb25zdCBjYXNoT3V0ID0gYXN5bmMgKCk6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBwcm9maXQ6IG51bWJlciB9PiA9PiB7XG4gICAgaWYgKCFjdXJyZW50R2FtZSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdObyBhY3RpdmUgZ2FtZScpO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgICAgc2V0RXJyb3IobnVsbCk7XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvZ2FtZS9jYXNob3V0Jywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICAgIGNyZWRlbnRpYWxzOiAnaW5jbHVkZScsXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICBnYW1lX2lkOiBjdXJyZW50R2FtZS5pZCxcbiAgICAgICAgICBnYW1lX3R5cGU6IGN1cnJlbnRHYW1lVHlwZVxuICAgICAgICB9KSxcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuXG4gICAgICBpZiAoZGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIC8vIFVwZGF0ZSBnYW1lIHN0YXRlXG4gICAgICAgIGlmIChkYXRhLmdhbWVTdGF0ZSkge1xuICAgICAgICAgIHNldEN1cnJlbnRHYW1lKGRhdGEuZ2FtZVN0YXRlKTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIFJlZnJlc2ggaGlzdG9yeSBhbmQgY2xlYXIgY3VycmVudCBnYW1lXG4gICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgIGlmIChjdXJyZW50R2FtZVR5cGUpIHtcbiAgICAgICAgICAgIGxvYWRHYW1lSGlzdG9yeShjdXJyZW50R2FtZVR5cGUpO1xuICAgICAgICAgIH1cbiAgICAgICAgICBzZXRDdXJyZW50R2FtZShudWxsKTtcbiAgICAgICAgfSwgMjAwMCk7XG5cbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgIHByb2ZpdDogZGF0YS5wcm9maXQgfHwgMFxuICAgICAgICB9O1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0RXJyb3IoZGF0YS5lcnJvciB8fCAnQ2FzaCBvdXQgZmFpbGVkJyk7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgcHJvZml0OiAwXG4gICAgICAgIH07XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdDYXNoIG91dCBlcnJvcjonLCBlcnIpO1xuICAgICAgc2V0RXJyb3IoJ0Nhc2ggb3V0IGZhaWxlZCcpO1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIHByb2ZpdDogMFxuICAgICAgfTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8qKlxuICAgKiBSZXNldCBjdXJyZW50IGdhbWUgc3RhdGVcbiAgICovXG4gIGNvbnN0IHJlc2V0R2FtZSA9ICgpID0+IHtcbiAgICBzZXRDdXJyZW50R2FtZShudWxsKTtcbiAgICBzZXRDdXJyZW50R2FtZVR5cGUobnVsbCk7XG4gICAgc2V0R2FtZUhpc3RvcnkoW10pO1xuICAgIHNldEVycm9yKG51bGwpO1xuICB9O1xuXG4gIC8qKlxuICAgKiBGb3JjZSByZXNldCBhbnkgYWN0aXZlIGdhbWVzIChjYW5jZWxzIGFjdGl2ZSBnYW1lIGFuZCByZWZ1bmRzIGJldClcbiAgICovXG4gIGNvbnN0IGZvcmNlUmVzZXRBY3RpdmVHYW1lID0gYXN5bmMgKCk6IFByb21pc2U8Ym9vbGVhbj4gPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9nYW1lL3Jlc2V0LWFjdGl2ZScsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGNyZWRlbnRpYWxzOiAnaW5jbHVkZScsXG4gICAgICB9KTtcblxuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgICByZXNldEdhbWUoKTtcbiAgICAgICAgICBhd2FpdCBsb2FkQWN0aXZlR2FtZSgpOyAvLyBSZWxvYWQgdG8gY29uZmlybSByZXNldFxuICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gcmVzZXQgYWN0aXZlIGdhbWU6JywgZXJyKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8qKlxuICAgKiBTd2l0Y2ggdG8gYSBkaWZmZXJlbnQgZ2FtZSB0eXBlXG4gICAqL1xuICBjb25zdCBzd2l0Y2hHYW1lID0gKGdhbWVUeXBlOiBHYW1lVHlwZSkgPT4ge1xuICAgIGlmIChjdXJyZW50R2FtZT8uc3RhdHVzID09PSAnYWN0aXZlJykge1xuICAgICAgY29uc29sZS53YXJuKCdDYW5ub3Qgc3dpdGNoIGdhbWVzIHdoaWxlIGEgZ2FtZSBpcyBhY3RpdmUnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBzZXRDdXJyZW50R2FtZVR5cGUoZ2FtZVR5cGUpO1xuICAgIHNldEN1cnJlbnRHYW1lKG51bGwpO1xuICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgLy8gTG9hZCBoaXN0b3J5IGZvciB0aGUgbmV3IGdhbWUgdHlwZVxuICAgIGxvYWRHYW1lSGlzdG9yeShnYW1lVHlwZSk7XG4gIH07XG5cbiAgLyoqXG4gICAqIExvYWQgZ2FtZSBoaXN0b3J5XG4gICAqL1xuICBjb25zdCBsb2FkR2FtZUhpc3RvcnkgPSBhc3luYyAoZ2FtZVR5cGU/OiBHYW1lVHlwZSkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB0eXBlUGFyYW0gPSBnYW1lVHlwZSB8fCBjdXJyZW50R2FtZVR5cGU7XG4gICAgICBjb25zdCB1cmwgPSB0eXBlUGFyYW1cbiAgICAgICAgPyBgL2FwaS9nYW1lL2hpc3Rvcnk/Z2FtZV90eXBlPSR7dHlwZVBhcmFtfWBcbiAgICAgICAgOiAnL2FwaS9nYW1lL2hpc3RvcnknO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKHVybCwge1xuICAgICAgICBjcmVkZW50aWFsczogJ2luY2x1ZGUnLFxuICAgICAgfSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBpZiAoZGF0YS5zdWNjZXNzICYmIGRhdGEuZ2FtZXMpIHtcbiAgICAgICAgICBzZXRHYW1lSGlzdG9yeShkYXRhLmdhbWVzKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGxvYWQgZ2FtZSBoaXN0b3J5OicsIGVycik7XG4gICAgfVxuICB9O1xuXG4gIC8qKlxuICAgKiBHZXQgZ2FtZSBjb25maWd1cmF0aW9uXG4gICAqL1xuICBjb25zdCBnZXRHYW1lQ29uZmlnID0gKGdhbWVUeXBlOiBHYW1lVHlwZSkgPT4ge1xuICAgIHJldHVybiBnYW1lRmFjdG9yeS5nZXRHYW1lQ29uZmlnKGdhbWVUeXBlKTtcbiAgfTtcblxuICAvKipcbiAgICogQ2hlY2sgaWYgY3VycmVudCBnYW1lIGlzIGFjdGl2ZVxuICAgKi9cbiAgY29uc3QgaXNHYW1lQWN0aXZlID0gKCk6IGJvb2xlYW4gPT4ge1xuICAgIHJldHVybiBjdXJyZW50R2FtZT8uc3RhdHVzID09PSAnYWN0aXZlJztcbiAgfTtcblxuICAvKipcbiAgICogQ2hlY2sgaWYgcGxheWVyIGNhbiBjYXNoIG91dFxuICAgKi9cbiAgY29uc3QgY2FuQ2FzaE91dCA9ICgpOiBib29sZWFuID0+IHtcbiAgICBpZiAoIWN1cnJlbnRHYW1lIHx8ICFjdXJyZW50R2FtZVR5cGUpIHJldHVybiBmYWxzZTtcblxuICAgIGNvbnN0IHByb3ZpZGVyID0gZ2FtZUZhY3RvcnkuZ2V0R2FtZVByb3ZpZGVyKGN1cnJlbnRHYW1lVHlwZSk7XG4gICAgaWYgKCFwcm92aWRlcikgcmV0dXJuIGZhbHNlO1xuXG4gICAgLy8gRm9yIG1pbmVzIGdhbWUsIGNoZWNrIGlmIGFueSBjZWxscyBhcmUgcmV2ZWFsZWRcbiAgICBpZiAoY3VycmVudEdhbWVUeXBlID09PSAnbWluZXMnKSB7XG4gICAgICByZXR1cm4gaXNHYW1lQWN0aXZlKCkgJiYgKGN1cnJlbnRHYW1lIGFzIGFueSkucmV2ZWFsZWRfY2VsbHM/Lmxlbmd0aCA+IDA7XG4gICAgfVxuXG4gICAgLy8gRGVmYXVsdDogY2FuIGNhc2ggb3V0IGlmIGdhbWUgaXMgYWN0aXZlIGFuZCBoYXMgcG9zaXRpdmUgbXVsdGlwbGllclxuICAgIHJldHVybiBpc0dhbWVBY3RpdmUoKSAmJiBjdXJyZW50R2FtZS5jdXJyZW50X211bHRpcGxpZXIgPiAxO1xuICB9O1xuXG4gIGNvbnN0IHZhbHVlOiBVbml2ZXJzYWxHYW1lQ29udGV4dFR5cGUgPSB7XG4gICAgY3VycmVudEdhbWUsXG4gICAgY3VycmVudEdhbWVUeXBlLFxuICAgIGdhbWVIaXN0b3J5LFxuICAgIGxvYWRpbmcsXG4gICAgZXJyb3IsXG4gICAgc3RhcnRHYW1lLFxuICAgIG1ha2VNb3ZlLFxuICAgIGNhc2hPdXQsXG4gICAgcmVzZXRHYW1lLFxuICAgIGZvcmNlUmVzZXRBY3RpdmVHYW1lLFxuICAgIHN3aXRjaEdhbWUsXG4gICAgbG9hZEdhbWVIaXN0b3J5LFxuICAgIGdldEdhbWVDb25maWcsXG4gICAgaXNHYW1lQWN0aXZlLFxuICAgIGNhbkNhc2hPdXRcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxVbml2ZXJzYWxHYW1lQ29udGV4dC5Qcm92aWRlciB2YWx1ZT17dmFsdWV9PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvVW5pdmVyc2FsR2FtZUNvbnRleHQuUHJvdmlkZXI+XG4gICk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VVbml2ZXJzYWxHYW1lKCkge1xuICBjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChVbml2ZXJzYWxHYW1lQ29udGV4dCk7XG4gIGlmIChjb250ZXh0ID09PSB1bmRlZmluZWQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ3VzZVVuaXZlcnNhbEdhbWUgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIFVuaXZlcnNhbEdhbWVQcm92aWRlcicpO1xuICB9XG4gIHJldHVybiBjb250ZXh0O1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsInVzZUF1dGgiLCJnYW1lRmFjdG9yeSIsIlVuaXZlcnNhbEdhbWVDb250ZXh0IiwidW5kZWZpbmVkIiwiVW5pdmVyc2FsR2FtZVByb3ZpZGVyIiwiY2hpbGRyZW4iLCJjdXJyZW50R2FtZSIsInNldEN1cnJlbnRHYW1lIiwiY3VycmVudEdhbWVUeXBlIiwic2V0Q3VycmVudEdhbWVUeXBlIiwiZ2FtZUhpc3RvcnkiLCJzZXRHYW1lSGlzdG9yeSIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsInVzZXIiLCJpbml0aWFsaXplQW5kTG9hZEdhbWUiLCJyZXNldEdhbWUiLCJpbml0aWFsaXplIiwibG9hZEFjdGl2ZUdhbWUiLCJlcnIiLCJjb25zb2xlIiwicmVzcG9uc2UiLCJmZXRjaCIsImNyZWRlbnRpYWxzIiwib2siLCJkYXRhIiwianNvbiIsInN1Y2Nlc3MiLCJnYW1lIiwiZ2FtZV90eXBlIiwic3RhcnRHYW1lIiwiZ2FtZVR5cGUiLCJwYXJhbXMiLCJyZXF1ZXN0Qm9keSIsImxvZyIsIkpTT04iLCJzdHJpbmdpZnkiLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIm1ha2VNb3ZlIiwid2FybiIsImhpdCIsImdhbWVPdmVyIiwiZ2FtZV9pZCIsImlkIiwiZ2FtZVN0YXRlIiwic2V0VGltZW91dCIsImxvYWRHYW1lSGlzdG9yeSIsInN0YXR1cyIsIkVycm9yIiwibWVzc2FnZSIsImNhc2hPdXQiLCJwcm9maXQiLCJmb3JjZVJlc2V0QWN0aXZlR2FtZSIsInN3aXRjaEdhbWUiLCJ0eXBlUGFyYW0iLCJ1cmwiLCJnYW1lcyIsImdldEdhbWVDb25maWciLCJpc0dhbWVBY3RpdmUiLCJjYW5DYXNoT3V0IiwicHJvdmlkZXIiLCJnZXRHYW1lUHJvdmlkZXIiLCJyZXZlYWxlZF9jZWxscyIsImxlbmd0aCIsImN1cnJlbnRfbXVsdGlwbGllciIsInZhbHVlIiwiUHJvdmlkZXIiLCJ1c2VVbml2ZXJzYWxHYW1lIiwiY29udGV4dCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./contexts/UniversalGameContext.tsx\n");

/***/ }),

/***/ "./lib/crypto.ts":
/*!***********************!*\
  !*** ./lib/crypto.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateOutcomeHash: () => (/* binding */ calculateOutcomeHash),\n/* harmony export */   createGameHash: () => (/* binding */ createGameHash),\n/* harmony export */   createHMAC: () => (/* binding */ createHMAC),\n/* harmony export */   decrypt: () => (/* binding */ decrypt),\n/* harmony export */   encrypt: () => (/* binding */ encrypt),\n/* harmony export */   generateClientSeed: () => (/* binding */ generateClientSeed),\n/* harmony export */   generateGameId: () => (/* binding */ generateGameId),\n/* harmony export */   generateJWTSecret: () => (/* binding */ generateJWTSecret),\n/* harmony export */   generateMinePositions: () => (/* binding */ generateMinePositions),\n/* harmony export */   generateNonce: () => (/* binding */ generateNonce),\n/* harmony export */   generateServerSeed: () => (/* binding */ generateServerSeed),\n/* harmony export */   generateSessionToken: () => (/* binding */ generateSessionToken),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   hashServerSeed: () => (/* binding */ hashServerSeed),\n/* harmony export */   verifyGameIntegrity: () => (/* binding */ verifyGameIntegrity),\n/* harmony export */   verifyHMAC: () => (/* binding */ verifyHMAC),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n\n/**\n * Generate a cryptographically secure random server seed\n */ function generateServerSeed() {\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(32).toString('hex');\n}\n/**\n * Generate a random client seed\n */ function generateClientSeed() {\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(16).toString('hex');\n}\n/**\n * Create SHA-256 hash of combined seeds\n */ function createGameHash(serverSeed, clientSeed, nonce = 0) {\n    const combined = `${serverSeed}:${clientSeed}:${nonce}`;\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().createHash('sha256').update(combined).digest('hex');\n}\n/**\n * Generate mine positions using provably fair algorithm\n * This ensures the mine placement is deterministic based on seeds but unpredictable\n */ function generateMinePositions(serverSeed, clientSeed, mineCount, gridSize = 25) {\n    const hash = createGameHash(serverSeed, clientSeed);\n    const mines = [];\n    const availablePositions = Array.from({\n        length: gridSize\n    }, (_, i)=>i);\n    // Use hash bytes to determine mine positions\n    let hashIndex = 0;\n    while(mines.length < mineCount && availablePositions.length > 0){\n        // Get next 4 bytes from hash and convert to number\n        const bytes = hash.slice(hashIndex * 8, (hashIndex + 1) * 8);\n        const randomValue = parseInt(bytes, 16);\n        // Use modulo to get position within available positions\n        const positionIndex = randomValue % availablePositions.length;\n        const position = availablePositions[positionIndex];\n        mines.push(position);\n        availablePositions.splice(positionIndex, 1);\n        hashIndex++;\n        // If we run out of hash bytes, create a new hash with incremented nonce\n        if (hashIndex * 8 >= hash.length) {\n            const newHash = createGameHash(serverSeed, clientSeed, hashIndex);\n            const extendedHash = hash + newHash;\n            hashIndex = hash.length / 8;\n        }\n    }\n    return mines.sort((a, b)=>a - b);\n}\n/**\n * Verify game integrity by recalculating mine positions\n */ function verifyGameIntegrity(serverSeed, clientSeed, mineCount, expectedMines, gridSize = 25) {\n    const calculatedMines = generateMinePositions(serverSeed, clientSeed, mineCount, gridSize);\n    if (calculatedMines.length !== expectedMines.length) {\n        return false;\n    }\n    return calculatedMines.every((mine, index)=>mine === expectedMines[index]);\n}\n/**\n * Create a hash of the server seed for client verification\n * This allows clients to verify the server seed wasn't changed after the game\n */ function hashServerSeed(serverSeed) {\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().createHash('sha256').update(serverSeed).digest('hex');\n}\n/**\n * Generate a secure random nonce\n */ function generateNonce() {\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().randomInt(0, 2147483647); // Max 32-bit signed integer\n}\n/**\n * Create HMAC signature for API security\n */ function createHMAC(data, secret) {\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().createHmac('sha256', secret).update(data).digest('hex');\n}\n/**\n * Verify HMAC signature\n */ function verifyHMAC(data, signature, secret) {\n    const expectedSignature = createHMAC(data, secret);\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().timingSafeEqual(Buffer.from(signature), Buffer.from(expectedSignature));\n}\n/**\n * Generate a secure session token\n */ function generateSessionToken() {\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(32).toString('hex');\n}\n/**\n * Hash password using bcrypt-compatible method\n */ function hashPassword(password) {\n    const bcrypt = __webpack_require__(/*! bcryptjs */ \"bcryptjs?a1e7\");\n    return bcrypt.hashSync(password, 12);\n}\n/**\n * Verify password against hash\n */ function verifyPassword(password, hash) {\n    const bcrypt = __webpack_require__(/*! bcryptjs */ \"bcryptjs?a1e7\");\n    return bcrypt.compareSync(password, hash);\n}\n/**\n * Generate JWT secret key\n */ function generateJWTSecret() {\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(64).toString('hex');\n}\n/**\n * Calculate game outcome hash for transparency\n */ function calculateOutcomeHash(serverSeed, clientSeed, minePositions, revealedCells) {\n    const outcomeData = {\n        serverSeed,\n        clientSeed,\n        minePositions,\n        revealedCells,\n        timestamp: Date.now()\n    };\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().createHash('sha256').update(JSON.stringify(outcomeData)).digest('hex');\n}\n/**\n * Generate a unique game ID\n */ function generateGameId() {\n    const timestamp = Date.now().toString(36);\n    const random = crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(8).toString('hex');\n    return `${timestamp}-${random}`;\n}\n/**\n * Encrypt sensitive data\n */ function encrypt(text, key) {\n    const algorithm = 'aes-256-gcm';\n    const iv = crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(16);\n    const cipher = crypto__WEBPACK_IMPORTED_MODULE_0___default().createCipher(algorithm, key);\n    let encrypted = cipher.update(text, 'utf8', 'hex');\n    encrypted += cipher.final('hex');\n    return iv.toString('hex') + ':' + encrypted;\n}\n/**\n * Decrypt sensitive data\n */ function decrypt(encryptedText, key) {\n    const algorithm = 'aes-256-gcm';\n    const parts = encryptedText.split(':');\n    const iv = Buffer.from(parts[0], 'hex');\n    const encrypted = parts[1];\n    const decipher = crypto__WEBPACK_IMPORTED_MODULE_0___default().createDecipher(algorithm, key);\n    let decrypted = decipher.update(encrypted, 'hex', 'utf8');\n    decrypted += decipher.final('utf8');\n    return decrypted;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWIvY3J5cHRvLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBNEI7QUFFNUI7O0NBRUMsR0FDTSxTQUFTQztJQUNkLE9BQU9ELHlEQUFrQixDQUFDLElBQUlHLFFBQVEsQ0FBQztBQUN6QztBQUVBOztDQUVDLEdBQ00sU0FBU0M7SUFDZCxPQUFPSix5REFBa0IsQ0FBQyxJQUFJRyxRQUFRLENBQUM7QUFDekM7QUFFQTs7Q0FFQyxHQUNNLFNBQVNFLGVBQWVDLFVBQWtCLEVBQUVDLFVBQWtCLEVBQUVDLFFBQWdCLENBQUM7SUFDdEYsTUFBTUMsV0FBVyxHQUFHSCxXQUFXLENBQUMsRUFBRUMsV0FBVyxDQUFDLEVBQUVDLE9BQU87SUFDdkQsT0FBT1Isd0RBQWlCLENBQUMsVUFBVVcsTUFBTSxDQUFDRixVQUFVRyxNQUFNLENBQUM7QUFDN0Q7QUFFQTs7O0NBR0MsR0FDTSxTQUFTQyxzQkFDZFAsVUFBa0IsRUFDbEJDLFVBQWtCLEVBQ2xCTyxTQUFpQixFQUNqQkMsV0FBbUIsRUFBRTtJQUVyQixNQUFNQyxPQUFPWCxlQUFlQyxZQUFZQztJQUN4QyxNQUFNVSxRQUFrQixFQUFFO0lBQzFCLE1BQU1DLHFCQUFxQkMsTUFBTUMsSUFBSSxDQUFDO1FBQUVDLFFBQVFOO0lBQVMsR0FBRyxDQUFDTyxHQUFHQyxJQUFNQTtJQUV0RSw2Q0FBNkM7SUFDN0MsSUFBSUMsWUFBWTtJQUVoQixNQUFPUCxNQUFNSSxNQUFNLEdBQUdQLGFBQWFJLG1CQUFtQkcsTUFBTSxHQUFHLEVBQUc7UUFDaEUsbURBQW1EO1FBQ25ELE1BQU1JLFFBQVFULEtBQUtVLEtBQUssQ0FBQ0YsWUFBWSxHQUFHLENBQUNBLFlBQVksS0FBSztRQUMxRCxNQUFNRyxjQUFjQyxTQUFTSCxPQUFPO1FBRXBDLHdEQUF3RDtRQUN4RCxNQUFNSSxnQkFBZ0JGLGNBQWNULG1CQUFtQkcsTUFBTTtRQUM3RCxNQUFNUyxXQUFXWixrQkFBa0IsQ0FBQ1csY0FBYztRQUVsRFosTUFBTWMsSUFBSSxDQUFDRDtRQUNYWixtQkFBbUJjLE1BQU0sQ0FBQ0gsZUFBZTtRQUV6Q0w7UUFFQSx3RUFBd0U7UUFDeEUsSUFBSUEsWUFBWSxLQUFLUixLQUFLSyxNQUFNLEVBQUU7WUFDaEMsTUFBTVksVUFBVTVCLGVBQWVDLFlBQVlDLFlBQVlpQjtZQUN2RCxNQUFNVSxlQUFlbEIsT0FBT2lCO1lBQzVCVCxZQUFZUixLQUFLSyxNQUFNLEdBQUc7UUFDNUI7SUFDRjtJQUVBLE9BQU9KLE1BQU1rQixJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUQsSUFBSUM7QUFDbEM7QUFFQTs7Q0FFQyxHQUNNLFNBQVNDLG9CQUNkaEMsVUFBa0IsRUFDbEJDLFVBQWtCLEVBQ2xCTyxTQUFpQixFQUNqQnlCLGFBQXVCLEVBQ3ZCeEIsV0FBbUIsRUFBRTtJQUVyQixNQUFNeUIsa0JBQWtCM0Isc0JBQXNCUCxZQUFZQyxZQUFZTyxXQUFXQztJQUVqRixJQUFJeUIsZ0JBQWdCbkIsTUFBTSxLQUFLa0IsY0FBY2xCLE1BQU0sRUFBRTtRQUNuRCxPQUFPO0lBQ1Q7SUFFQSxPQUFPbUIsZ0JBQWdCQyxLQUFLLENBQUMsQ0FBQ0MsTUFBTUMsUUFBVUQsU0FBU0gsYUFBYSxDQUFDSSxNQUFNO0FBQzdFO0FBRUE7OztDQUdDLEdBQ00sU0FBU0MsZUFBZXRDLFVBQWtCO0lBQy9DLE9BQU9OLHdEQUFpQixDQUFDLFVBQVVXLE1BQU0sQ0FBQ0wsWUFBWU0sTUFBTSxDQUFDO0FBQy9EO0FBRUE7O0NBRUMsR0FDTSxTQUFTaUM7SUFDZCxPQUFPN0MsdURBQWdCLENBQUMsR0FBRyxhQUFhLDRCQUE0QjtBQUN0RTtBQUVBOztDQUVDLEdBQ00sU0FBUytDLFdBQVdDLElBQVksRUFBRUMsTUFBYztJQUNyRCxPQUFPakQsd0RBQWlCLENBQUMsVUFBVWlELFFBQVF0QyxNQUFNLENBQUNxQyxNQUFNcEMsTUFBTSxDQUFDO0FBQ2pFO0FBRUE7O0NBRUMsR0FDTSxTQUFTdUMsV0FBV0gsSUFBWSxFQUFFSSxTQUFpQixFQUFFSCxNQUFjO0lBQ3hFLE1BQU1JLG9CQUFvQk4sV0FBV0MsTUFBTUM7SUFDM0MsT0FBT2pELDZEQUFzQixDQUFDdUQsT0FBT25DLElBQUksQ0FBQ2dDLFlBQVlHLE9BQU9uQyxJQUFJLENBQUNpQztBQUNwRTtBQUVBOztDQUVDLEdBQ00sU0FBU0c7SUFDZCxPQUFPeEQseURBQWtCLENBQUMsSUFBSUcsUUFBUSxDQUFDO0FBQ3pDO0FBRUE7O0NBRUMsR0FDTSxTQUFTc0QsYUFBYUMsUUFBZ0I7SUFDM0MsTUFBTUMsU0FBU0MsbUJBQU9BLENBQUMsK0JBQVU7SUFDakMsT0FBT0QsT0FBT0UsUUFBUSxDQUFDSCxVQUFVO0FBQ25DO0FBRUE7O0NBRUMsR0FDTSxTQUFTSSxlQUFlSixRQUFnQixFQUFFMUMsSUFBWTtJQUMzRCxNQUFNMkMsU0FBU0MsbUJBQU9BLENBQUMsK0JBQVU7SUFDakMsT0FBT0QsT0FBT0ksV0FBVyxDQUFDTCxVQUFVMUM7QUFDdEM7QUFFQTs7Q0FFQyxHQUNNLFNBQVNnRDtJQUNkLE9BQU9oRSx5REFBa0IsQ0FBQyxJQUFJRyxRQUFRLENBQUM7QUFDekM7QUFFQTs7Q0FFQyxHQUNNLFNBQVM4RCxxQkFDZDNELFVBQWtCLEVBQ2xCQyxVQUFrQixFQUNsQjJELGFBQXVCLEVBQ3ZCQyxhQUF1QjtJQUV2QixNQUFNQyxjQUFjO1FBQ2xCOUQ7UUFDQUM7UUFDQTJEO1FBQ0FDO1FBQ0FFLFdBQVdDLEtBQUtDLEdBQUc7SUFDckI7SUFFQSxPQUFPdkUsd0RBQWlCLENBQUMsVUFBVVcsTUFBTSxDQUFDNkQsS0FBS0MsU0FBUyxDQUFDTCxjQUFjeEQsTUFBTSxDQUFDO0FBQ2hGO0FBRUE7O0NBRUMsR0FDTSxTQUFTOEQ7SUFDZCxNQUFNTCxZQUFZQyxLQUFLQyxHQUFHLEdBQUdwRSxRQUFRLENBQUM7SUFDdEMsTUFBTXdFLFNBQVMzRSx5REFBa0IsQ0FBQyxHQUFHRyxRQUFRLENBQUM7SUFDOUMsT0FBTyxHQUFHa0UsVUFBVSxDQUFDLEVBQUVNLFFBQVE7QUFDakM7QUFFQTs7Q0FFQyxHQUNNLFNBQVNDLFFBQVFDLElBQVksRUFBRUMsR0FBVztJQUMvQyxNQUFNQyxZQUFZO0lBQ2xCLE1BQU1DLEtBQUtoRix5REFBa0IsQ0FBQztJQUM5QixNQUFNaUYsU0FBU2pGLDBEQUFtQixDQUFDK0UsV0FBV0Q7SUFFOUMsSUFBSUssWUFBWUYsT0FBT3RFLE1BQU0sQ0FBQ2tFLE1BQU0sUUFBUTtJQUM1Q00sYUFBYUYsT0FBT0csS0FBSyxDQUFDO0lBRTFCLE9BQU9KLEdBQUc3RSxRQUFRLENBQUMsU0FBUyxNQUFNZ0Y7QUFDcEM7QUFFQTs7Q0FFQyxHQUNNLFNBQVNFLFFBQVFDLGFBQXFCLEVBQUVSLEdBQVc7SUFDeEQsTUFBTUMsWUFBWTtJQUNsQixNQUFNUSxRQUFRRCxjQUFjRSxLQUFLLENBQUM7SUFDbEMsTUFBTVIsS0FBS3pCLE9BQU9uQyxJQUFJLENBQUNtRSxLQUFLLENBQUMsRUFBRSxFQUFFO0lBQ2pDLE1BQU1KLFlBQVlJLEtBQUssQ0FBQyxFQUFFO0lBRTFCLE1BQU1FLFdBQVd6Riw0REFBcUIsQ0FBQytFLFdBQVdEO0lBQ2xELElBQUlhLFlBQVlGLFNBQVM5RSxNQUFNLENBQUN3RSxXQUFXLE9BQU87SUFDbERRLGFBQWFGLFNBQVNMLEtBQUssQ0FBQztJQUU1QixPQUFPTztBQUNUIiwic291cmNlcyI6WyJFOlxcMTExXFxQUk9KRUNUXFxtaW5lcy1nYW1lXFxsaWJcXGNyeXB0by50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3J5cHRvIGZyb20gJ2NyeXB0byc7XG5cbi8qKlxuICogR2VuZXJhdGUgYSBjcnlwdG9ncmFwaGljYWxseSBzZWN1cmUgcmFuZG9tIHNlcnZlciBzZWVkXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZVNlcnZlclNlZWQoKTogc3RyaW5nIHtcbiAgcmV0dXJuIGNyeXB0by5yYW5kb21CeXRlcygzMikudG9TdHJpbmcoJ2hleCcpO1xufVxuXG4vKipcbiAqIEdlbmVyYXRlIGEgcmFuZG9tIGNsaWVudCBzZWVkXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZUNsaWVudFNlZWQoKTogc3RyaW5nIHtcbiAgcmV0dXJuIGNyeXB0by5yYW5kb21CeXRlcygxNikudG9TdHJpbmcoJ2hleCcpO1xufVxuXG4vKipcbiAqIENyZWF0ZSBTSEEtMjU2IGhhc2ggb2YgY29tYmluZWQgc2VlZHNcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUdhbWVIYXNoKHNlcnZlclNlZWQ6IHN0cmluZywgY2xpZW50U2VlZDogc3RyaW5nLCBub25jZTogbnVtYmVyID0gMCk6IHN0cmluZyB7XG4gIGNvbnN0IGNvbWJpbmVkID0gYCR7c2VydmVyU2VlZH06JHtjbGllbnRTZWVkfToke25vbmNlfWA7XG4gIHJldHVybiBjcnlwdG8uY3JlYXRlSGFzaCgnc2hhMjU2JykudXBkYXRlKGNvbWJpbmVkKS5kaWdlc3QoJ2hleCcpO1xufVxuXG4vKipcbiAqIEdlbmVyYXRlIG1pbmUgcG9zaXRpb25zIHVzaW5nIHByb3ZhYmx5IGZhaXIgYWxnb3JpdGhtXG4gKiBUaGlzIGVuc3VyZXMgdGhlIG1pbmUgcGxhY2VtZW50IGlzIGRldGVybWluaXN0aWMgYmFzZWQgb24gc2VlZHMgYnV0IHVucHJlZGljdGFibGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdlbmVyYXRlTWluZVBvc2l0aW9ucyhcbiAgc2VydmVyU2VlZDogc3RyaW5nLFxuICBjbGllbnRTZWVkOiBzdHJpbmcsXG4gIG1pbmVDb3VudDogbnVtYmVyLFxuICBncmlkU2l6ZTogbnVtYmVyID0gMjVcbik6IG51bWJlcltdIHtcbiAgY29uc3QgaGFzaCA9IGNyZWF0ZUdhbWVIYXNoKHNlcnZlclNlZWQsIGNsaWVudFNlZWQpO1xuICBjb25zdCBtaW5lczogbnVtYmVyW10gPSBbXTtcbiAgY29uc3QgYXZhaWxhYmxlUG9zaXRpb25zID0gQXJyYXkuZnJvbSh7IGxlbmd0aDogZ3JpZFNpemUgfSwgKF8sIGkpID0+IGkpO1xuICBcbiAgLy8gVXNlIGhhc2ggYnl0ZXMgdG8gZGV0ZXJtaW5lIG1pbmUgcG9zaXRpb25zXG4gIGxldCBoYXNoSW5kZXggPSAwO1xuICBcbiAgd2hpbGUgKG1pbmVzLmxlbmd0aCA8IG1pbmVDb3VudCAmJiBhdmFpbGFibGVQb3NpdGlvbnMubGVuZ3RoID4gMCkge1xuICAgIC8vIEdldCBuZXh0IDQgYnl0ZXMgZnJvbSBoYXNoIGFuZCBjb252ZXJ0IHRvIG51bWJlclxuICAgIGNvbnN0IGJ5dGVzID0gaGFzaC5zbGljZShoYXNoSW5kZXggKiA4LCAoaGFzaEluZGV4ICsgMSkgKiA4KTtcbiAgICBjb25zdCByYW5kb21WYWx1ZSA9IHBhcnNlSW50KGJ5dGVzLCAxNik7XG4gICAgXG4gICAgLy8gVXNlIG1vZHVsbyB0byBnZXQgcG9zaXRpb24gd2l0aGluIGF2YWlsYWJsZSBwb3NpdGlvbnNcbiAgICBjb25zdCBwb3NpdGlvbkluZGV4ID0gcmFuZG9tVmFsdWUgJSBhdmFpbGFibGVQb3NpdGlvbnMubGVuZ3RoO1xuICAgIGNvbnN0IHBvc2l0aW9uID0gYXZhaWxhYmxlUG9zaXRpb25zW3Bvc2l0aW9uSW5kZXhdO1xuICAgIFxuICAgIG1pbmVzLnB1c2gocG9zaXRpb24pO1xuICAgIGF2YWlsYWJsZVBvc2l0aW9ucy5zcGxpY2UocG9zaXRpb25JbmRleCwgMSk7XG4gICAgXG4gICAgaGFzaEluZGV4Kys7XG4gICAgXG4gICAgLy8gSWYgd2UgcnVuIG91dCBvZiBoYXNoIGJ5dGVzLCBjcmVhdGUgYSBuZXcgaGFzaCB3aXRoIGluY3JlbWVudGVkIG5vbmNlXG4gICAgaWYgKGhhc2hJbmRleCAqIDggPj0gaGFzaC5sZW5ndGgpIHtcbiAgICAgIGNvbnN0IG5ld0hhc2ggPSBjcmVhdGVHYW1lSGFzaChzZXJ2ZXJTZWVkLCBjbGllbnRTZWVkLCBoYXNoSW5kZXgpO1xuICAgICAgY29uc3QgZXh0ZW5kZWRIYXNoID0gaGFzaCArIG5ld0hhc2g7XG4gICAgICBoYXNoSW5kZXggPSBoYXNoLmxlbmd0aCAvIDg7XG4gICAgfVxuICB9XG4gIFxuICByZXR1cm4gbWluZXMuc29ydCgoYSwgYikgPT4gYSAtIGIpO1xufVxuXG4vKipcbiAqIFZlcmlmeSBnYW1lIGludGVncml0eSBieSByZWNhbGN1bGF0aW5nIG1pbmUgcG9zaXRpb25zXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB2ZXJpZnlHYW1lSW50ZWdyaXR5KFxuICBzZXJ2ZXJTZWVkOiBzdHJpbmcsXG4gIGNsaWVudFNlZWQ6IHN0cmluZyxcbiAgbWluZUNvdW50OiBudW1iZXIsXG4gIGV4cGVjdGVkTWluZXM6IG51bWJlcltdLFxuICBncmlkU2l6ZTogbnVtYmVyID0gMjVcbik6IGJvb2xlYW4ge1xuICBjb25zdCBjYWxjdWxhdGVkTWluZXMgPSBnZW5lcmF0ZU1pbmVQb3NpdGlvbnMoc2VydmVyU2VlZCwgY2xpZW50U2VlZCwgbWluZUNvdW50LCBncmlkU2l6ZSk7XG4gIFxuICBpZiAoY2FsY3VsYXRlZE1pbmVzLmxlbmd0aCAhPT0gZXhwZWN0ZWRNaW5lcy5sZW5ndGgpIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbiAgXG4gIHJldHVybiBjYWxjdWxhdGVkTWluZXMuZXZlcnkoKG1pbmUsIGluZGV4KSA9PiBtaW5lID09PSBleHBlY3RlZE1pbmVzW2luZGV4XSk7XG59XG5cbi8qKlxuICogQ3JlYXRlIGEgaGFzaCBvZiB0aGUgc2VydmVyIHNlZWQgZm9yIGNsaWVudCB2ZXJpZmljYXRpb25cbiAqIFRoaXMgYWxsb3dzIGNsaWVudHMgdG8gdmVyaWZ5IHRoZSBzZXJ2ZXIgc2VlZCB3YXNuJ3QgY2hhbmdlZCBhZnRlciB0aGUgZ2FtZVxuICovXG5leHBvcnQgZnVuY3Rpb24gaGFzaFNlcnZlclNlZWQoc2VydmVyU2VlZDogc3RyaW5nKTogc3RyaW5nIHtcbiAgcmV0dXJuIGNyeXB0by5jcmVhdGVIYXNoKCdzaGEyNTYnKS51cGRhdGUoc2VydmVyU2VlZCkuZGlnZXN0KCdoZXgnKTtcbn1cblxuLyoqXG4gKiBHZW5lcmF0ZSBhIHNlY3VyZSByYW5kb20gbm9uY2VcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdlbmVyYXRlTm9uY2UoKTogbnVtYmVyIHtcbiAgcmV0dXJuIGNyeXB0by5yYW5kb21JbnQoMCwgMjE0NzQ4MzY0Nyk7IC8vIE1heCAzMi1iaXQgc2lnbmVkIGludGVnZXJcbn1cblxuLyoqXG4gKiBDcmVhdGUgSE1BQyBzaWduYXR1cmUgZm9yIEFQSSBzZWN1cml0eVxuICovXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlSE1BQyhkYXRhOiBzdHJpbmcsIHNlY3JldDogc3RyaW5nKTogc3RyaW5nIHtcbiAgcmV0dXJuIGNyeXB0by5jcmVhdGVIbWFjKCdzaGEyNTYnLCBzZWNyZXQpLnVwZGF0ZShkYXRhKS5kaWdlc3QoJ2hleCcpO1xufVxuXG4vKipcbiAqIFZlcmlmeSBITUFDIHNpZ25hdHVyZVxuICovXG5leHBvcnQgZnVuY3Rpb24gdmVyaWZ5SE1BQyhkYXRhOiBzdHJpbmcsIHNpZ25hdHVyZTogc3RyaW5nLCBzZWNyZXQ6IHN0cmluZyk6IGJvb2xlYW4ge1xuICBjb25zdCBleHBlY3RlZFNpZ25hdHVyZSA9IGNyZWF0ZUhNQUMoZGF0YSwgc2VjcmV0KTtcbiAgcmV0dXJuIGNyeXB0by50aW1pbmdTYWZlRXF1YWwoQnVmZmVyLmZyb20oc2lnbmF0dXJlKSwgQnVmZmVyLmZyb20oZXhwZWN0ZWRTaWduYXR1cmUpKTtcbn1cblxuLyoqXG4gKiBHZW5lcmF0ZSBhIHNlY3VyZSBzZXNzaW9uIHRva2VuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZVNlc3Npb25Ub2tlbigpOiBzdHJpbmcge1xuICByZXR1cm4gY3J5cHRvLnJhbmRvbUJ5dGVzKDMyKS50b1N0cmluZygnaGV4Jyk7XG59XG5cbi8qKlxuICogSGFzaCBwYXNzd29yZCB1c2luZyBiY3J5cHQtY29tcGF0aWJsZSBtZXRob2RcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGhhc2hQYXNzd29yZChwYXNzd29yZDogc3RyaW5nKTogc3RyaW5nIHtcbiAgY29uc3QgYmNyeXB0ID0gcmVxdWlyZSgnYmNyeXB0anMnKTtcbiAgcmV0dXJuIGJjcnlwdC5oYXNoU3luYyhwYXNzd29yZCwgMTIpO1xufVxuXG4vKipcbiAqIFZlcmlmeSBwYXNzd29yZCBhZ2FpbnN0IGhhc2hcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHZlcmlmeVBhc3N3b3JkKHBhc3N3b3JkOiBzdHJpbmcsIGhhc2g6IHN0cmluZyk6IGJvb2xlYW4ge1xuICBjb25zdCBiY3J5cHQgPSByZXF1aXJlKCdiY3J5cHRqcycpO1xuICByZXR1cm4gYmNyeXB0LmNvbXBhcmVTeW5jKHBhc3N3b3JkLCBoYXNoKTtcbn1cblxuLyoqXG4gKiBHZW5lcmF0ZSBKV1Qgc2VjcmV0IGtleVxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2VuZXJhdGVKV1RTZWNyZXQoKTogc3RyaW5nIHtcbiAgcmV0dXJuIGNyeXB0by5yYW5kb21CeXRlcyg2NCkudG9TdHJpbmcoJ2hleCcpO1xufVxuXG4vKipcbiAqIENhbGN1bGF0ZSBnYW1lIG91dGNvbWUgaGFzaCBmb3IgdHJhbnNwYXJlbmN5XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjYWxjdWxhdGVPdXRjb21lSGFzaChcbiAgc2VydmVyU2VlZDogc3RyaW5nLFxuICBjbGllbnRTZWVkOiBzdHJpbmcsXG4gIG1pbmVQb3NpdGlvbnM6IG51bWJlcltdLFxuICByZXZlYWxlZENlbGxzOiBudW1iZXJbXVxuKTogc3RyaW5nIHtcbiAgY29uc3Qgb3V0Y29tZURhdGEgPSB7XG4gICAgc2VydmVyU2VlZCxcbiAgICBjbGllbnRTZWVkLFxuICAgIG1pbmVQb3NpdGlvbnMsXG4gICAgcmV2ZWFsZWRDZWxscyxcbiAgICB0aW1lc3RhbXA6IERhdGUubm93KClcbiAgfTtcbiAgXG4gIHJldHVybiBjcnlwdG8uY3JlYXRlSGFzaCgnc2hhMjU2JykudXBkYXRlKEpTT04uc3RyaW5naWZ5KG91dGNvbWVEYXRhKSkuZGlnZXN0KCdoZXgnKTtcbn1cblxuLyoqXG4gKiBHZW5lcmF0ZSBhIHVuaXF1ZSBnYW1lIElEXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZUdhbWVJZCgpOiBzdHJpbmcge1xuICBjb25zdCB0aW1lc3RhbXAgPSBEYXRlLm5vdygpLnRvU3RyaW5nKDM2KTtcbiAgY29uc3QgcmFuZG9tID0gY3J5cHRvLnJhbmRvbUJ5dGVzKDgpLnRvU3RyaW5nKCdoZXgnKTtcbiAgcmV0dXJuIGAke3RpbWVzdGFtcH0tJHtyYW5kb219YDtcbn1cblxuLyoqXG4gKiBFbmNyeXB0IHNlbnNpdGl2ZSBkYXRhXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBlbmNyeXB0KHRleHQ6IHN0cmluZywga2V5OiBzdHJpbmcpOiBzdHJpbmcge1xuICBjb25zdCBhbGdvcml0aG0gPSAnYWVzLTI1Ni1nY20nO1xuICBjb25zdCBpdiA9IGNyeXB0by5yYW5kb21CeXRlcygxNik7XG4gIGNvbnN0IGNpcGhlciA9IGNyeXB0by5jcmVhdGVDaXBoZXIoYWxnb3JpdGhtLCBrZXkpO1xuICBcbiAgbGV0IGVuY3J5cHRlZCA9IGNpcGhlci51cGRhdGUodGV4dCwgJ3V0ZjgnLCAnaGV4Jyk7XG4gIGVuY3J5cHRlZCArPSBjaXBoZXIuZmluYWwoJ2hleCcpO1xuICBcbiAgcmV0dXJuIGl2LnRvU3RyaW5nKCdoZXgnKSArICc6JyArIGVuY3J5cHRlZDtcbn1cblxuLyoqXG4gKiBEZWNyeXB0IHNlbnNpdGl2ZSBkYXRhXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBkZWNyeXB0KGVuY3J5cHRlZFRleHQ6IHN0cmluZywga2V5OiBzdHJpbmcpOiBzdHJpbmcge1xuICBjb25zdCBhbGdvcml0aG0gPSAnYWVzLTI1Ni1nY20nO1xuICBjb25zdCBwYXJ0cyA9IGVuY3J5cHRlZFRleHQuc3BsaXQoJzonKTtcbiAgY29uc3QgaXYgPSBCdWZmZXIuZnJvbShwYXJ0c1swXSwgJ2hleCcpO1xuICBjb25zdCBlbmNyeXB0ZWQgPSBwYXJ0c1sxXTtcbiAgXG4gIGNvbnN0IGRlY2lwaGVyID0gY3J5cHRvLmNyZWF0ZURlY2lwaGVyKGFsZ29yaXRobSwga2V5KTtcbiAgbGV0IGRlY3J5cHRlZCA9IGRlY2lwaGVyLnVwZGF0ZShlbmNyeXB0ZWQsICdoZXgnLCAndXRmOCcpO1xuICBkZWNyeXB0ZWQgKz0gZGVjaXBoZXIuZmluYWwoJ3V0ZjgnKTtcbiAgXG4gIHJldHVybiBkZWNyeXB0ZWQ7XG59XG4iXSwibmFtZXMiOlsiY3J5cHRvIiwiZ2VuZXJhdGVTZXJ2ZXJTZWVkIiwicmFuZG9tQnl0ZXMiLCJ0b1N0cmluZyIsImdlbmVyYXRlQ2xpZW50U2VlZCIsImNyZWF0ZUdhbWVIYXNoIiwic2VydmVyU2VlZCIsImNsaWVudFNlZWQiLCJub25jZSIsImNvbWJpbmVkIiwiY3JlYXRlSGFzaCIsInVwZGF0ZSIsImRpZ2VzdCIsImdlbmVyYXRlTWluZVBvc2l0aW9ucyIsIm1pbmVDb3VudCIsImdyaWRTaXplIiwiaGFzaCIsIm1pbmVzIiwiYXZhaWxhYmxlUG9zaXRpb25zIiwiQXJyYXkiLCJmcm9tIiwibGVuZ3RoIiwiXyIsImkiLCJoYXNoSW5kZXgiLCJieXRlcyIsInNsaWNlIiwicmFuZG9tVmFsdWUiLCJwYXJzZUludCIsInBvc2l0aW9uSW5kZXgiLCJwb3NpdGlvbiIsInB1c2giLCJzcGxpY2UiLCJuZXdIYXNoIiwiZXh0ZW5kZWRIYXNoIiwic29ydCIsImEiLCJiIiwidmVyaWZ5R2FtZUludGVncml0eSIsImV4cGVjdGVkTWluZXMiLCJjYWxjdWxhdGVkTWluZXMiLCJldmVyeSIsIm1pbmUiLCJpbmRleCIsImhhc2hTZXJ2ZXJTZWVkIiwiZ2VuZXJhdGVOb25jZSIsInJhbmRvbUludCIsImNyZWF0ZUhNQUMiLCJkYXRhIiwic2VjcmV0IiwiY3JlYXRlSG1hYyIsInZlcmlmeUhNQUMiLCJzaWduYXR1cmUiLCJleHBlY3RlZFNpZ25hdHVyZSIsInRpbWluZ1NhZmVFcXVhbCIsIkJ1ZmZlciIsImdlbmVyYXRlU2Vzc2lvblRva2VuIiwiaGFzaFBhc3N3b3JkIiwicGFzc3dvcmQiLCJiY3J5cHQiLCJyZXF1aXJlIiwiaGFzaFN5bmMiLCJ2ZXJpZnlQYXNzd29yZCIsImNvbXBhcmVTeW5jIiwiZ2VuZXJhdGVKV1RTZWNyZXQiLCJjYWxjdWxhdGVPdXRjb21lSGFzaCIsIm1pbmVQb3NpdGlvbnMiLCJyZXZlYWxlZENlbGxzIiwib3V0Y29tZURhdGEiLCJ0aW1lc3RhbXAiLCJEYXRlIiwibm93IiwiSlNPTiIsInN0cmluZ2lmeSIsImdlbmVyYXRlR2FtZUlkIiwicmFuZG9tIiwiZW5jcnlwdCIsInRleHQiLCJrZXkiLCJhbGdvcml0aG0iLCJpdiIsImNpcGhlciIsImNyZWF0ZUNpcGhlciIsImVuY3J5cHRlZCIsImZpbmFsIiwiZGVjcnlwdCIsImVuY3J5cHRlZFRleHQiLCJwYXJ0cyIsInNwbGl0IiwiZGVjaXBoZXIiLCJjcmVhdGVEZWNpcGhlciIsImRlY3J5cHRlZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./lib/crypto.ts\n");

/***/ }),

/***/ "./lib/games/BaseGameProvider.ts":
/*!***************************************!*\
  !*** ./lib/games/BaseGameProvider.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseGameProvider: () => (/* binding */ BaseGameProvider),\n/* harmony export */   GameActionError: () => (/* binding */ GameActionError),\n/* harmony export */   GameActionType: () => (/* binding */ GameActionType),\n/* harmony export */   GameError: () => (/* binding */ GameError),\n/* harmony export */   GameNotActiveError: () => (/* binding */ GameNotActiveError),\n/* harmony export */   InsufficientFundsError: () => (/* binding */ InsufficientFundsError),\n/* harmony export */   InvalidGameParamsError: () => (/* binding */ InvalidGameParamsError)\n/* harmony export */ });\n/* harmony import */ var _lib_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/crypto */ \"./lib/crypto.ts\");\n\n/**\n * Abstract base class for all game providers\n * Implements common functionality and enforces interface compliance\n */ class BaseGameProvider {\n    /**\n   * Validate common game parameters\n   */ validateBaseParams(betAmount) {\n        if (typeof betAmount !== 'number' || betAmount <= 0) {\n            return false;\n        }\n        if (betAmount < this.config.minBet || betAmount > this.config.maxBet) {\n            return false;\n        }\n        return true;\n    }\n    /**\n   * Generate base game data common to all games\n   */ generateBaseGameData(userId, betAmount, clientSeed) {\n        return {\n            user_id: userId,\n            game_type: this.gameType,\n            bet_amount: betAmount,\n            current_multiplier: 1.0,\n            status: 'active',\n            server_seed: (0,_lib_crypto__WEBPACK_IMPORTED_MODULE_0__.generateServerSeed)(),\n            client_seed: clientSeed || (0,_lib_crypto__WEBPACK_IMPORTED_MODULE_0__.generateClientSeed)(),\n            profit: 0\n        };\n    }\n    /**\n   * Calculate profit based on bet amount and multiplier\n   */ calculateProfit(betAmount, multiplier) {\n        return betAmount * multiplier - betAmount;\n    }\n    /**\n   * Apply house edge to multiplier\n   */ applyHouseEdge(multiplier) {\n        return multiplier * (1 - this.config.houseEdge);\n    }\n    /**\n   * Validate that multiplier doesn't exceed maximum\n   */ validateMultiplier(multiplier) {\n        return multiplier <= this.config.maxMultiplier;\n    }\n    /**\n   * Generate provably fair hash for verification\n   */ generateGameHash(serverSeed, clientSeed, nonce = 0) {\n        const crypto = __webpack_require__(/*! crypto */ \"crypto\");\n        const combined = `${serverSeed}:${clientSeed}:${nonce}`;\n        return crypto.createHash('sha256').update(combined).digest('hex');\n    }\n    /**\n   * Generate random number from seeds (0-1)\n   */ generateRandomFromSeeds(serverSeed, clientSeed, nonce = 0) {\n        const hash = this.generateGameHash(serverSeed, clientSeed, nonce);\n        // Use first 8 characters of hash to generate number between 0-1\n        const hexValue = hash.substring(0, 8);\n        const intValue = parseInt(hexValue, 16);\n        return intValue / 0xffffffff;\n    }\n    /**\n   * Generate random integer within range using provably fair method\n   */ generateRandomInt(min, max, serverSeed, clientSeed, nonce = 0) {\n        const random = this.generateRandomFromSeeds(serverSeed, clientSeed, nonce);\n        return Math.floor(random * (max - min + 1)) + min;\n    }\n    /**\n   * Generate array of random integers (useful for mines, card games, etc.)\n   */ generateRandomArray(count, min, max, serverSeed, clientSeed, startNonce = 0) {\n        const result = [];\n        const range = max - min + 1;\n        for(let i = 0; i < count; i++){\n            const random = this.generateRandomFromSeeds(serverSeed, clientSeed, startNonce + i);\n            const value = Math.floor(random * range) + min;\n            result.push(value);\n        }\n        return result;\n    }\n    /**\n   * Shuffle array using Fisher-Yates algorithm with provably fair randomness\n   */ shuffleArray(array, serverSeed, clientSeed, startNonce = 0) {\n        const shuffled = [\n            ...array\n        ];\n        for(let i = shuffled.length - 1; i > 0; i--){\n            const random = this.generateRandomFromSeeds(serverSeed, clientSeed, startNonce + i);\n            const j = Math.floor(random * (i + 1));\n            [shuffled[i], shuffled[j]] = [\n                shuffled[j],\n                shuffled[i]\n            ];\n        }\n        return shuffled;\n    }\n    /**\n   * Validate game state belongs to user\n   */ validateGameOwnership(gameState, userId) {\n        return gameState.user_id === userId;\n    }\n    /**\n   * Check if game is in active state\n   */ isGameActive(gameState) {\n        return gameState.status === 'active';\n    }\n    /**\n   * Log game action for debugging/auditing\n   */ logGameAction(gameState, action, result) {\n        if (true) {\n            console.log(`[${this.gameType.toUpperCase()}] Game ${gameState.id}: ${action.type}`, {\n                payload: action.payload,\n                result,\n                timestamp: new Date().toISOString()\n            });\n        }\n    }\n}\n/**\n * Game action types enum for consistency\n */ var GameActionType = /*#__PURE__*/ function(GameActionType) {\n    GameActionType[\"START_GAME\"] = \"START_GAME\";\n    GameActionType[\"MAKE_MOVE\"] = \"MAKE_MOVE\";\n    GameActionType[\"CASH_OUT\"] = \"CASH_OUT\";\n    GameActionType[\"END_GAME\"] = \"END_GAME\";\n    GameActionType[\"CANCEL_GAME\"] = \"CANCEL_GAME\";\n    return GameActionType;\n}({});\n/**\n * Common game errors\n */ class GameError extends Error {\n    constructor(message, code, gameType){\n        super(message), this.code = code, this.gameType = gameType;\n        this.name = 'GameError';\n    }\n}\nclass InvalidGameParamsError extends GameError {\n    constructor(gameType, message = 'Invalid game parameters'){\n        super(message, 'INVALID_PARAMS', gameType);\n    }\n}\nclass GameNotActiveError extends GameError {\n    constructor(gameType, message = 'Game is not active'){\n        super(message, 'GAME_NOT_ACTIVE', gameType);\n    }\n}\nclass InsufficientFundsError extends GameError {\n    constructor(gameType, message = 'Insufficient funds'){\n        super(message, 'INSUFFICIENT_FUNDS', gameType);\n    }\n}\nclass GameActionError extends GameError {\n    constructor(message, gameType){\n        super(message, 'INVALID_ACTION', gameType);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/games/BaseGameProvider.ts\n");

/***/ }),

/***/ "./lib/games/GameFactory.ts":
/*!**********************************!*\
  !*** ./lib/games/GameFactory.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GameFactory: () => (/* binding */ GameFactory),\n/* harmony export */   gameFactory: () => (/* binding */ gameFactory)\n/* harmony export */ });\n/* harmony import */ var _registry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./registry */ \"./lib/games/registry.ts\");\n/* harmony import */ var _mines_MinesGameProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mines/MinesGameProvider */ \"./lib/games/mines/MinesGameProvider.ts\");\n/* harmony import */ var _dice_DiceGameProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./dice/DiceGameProvider */ \"./lib/games/dice/DiceGameProvider.ts\");\n/* harmony import */ var _crash_CrashGameProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./crash/CrashGameProvider */ \"./lib/games/crash/CrashGameProvider.ts\");\n/* harmony import */ var _PlaceholderGameProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./PlaceholderGameProvider */ \"./lib/games/PlaceholderGameProvider.ts\");\n\n\n\n\n\n/**\n * Game Factory - Creates and manages game instances\n */ class GameFactory {\n    constructor(){\n        this.initialized = false;\n    }\n    /**\n   * Get singleton instance\n   */ static getInstance() {\n        if (!GameFactory.instance) {\n            GameFactory.instance = new GameFactory();\n        }\n        return GameFactory.instance;\n    }\n    /**\n   * Initialize all game providers\n   */ async initialize() {\n        if (this.initialized) {\n            console.log('🎮 Game factory already initialized');\n            return;\n        }\n        console.log('🎮 Initializing game factory...');\n        try {\n            // Register all game providers\n            await this.registerAllGames();\n            this.initialized = true;\n            console.log('✅ Game factory initialized successfully');\n            // Log registry stats\n            const stats = _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getRegistryStats();\n            console.log(`📊 Registered ${stats.totalGames} games (${stats.activeGames} active)`);\n            console.log('📋 Games by category:', stats.gamesByCategory);\n        } catch (error) {\n            console.error('❌ Failed to initialize game factory:', error);\n            throw error;\n        }\n    }\n    /**\n   * Register all available game providers\n   */ async registerAllGames() {\n        const providers = [\n            new _mines_MinesGameProvider__WEBPACK_IMPORTED_MODULE_1__.MinesGameProvider(),\n            new _dice_DiceGameProvider__WEBPACK_IMPORTED_MODULE_2__.DiceGameProvider(),\n            new _crash_CrashGameProvider__WEBPACK_IMPORTED_MODULE_3__.CrashGameProvider(),\n            ...(0,_PlaceholderGameProvider__WEBPACK_IMPORTED_MODULE_4__.createPlaceholderProviders)()\n        ];\n        for (const provider of providers){\n            try {\n                _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.registerGame(provider.config, provider);\n            } catch (error) {\n                console.error(`Failed to register ${provider.gameType} game:`, error);\n            // Continue with other games even if one fails\n            }\n        }\n    }\n    /**\n   * Create a new game instance\n   */ async createGame(gameType, params) {\n        try {\n            if (!this.initialized) {\n                await this.initialize();\n            }\n            const provider = _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getGameProvider(gameType);\n            console.log(`🎮 GameFactory - Retrieved provider for ${gameType}:`, provider?.constructor.name);\n            if (!provider) {\n                return {\n                    success: false,\n                    error: `Game type '${gameType}' is not registered`\n                };\n            }\n            // Validate parameters\n            console.log(`🎮 GameFactory - Validating params for ${gameType}:`, params);\n            if (!provider.validateGameParams(params)) {\n                return {\n                    success: false,\n                    error: 'Invalid game parameters'\n                };\n            }\n            // Generate game data\n            console.log(`🎮 GameFactory - Calling generateGameData for ${gameType}`);\n            const gameData = provider.generateGameData(params);\n            console.log(`🎮 GameFactory - Generated game data:`, gameData);\n            return {\n                success: true,\n                game: gameData\n            };\n        } catch (error) {\n            console.error(`Error creating ${gameType} game:`, error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Process a game action\n   */ async processGameAction(gameType, gameState, actionType, payload) {\n        try {\n            if (!this.initialized) {\n                await this.initialize();\n            }\n            const provider = _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getGameProvider(gameType);\n            if (!provider) {\n                return {\n                    success: false,\n                    error: `Game type '${gameType}' is not registered`\n                };\n            }\n            const action = {\n                type: actionType,\n                payload\n            };\n            const updatedGameState = await provider.processGameAction(gameState, action);\n            return {\n                success: true,\n                gameState: updatedGameState\n            };\n        } catch (error) {\n            console.error(`Error processing ${gameType} action:`, error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Calculate multiplier for a game state\n   */ calculateMultiplier(gameType, gameState, params) {\n        if (!this.initialized) {\n            console.warn('Game factory not initialized, returning default multiplier');\n            return 1.0;\n        }\n        const provider = _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getGameProvider(gameType);\n        if (!provider) {\n            console.warn(`Game type '${gameType}' not found, returning default multiplier`);\n            return 1.0;\n        }\n        return provider.calculateMultiplier(gameState, params);\n    }\n    /**\n   * Get game configuration\n   */ getGameConfig(gameType) {\n        return _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getGameConfig(gameType);\n    }\n    /**\n   * Get all available games\n   */ getAllGames() {\n        return _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getAllGames();\n    }\n    /**\n   * Get games by category\n   */ getGamesByCategory(category) {\n        return _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getGamesByCategory(category);\n    }\n    /**\n   * Search games\n   */ searchGames(query) {\n        return _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.searchGames(query);\n    }\n    /**\n   * Check if game type is available\n   */ isGameAvailable(gameType) {\n        const config = _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getGameConfig(gameType);\n        return config?.isActive ?? false;\n    }\n    /**\n   * Get game provider (for advanced usage)\n   */ getGameProvider(gameType) {\n        return _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getGameProvider(gameType);\n    }\n    /**\n   * Reset factory (for testing)\n   */ reset() {\n        _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.clear();\n        this.initialized = false;\n        console.log('🔄 Game factory reset');\n    }\n    /**\n   * Get initialization status\n   */ isInitialized() {\n        return this.initialized;\n    }\n}\n// Export singleton instance\nconst gameFactory = GameFactory.getInstance();\n// Auto-initialize in production\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/games/GameFactory.ts\n");

/***/ }),

/***/ "./lib/games/PlaceholderGameProvider.ts":
/*!**********************************************!*\
  !*** ./lib/games/PlaceholderGameProvider.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PlaceholderGameProvider: () => (/* binding */ PlaceholderGameProvider),\n/* harmony export */   createPlaceholderProviders: () => (/* binding */ createPlaceholderProviders),\n/* harmony export */   placeholderGames: () => (/* binding */ placeholderGames)\n/* harmony export */ });\n/* harmony import */ var _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BaseGameProvider */ \"./lib/games/BaseGameProvider.ts\");\n\n/**\n * Placeholder Game Provider - For games that are not yet implemented\n * This allows us to show games in the lobby while they're under development\n */ class PlaceholderGameProvider extends _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.BaseGameProvider {\n    constructor(config){\n        super();\n        this.gameType = config.id;\n        this.config = config;\n    }\n    /**\n   * Validate game parameters (placeholder implementation)\n   */ validateGameParams(params) {\n        // Basic validation for placeholder games\n        return params && typeof params.betAmount === 'number' && params.betAmount > 0;\n    }\n    /**\n   * Calculate multiplier (placeholder implementation)\n   */ calculateMultiplier(gameState, params) {\n        return gameState.current_multiplier || 1.0;\n    }\n    /**\n   * Generate game data (placeholder implementation)\n   */ generateGameData(params) {\n        const baseData = this.generateBaseGameData(params.userId, params.betAmount, params.clientSeed);\n        return {\n            ...baseData,\n            game_type: this.gameType\n        };\n    }\n    /**\n   * Process game action (placeholder implementation)\n   */ async processGameAction(gameState, action) {\n        // Placeholder games don't actually process actions\n        // They just return the current state\n        return gameState;\n    }\n}\n// Create placeholder game configurations\nconst placeholderGames = [\n    // Crash game removed - using real CrashGameProvider instead\n    {\n        id: 'plinko',\n        name: 'Plinko',\n        description: 'Drop balls down the plinko board and watch them bounce into different multiplier slots. Pure luck and excitement!',\n        icon: '🏀',\n        category: 'originals',\n        minBet: 0.01,\n        maxBet: 1000,\n        houseEdge: 0.01,\n        maxMultiplier: 1000,\n        features: [\n            'Provably Fair',\n            'Multiple Risk Levels',\n            'Animated Gameplay'\n        ],\n        isActive: true,\n        isFeatured: true,\n        isNew: false\n    },\n    {\n        id: 'limbo',\n        name: 'Limbo',\n        description: 'Choose your target multiplier and see if you can reach it. The higher the target, the lower the chance!',\n        icon: '🎯',\n        category: 'originals',\n        minBet: 0.01,\n        maxBet: 1000,\n        houseEdge: 0.01,\n        maxMultiplier: 1000000,\n        features: [\n            'Provably Fair',\n            'Instant Results',\n            'Unlimited Multiplier'\n        ],\n        isActive: true,\n        isFeatured: false,\n        isNew: false\n    },\n    {\n        id: 'wheel',\n        name: 'Wheel',\n        description: 'Spin the wheel of fortune and win big! Choose your risk level and watch the wheel decide your fate.',\n        icon: '🎡',\n        category: 'originals',\n        minBet: 0.01,\n        maxBet: 1000,\n        houseEdge: 0.01,\n        maxMultiplier: 50,\n        features: [\n            'Provably Fair',\n            'Multiple Risk Levels',\n            'Visual Spinning'\n        ],\n        isActive: true,\n        isFeatured: false,\n        isNew: true\n    },\n    {\n        id: 'blackjack',\n        name: 'Blackjack',\n        description: 'Classic card game where you try to get as close to 21 as possible without going over. Beat the dealer!',\n        icon: '🃏',\n        category: 'table',\n        minBet: 0.01,\n        maxBet: 1000,\n        houseEdge: 0.005,\n        maxMultiplier: 3,\n        features: [\n            'Classic Rules',\n            'Strategy Based',\n            'Low House Edge'\n        ],\n        isActive: true,\n        isFeatured: false,\n        isNew: false\n    },\n    {\n        id: 'roulette',\n        name: 'Roulette',\n        description: 'Place your bets on the roulette table and watch the ball spin. Red or black? Odd or even? Your choice!',\n        icon: '🎰',\n        category: 'table',\n        minBet: 0.01,\n        maxBet: 1000,\n        houseEdge: 0.027,\n        maxMultiplier: 36,\n        features: [\n            'European Rules',\n            'Multiple Bet Types',\n            'Live Animation'\n        ],\n        isActive: true,\n        isFeatured: false,\n        isNew: false\n    }\n];\n// Create placeholder providers for each game\nconst createPlaceholderProviders = ()=>{\n    return placeholderGames.map((config)=>new PlaceholderGameProvider(config));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/games/PlaceholderGameProvider.ts\n");

/***/ }),

/***/ "./lib/games/crash/CrashGameProvider.ts":
/*!**********************************************!*\
  !*** ./lib/games/crash/CrashGameProvider.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CrashGameProvider: () => (/* binding */ CrashGameProvider)\n/* harmony export */ });\n/* harmony import */ var _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../BaseGameProvider */ \"./lib/games/BaseGameProvider.ts\");\n\n/**\n * Crash Game Provider - Handles crash game logic\n */ class CrashGameProvider extends _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.BaseGameProvider {\n    /**\n   * Validate crash game parameters\n   */ validateGameParams(params) {\n        const { bet_amount, auto_cash_out } = params;\n        if (!this.validateBaseParams(bet_amount)) {\n            return false;\n        }\n        // Validate auto cash out if provided\n        if (auto_cash_out !== undefined) {\n            if (typeof auto_cash_out !== 'number' || auto_cash_out < 1.01) {\n                return false;\n            }\n            if (auto_cash_out > this.config.maxMultiplier) {\n                return false;\n            }\n        }\n        return true;\n    }\n    /**\n   * Calculate current multiplier based on time elapsed\n   */ calculateMultiplier(gameState, params) {\n        if (gameState.phase !== 'flying') {\n            return 1.0;\n        }\n        const timeElapsedMs = gameState.time_elapsed || 0;\n        // Scale time to create realistic multiplier growth\n        // Use a much smaller time scale for the exponential formula\n        const timeElapsed = timeElapsedMs / 100; // Scale down by 100x\n        // Use exponential growth: multiplier = 1.002^(scaled_time)\n        // This creates a smooth curve that accelerates over time\n        const multiplier = Math.pow(1.002, timeElapsed);\n        // Cap at crash point if it exists\n        if (gameState.crash_point && multiplier >= gameState.crash_point) {\n            return gameState.crash_point;\n        }\n        return Math.round(multiplier * 100) / 100; // Round to 2 decimal places\n    }\n    /**\n   * Generate crash game data\n   */ generateGameData(params) {\n        console.log('🎮 CrashGameProvider - generateGameData called with params:', params);\n        const { bet_amount, auto_cash_out, user_id, client_seed } = params;\n        const baseData = this.generateBaseGameData(user_id, bet_amount, client_seed);\n        console.log('🎮 CrashGameProvider - baseData generated:', baseData);\n        // Generate crash point using provably fair method\n        const crashPoint = this.generateCrashPoint(baseData.server_seed, baseData.client_seed);\n        console.log('🎮 CrashGameProvider - crashPoint generated:', crashPoint);\n        const result = {\n            ...baseData,\n            game_type: 'crash',\n            crash_point: crashPoint,\n            auto_cash_out: auto_cash_out,\n            phase: 'betting',\n            time_elapsed: 0,\n            cashed_out: false,\n            round_id: this.generateRoundId()\n        };\n        console.log('🎮 CrashGameProvider - final result:', result);\n        console.log('🎮 CrashGameProvider - result keys:', Object.keys(result));\n        return result;\n    }\n    /**\n   * Process crash game actions\n   */ async processGameAction(gameState, action) {\n        this.logGameAction(gameState, action);\n        switch(action.type){\n            case _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.GameActionType.START_GAME:\n                return this.handleStartGame(gameState, action.payload);\n            case _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.GameActionType.CASH_OUT:\n                return this.handleCashOut(gameState, action.payload);\n            case 'UPDATE_MULTIPLIER':\n                return this.handleUpdateMultiplier(gameState, action.payload);\n            case 'CRASH':\n                return this.handleCrash(gameState);\n            case 'START_ROUND':\n                return this.handleStartRound(gameState);\n            case 'END_ROUND':\n                return this.handleEndRound(gameState);\n            default:\n                throw new Error(`Unknown action type: ${action.type}`);\n        }\n    }\n    /**\n   * Handle starting the game (betting phase)\n   */ handleStartGame(gameState, payload) {\n        return {\n            ...gameState,\n            phase: 'betting',\n            status: 'active',\n            time_elapsed: 0,\n            current_multiplier: 1.0,\n            cashed_out: false\n        };\n    }\n    /**\n   * Handle cash out action\n   */ handleCashOut(gameState, payload) {\n        if (gameState.phase !== 'flying' || gameState.cashed_out) {\n            throw new Error('Cannot cash out at this time');\n        }\n        const currentMultiplier = this.calculateMultiplier(gameState);\n        const profit = this.calculateProfit(gameState.bet_amount, currentMultiplier);\n        return {\n            ...gameState,\n            cashed_out: true,\n            cash_out_at: currentMultiplier,\n            current_multiplier: currentMultiplier,\n            profit: profit,\n            status: 'cashed_out'\n        };\n    }\n    /**\n   * Handle multiplier update during flight\n   */ handleUpdateMultiplier(gameState, payload) {\n        if (gameState.phase !== 'flying') {\n            return gameState;\n        }\n        const newMultiplier = this.calculateMultiplier({\n            ...gameState,\n            time_elapsed: payload.timeElapsed\n        });\n        // Check if we've hit the crash point\n        if (gameState.crash_point && newMultiplier >= gameState.crash_point) {\n            return this.handleCrash({\n                ...gameState,\n                time_elapsed: payload.timeElapsed,\n                current_multiplier: gameState.crash_point\n            });\n        }\n        // Check for auto cash out\n        if (gameState.auto_cash_out && newMultiplier >= gameState.auto_cash_out && !gameState.cashed_out) {\n            return this.handleCashOut({\n                ...gameState,\n                time_elapsed: payload.timeElapsed,\n                current_multiplier: newMultiplier\n            }, {});\n        }\n        return {\n            ...gameState,\n            time_elapsed: payload.timeElapsed,\n            current_multiplier: newMultiplier\n        };\n    }\n    /**\n   * Handle crash event\n   */ handleCrash(gameState) {\n        const profit = gameState.cashed_out ? gameState.profit : -gameState.bet_amount;\n        return {\n            ...gameState,\n            phase: 'crashed',\n            status: gameState.cashed_out ? 'cashed_out' : 'lost',\n            profit: profit,\n            current_multiplier: gameState.crash_point || gameState.current_multiplier\n        };\n    }\n    /**\n   * Handle start of flying phase\n   */ handleStartRound(gameState) {\n        return {\n            ...gameState,\n            phase: 'flying',\n            time_elapsed: 0,\n            current_multiplier: 1.0\n        };\n    }\n    /**\n   * Handle end of round (waiting phase)\n   */ handleEndRound(gameState) {\n        return {\n            ...gameState,\n            phase: 'waiting'\n        };\n    }\n    /**\n   * Generate crash point using provably fair method\n   */ generateCrashPoint(serverSeed, clientSeed) {\n        // Generate a random number between 0 and 1\n        const random = this.generateRandomFromSeeds(serverSeed, clientSeed, 0);\n        // Use inverse exponential distribution to generate crash point\n        // This creates realistic crash points with most being low but some being very high\n        const houseEdge = this.config.houseEdge;\n        const crashPoint = Math.max(1.01, (1 - houseEdge) / random);\n        // Cap at maximum multiplier\n        return Math.min(crashPoint, this.config.maxMultiplier);\n    }\n    /**\n   * Generate unique round ID\n   */ generateRoundId() {\n        return `crash_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    }\n    constructor(...args){\n        super(...args), this.gameType = 'crash', this.config = {\n            id: 'crash',\n            name: 'Crash',\n            description: 'Watch the multiplier rise and cash out before it crashes! The longer you wait, the higher the multiplier, but if you wait too long, you lose everything.',\n            icon: '🚀',\n            category: 'originals',\n            minBet: 0.01,\n            maxBet: 1000,\n            houseEdge: 0.01,\n            maxMultiplier: 1000000,\n            features: [\n                'Provably Fair',\n                'Real-time',\n                'Auto Cash Out',\n                'Live Multiplier'\n            ],\n            isActive: true,\n            isNew: true,\n            isFeatured: true\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/games/crash/CrashGameProvider.ts\n");

/***/ }),

/***/ "./lib/games/dice/DiceGameProvider.ts":
/*!********************************************!*\
  !*** ./lib/games/dice/DiceGameProvider.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiceGameProvider: () => (/* binding */ DiceGameProvider)\n/* harmony export */ });\n/* harmony import */ var _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../BaseGameProvider */ \"./lib/games/BaseGameProvider.ts\");\n\n/**\n * Dice Game Provider - Implements the classic dice gambling game\n * Players bet on whether a dice roll (1-100) will be over or under their chosen target number\n */ class DiceGameProvider extends _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.BaseGameProvider {\n    /**\n   * Validate dice game parameters\n   */ validateGameParams(params) {\n        const { betAmount, targetNumber, rollUnder } = params;\n        // Validate base parameters\n        if (!this.validateBaseParams(betAmount)) {\n            return false;\n        }\n        // Validate target number\n        if (typeof targetNumber !== 'number' || targetNumber < this.MIN_TARGET || targetNumber > this.MAX_TARGET) {\n            return false;\n        }\n        // Validate roll direction\n        if (typeof rollUnder !== 'boolean') {\n            return false;\n        }\n        return true;\n    }\n    /**\n   * Calculate multiplier based on win chance\n   */ calculateMultiplier(gameState, params) {\n        const { target_number, roll_under } = gameState;\n        // Calculate win chance\n        const winChance = this.calculateWinChance(target_number, roll_under);\n        // Calculate multiplier: (100 - house_edge) / win_chance\n        const multiplier = (100 - this.config.houseEdge * 100) / winChance;\n        // Round to 4 decimal places and ensure minimum of 1.01\n        return Math.max(1.01, Math.round(multiplier * 10000) / 10000);\n    }\n    /**\n   * Calculate win chance percentage\n   */ calculateWinChance(targetNumber, rollUnder) {\n        if (rollUnder) {\n            // Win if roll < target (1 to target-1)\n            return targetNumber - 1;\n        } else {\n            // Win if roll > target (target+1 to 100)\n            return 100 - targetNumber;\n        }\n    }\n    /**\n   * Generate dice game data\n   */ generateGameData(params) {\n        const { userId, betAmount, targetNumber, rollUnder, clientSeed } = params;\n        if (!this.validateGameParams({\n            betAmount,\n            targetNumber,\n            rollUnder\n        })) {\n            throw new _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.InvalidGameParamsError(this.gameType);\n        }\n        const baseData = this.generateBaseGameData(userId, betAmount, clientSeed);\n        const multiplier = this.calculateMultiplier({\n            ...baseData,\n            game_type: 'dice',\n            target_number: targetNumber,\n            roll_under: rollUnder\n        });\n        return {\n            ...baseData,\n            game_type: 'dice',\n            target_number: targetNumber,\n            roll_under: rollUnder,\n            current_multiplier: multiplier,\n            result: undefined // Will be set when dice is rolled\n        };\n    }\n    /**\n   * Process dice game actions\n   */ async processGameAction(gameState, action) {\n        switch(action.type){\n            case _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.GameActionType.MAKE_MOVE:\n                return this.rollDice(gameState);\n            default:\n                throw new _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.GameActionError(`Unsupported action type: ${action.type}`, this.gameType);\n        }\n    }\n    /**\n   * Roll the dice and determine outcome\n   */ rollDice(gameState) {\n        if (gameState.status !== 'active') {\n            throw new _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.GameActionError('Game is not active', this.gameType);\n        }\n        if (gameState.result !== undefined) {\n            throw new _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.GameActionError('Dice already rolled', this.gameType);\n        }\n        // Generate dice result using provably fair method\n        const diceResult = this.generateRandomInt(this.DICE_MIN, this.DICE_MAX, gameState.server_seed, gameState.client_seed, 0 // nonce for dice roll\n        );\n        // Determine if player won\n        const won = this.checkWin(diceResult, gameState.target_number, gameState.roll_under);\n        // Calculate profit\n        const profit = won ? gameState.bet_amount * (gameState.current_multiplier - 1) : -gameState.bet_amount;\n        return {\n            ...gameState,\n            result: diceResult,\n            status: won ? 'won' : 'lost',\n            profit: profit,\n            updated_at: new Date().toISOString()\n        };\n    }\n    /**\n   * Check if the dice result is a win\n   */ checkWin(diceResult, targetNumber, rollUnder) {\n        if (rollUnder) {\n            return diceResult < targetNumber;\n        } else {\n            return diceResult > targetNumber;\n        }\n    }\n    /**\n   * Get game statistics for display\n   */ getGameStats(gameState) {\n        const winChance = this.calculateWinChance(gameState.target_number, gameState.roll_under);\n        return {\n            targetNumber: gameState.target_number,\n            rollUnder: gameState.roll_under,\n            winChance: winChance,\n            multiplier: gameState.current_multiplier,\n            result: gameState.result,\n            profit: gameState.profit,\n            status: gameState.status\n        };\n    }\n    constructor(...args){\n        super(...args), this.gameType = 'dice', this.config = {\n            id: 'dice',\n            name: 'Dice',\n            description: 'Roll the dice and predict if the result will be over or under your chosen number. Simple yet thrilling!',\n            icon: '🎲',\n            category: 'originals',\n            minBet: 0.01,\n            maxBet: 1000,\n            houseEdge: 0.01,\n            maxMultiplier: 9900,\n            features: [\n                'Provably Fair',\n                'Instant Play',\n                'Custom Multiplier'\n            ],\n            isActive: true,\n            isFeatured: true,\n            isNew: true\n        }, // Dice game constants\n        this.MIN_TARGET = 2, this.MAX_TARGET = 98, this.DICE_MIN = 1, this.DICE_MAX = 100;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/games/dice/DiceGameProvider.ts\n");

/***/ }),

/***/ "./lib/games/mines/MinesGameProvider.ts":
/*!**********************************************!*\
  !*** ./lib/games/mines/MinesGameProvider.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MinesGameProvider: () => (/* binding */ MinesGameProvider)\n/* harmony export */ });\n/* harmony import */ var _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../BaseGameProvider */ \"./lib/games/BaseGameProvider.ts\");\n\n/**\n * Mines Game Provider - Implements the classic minesweeper-style gambling game\n */ class MinesGameProvider extends _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.BaseGameProvider {\n    /**\n   * Validate mines game parameters\n   */ validateGameParams(params) {\n        const { betAmount, mineCount } = params;\n        // Validate base parameters\n        if (!this.validateBaseParams(betAmount)) {\n            return false;\n        }\n        // Validate mine count\n        if (typeof mineCount !== 'number' || mineCount < this.MIN_MINES || mineCount > this.MAX_MINES) {\n            return false;\n        }\n        return true;\n    }\n    /**\n   * Calculate multiplier based on mines and revealed safe cells\n   */ calculateMultiplier(gameState, params) {\n        const revealedSafeCells = params?.revealedCells ?? gameState.revealed_cells.length;\n        if (revealedSafeCells === 0) return 1.0;\n        const totalSafeCells = this.GRID_SIZE - gameState.mine_count;\n        const remainingSafeCells = totalSafeCells - revealedSafeCells;\n        if (remainingSafeCells <= 0) return 1.0;\n        // Calculate probability-based multiplier\n        let multiplier = 1.0;\n        for(let i = 0; i < revealedSafeCells; i++){\n            const safeCellsAtStep = totalSafeCells - i;\n            const totalCellsAtStep = this.GRID_SIZE - i;\n            const probability = safeCellsAtStep / totalCellsAtStep;\n            multiplier *= 1 / probability;\n        }\n        // Apply house edge\n        multiplier = this.applyHouseEdge(multiplier);\n        // Ensure multiplier doesn't exceed maximum\n        return Math.min(multiplier, this.config.maxMultiplier);\n    }\n    /**\n   * Generate mines game data\n   */ generateGameData(params) {\n        const { userId, betAmount, mineCount, clientSeed } = params;\n        if (!this.validateGameParams({\n            betAmount,\n            mineCount\n        })) {\n            throw new _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.InvalidGameParamsError(this.gameType);\n        }\n        const baseData = this.generateBaseGameData(userId, betAmount, clientSeed);\n        const minePositions = this.generateMinePositions(baseData.server_seed, baseData.client_seed, mineCount);\n        return {\n            ...baseData,\n            game_type: 'mines',\n            grid_size: this.GRID_SIZE,\n            mine_count: mineCount,\n            revealed_cells: [],\n            mine_positions: minePositions\n        };\n    }\n    /**\n   * Process game actions (reveal cell, cash out, etc.)\n   */ async processGameAction(gameState, action) {\n        this.logGameAction(gameState, action);\n        if (!this.isGameActive(gameState)) {\n            throw new _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.GameNotActiveError(this.gameType);\n        }\n        switch(action.type){\n            case _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.GameActionType.MAKE_MOVE:\n                return this.processRevealCell(gameState, action.payload.cellIndex);\n            case _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.GameActionType.CASH_OUT:\n                return this.processCashOut(gameState);\n            default:\n                throw new Error(`Unknown action type: ${action.type}`);\n        }\n    }\n    /**\n   * Process revealing a cell\n   */ processRevealCell(gameState, cellIndex) {\n        // Validate cell index\n        if (cellIndex < 0 || cellIndex >= this.GRID_SIZE) {\n            throw new Error('Invalid cell index');\n        }\n        // Check if cell is already revealed\n        if (gameState.revealed_cells.includes(cellIndex)) {\n            throw new Error('Cell already revealed');\n        }\n        const newRevealedCells = [\n            ...gameState.revealed_cells,\n            cellIndex\n        ];\n        const hitMine = gameState.mine_positions.includes(cellIndex);\n        if (hitMine) {\n            // Game over - hit mine\n            return {\n                ...gameState,\n                revealed_cells: newRevealedCells,\n                status: 'lost',\n                profit: -gameState.bet_amount,\n                current_multiplier: 0\n            };\n        } else {\n            // Safe cell revealed\n            const newMultiplier = this.calculateMultiplier(gameState, {\n                revealedCells: newRevealedCells.length\n            });\n            const profit = this.calculateProfit(gameState.bet_amount, newMultiplier);\n            // Check if all safe cells are revealed (auto win)\n            const totalSafeCells = this.GRID_SIZE - gameState.mine_count;\n            const isGameComplete = newRevealedCells.length === totalSafeCells;\n            return {\n                ...gameState,\n                revealed_cells: newRevealedCells,\n                current_multiplier: newMultiplier,\n                profit: profit,\n                status: isGameComplete ? 'won' : 'active'\n            };\n        }\n    }\n    /**\n   * Process cash out action\n   */ processCashOut(gameState) {\n        if (gameState.revealed_cells.length === 0) {\n            throw new Error('Cannot cash out without revealing any cells');\n        }\n        const profit = this.calculateProfit(gameState.bet_amount, gameState.current_multiplier);\n        return {\n            ...gameState,\n            status: 'cashed_out',\n            profit: profit\n        };\n    }\n    /**\n   * Generate mine positions using provably fair method\n   */ generateMinePositions(serverSeed, clientSeed, mineCount) {\n        const positions = [];\n        const availablePositions = Array.from({\n            length: this.GRID_SIZE\n        }, (_, i)=>i);\n        // Shuffle available positions using provably fair randomness\n        const shuffledPositions = this.shuffleArray(availablePositions, serverSeed, clientSeed);\n        // Take first mineCount positions\n        return shuffledPositions.slice(0, mineCount).sort((a, b)=>a - b);\n    }\n    /**\n   * Get safe cells remaining\n   */ getSafeCellsRemaining(gameState) {\n        const totalSafeCells = this.GRID_SIZE - gameState.mine_count;\n        return totalSafeCells - gameState.revealed_cells.length;\n    }\n    /**\n   * Get next multiplier if safe cell is revealed\n   */ getNextMultiplier(gameState) {\n        if (!this.isGameActive(gameState)) return gameState.current_multiplier;\n        const nextRevealedCount = gameState.revealed_cells.length + 1;\n        return this.calculateMultiplier(gameState, {\n            revealedCells: nextRevealedCount\n        });\n    }\n    /**\n   * Check if player can cash out\n   */ canCashOut(gameState) {\n        return this.isGameActive(gameState) && gameState.revealed_cells.length > 0;\n    }\n    constructor(...args){\n        super(...args), this.gameType = 'mines', this.config = {\n            id: 'mines',\n            name: 'Mines',\n            description: 'Click tiles to reveal gems while avoiding hidden mines. Cash out anytime to secure your winnings!',\n            icon: '💎',\n            category: 'originals',\n            minBet: 0.01,\n            maxBet: 1000,\n            houseEdge: 0.04,\n            maxMultiplier: 1000,\n            features: [\n                'Provably Fair',\n                'Instant Play',\n                'Auto Cashout',\n                'Custom Risk'\n            ],\n            isActive: true,\n            isFeatured: true,\n            isNew: false\n        }, this.GRID_SIZE = 25, this.MIN_MINES = 1, this.MAX_MINES = 24;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/games/mines/MinesGameProvider.ts\n");

/***/ }),

/***/ "./lib/games/registry.ts":
/*!*******************************!*\
  !*** ./lib/games/registry.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GameRegistry: () => (/* binding */ GameRegistry),\n/* harmony export */   gameRegistry: () => (/* binding */ gameRegistry)\n/* harmony export */ });\n/**\n * Game Registry - Central hub for all game configurations and providers\n */ class GameRegistry {\n    /**\n   * Register a new game with its configuration and provider\n   */ registerGame(config, provider) {\n        if (this.games.has(config.id)) {\n            console.warn(`Game ${config.id} is already registered. Overwriting...`);\n        }\n        this.games.set(config.id, config);\n        this.providers.set(config.id, provider);\n        console.log(`✅ Registered game: ${config.name} (${config.id})`);\n    }\n    /**\n   * Get game configuration by type\n   */ getGameConfig(gameType) {\n        return this.games.get(gameType);\n    }\n    /**\n   * Get game provider by type\n   */ getGameProvider(gameType) {\n        return this.providers.get(gameType);\n    }\n    /**\n   * Get all registered games\n   */ getAllGames() {\n        return Array.from(this.games.values());\n    }\n    /**\n   * Get games by category\n   */ getGamesByCategory(category) {\n        return this.getAllGames().filter((game)=>game.category === category);\n    }\n    /**\n   * Get active games only\n   */ getActiveGames() {\n        return this.getAllGames().filter((game)=>game.isActive);\n    }\n    /**\n   * Get featured games\n   */ getFeaturedGames() {\n        return this.getAllGames().filter((game)=>game.isFeatured && game.isActive);\n    }\n    /**\n   * Get new games\n   */ getNewGames() {\n        return this.getAllGames().filter((game)=>game.isNew && game.isActive);\n    }\n    /**\n   * Search games by name or description\n   */ searchGames(query) {\n        const lowercaseQuery = query.toLowerCase();\n        return this.getAllGames().filter((game)=>game.name.toLowerCase().includes(lowercaseQuery) || game.description.toLowerCase().includes(lowercaseQuery) || game.features.some((feature)=>feature.toLowerCase().includes(lowercaseQuery)));\n    }\n    /**\n   * Check if a game type is registered\n   */ isGameRegistered(gameType) {\n        return this.games.has(gameType);\n    }\n    /**\n   * Get game statistics\n   */ getRegistryStats() {\n        const allGames = this.getAllGames();\n        const activeGames = this.getActiveGames();\n        const gamesByCategory = allGames.reduce((acc, game)=>{\n            acc[game.category] = (acc[game.category] || 0) + 1;\n            return acc;\n        }, {});\n        return {\n            totalGames: allGames.length,\n            activeGames: activeGames.length,\n            gamesByCategory\n        };\n    }\n    /**\n   * Validate game configuration\n   */ validateGameConfig(config) {\n        const requiredFields = [\n            'id',\n            'name',\n            'description',\n            'category',\n            'minBet',\n            'maxBet'\n        ];\n        for (const field of requiredFields){\n            if (!(field in config)) {\n                console.error(`Game config missing required field: ${field}`);\n                return false;\n            }\n        }\n        if (config.minBet >= config.maxBet) {\n            console.error('minBet must be less than maxBet');\n            return false;\n        }\n        if (config.houseEdge < 0 || config.houseEdge > 1) {\n            console.error('houseEdge must be between 0 and 1');\n            return false;\n        }\n        return true;\n    }\n    /**\n   * Unregister a game (for testing or maintenance)\n   */ unregisterGame(gameType) {\n        const hasGame = this.games.has(gameType);\n        this.games.delete(gameType);\n        this.providers.delete(gameType);\n        if (hasGame) {\n            console.log(`🗑️ Unregistered game: ${gameType}`);\n        }\n        return hasGame;\n    }\n    /**\n   * Clear all registered games (for testing)\n   */ clear() {\n        this.games.clear();\n        this.providers.clear();\n        console.log('🧹 Cleared all registered games');\n    }\n    constructor(){\n        this.games = new Map();\n        this.providers = new Map();\n    }\n}\n// Export singleton instance\nconst gameRegistry = new GameRegistry();\n// Export the class for testing\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/games/registry.ts\n");

/***/ }),

/***/ "./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_ENDPOINTS: () => (/* binding */ API_ENDPOINTS),\n/* harmony export */   GAME_CONFIG: () => (/* binding */ GAME_CONFIG),\n/* harmony export */   SessionStorage: () => (/* binding */ SessionStorage),\n/* harmony export */   calculateMultiplier: () => (/* binding */ calculateMultiplier),\n/* harmony export */   calculateProfit: () => (/* binding */ calculateProfit),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   generateClientSeed: () => (/* binding */ generateClientSeed),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidPassword: () => (/* binding */ isValidPassword),\n/* harmony export */   randomInt: () => (/* binding */ randomInt),\n/* harmony export */   shuffleArray: () => (/* binding */ shuffleArray),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   throttle: () => (/* binding */ throttle)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"clsx\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__]);\n([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n/**\n * Utility function to merge Tailwind CSS classes\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Format currency values with proper decimal places\n */ function formatCurrency(amount, currency = 'USDT', compact = false) {\n    // Handle null/undefined values\n    if (amount == null) {\n        const decimals = currency === 'USDT' ? 2 : 8;\n        return 0..toFixed(decimals);\n    }\n    if (compact && Math.abs(amount) >= 1000) {\n        return formatNumber(amount);\n    }\n    const decimals = currency === 'USDT' ? 2 : 8;\n    return amount.toFixed(decimals);\n}\n/**\n * Format large numbers with K, M, B suffixes\n */ function formatNumber(num) {\n    if (num >= 1e9) {\n        return (num / 1e9).toFixed(1) + 'B';\n    }\n    if (num >= 1e6) {\n        return (num / 1e6).toFixed(1) + 'M';\n    }\n    if (num >= 1e3) {\n        return (num / 1e3).toFixed(1) + 'K';\n    }\n    return num.toString();\n}\n/**\n * Generate a random string for client seeds\n */ function generateClientSeed() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n}\n/**\n * Validate email format\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Validate password strength\n */ function isValidPassword(password) {\n    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number\n    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$/;\n    return passwordRegex.test(password);\n}\n/**\n * Calculate multiplier based on mines and revealed cells\n */ function calculateMultiplier(mineCount, revealedCount, gridSize = 25) {\n    if (revealedCount === 0) return 1;\n    const safeCells = gridSize - mineCount;\n    const remainingSafeCells = safeCells - revealedCount;\n    if (remainingSafeCells <= 0) return 1;\n    // Base multiplier calculation with house edge\n    const baseMultiplier = safeCells / remainingSafeCells;\n    const houseEdge = 0.04; // 4% house edge\n    return Math.max(1, baseMultiplier * (1 - houseEdge));\n}\n/**\n * Calculate potential profit\n */ function calculateProfit(betAmount, multiplier) {\n    return betAmount * multiplier - betAmount;\n}\n/**\n * Debounce function for performance optimization\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * Throttle function for performance optimization\n */ function throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n/**\n * Sleep utility for async operations\n */ function sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n/**\n * Generate a random integer between min and max (inclusive)\n */ function randomInt(min, max) {\n    return Math.floor(Math.random() * (max - min + 1)) + min;\n}\n/**\n * Shuffle array using Fisher-Yates algorithm\n */ function shuffleArray(array) {\n    const shuffled = [\n        ...array\n    ];\n    for(let i = shuffled.length - 1; i > 0; i--){\n        const j = Math.floor(Math.random() * (i + 1));\n        [shuffled[i], shuffled[j]] = [\n            shuffled[j],\n            shuffled[i]\n        ];\n    }\n    return shuffled;\n}\n/**\n * Format date to readable string\n */ function formatDate(date) {\n    const d = new Date(date);\n    return d.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n    });\n}\n/**\n * Copy text to clipboard\n */ async function copyToClipboard(text) {\n    try {\n        await navigator.clipboard.writeText(text);\n        return true;\n    } catch (err) {\n        console.error('Failed to copy text: ', err);\n        return false;\n    }\n}\n/**\n * Game configuration constants\n */ const GAME_CONFIG = {\n    GRID_SIZE: 25,\n    MIN_MINES: 1,\n    MAX_MINES: 24,\n    MIN_BET: 0.01,\n    MAX_BET: 1000,\n    HOUSE_EDGE: 0.04,\n    BASE_MULTIPLIER: 1.0\n};\n/**\n * API endpoints\n */ const API_ENDPOINTS = {\n    AUTH: {\n        LOGIN: '/api/auth/login',\n        SIGNUP: '/api/auth/signup',\n        ME: '/api/auth/me',\n        LOGOUT: '/api/auth/logout'\n    },\n    GAME: {\n        START: '/api/game/start',\n        MOVE: '/api/game/move',\n        CASHOUT: '/api/game/cashout',\n        HISTORY: '/api/game/history',\n        ACTIVE: '/api/game/active',\n        CONFIG: '/api/game/config',\n        LIST: '/api/game/list',\n        STATS: '/api/game/stats'\n    },\n    // Legacy endpoints for backward compatibility\n    MINES: {\n        START: '/api/game/start',\n        PICK: '/api/game/move',\n        CASHOUT: '/api/game/cashout',\n        HISTORY: '/api/game/history'\n    },\n    WALLET: {\n        DEPOSIT: '/api/wallet/deposit',\n        WITHDRAW: '/api/wallet/withdraw',\n        BALANCE: '/api/wallet/balance'\n    }\n};\n/**\n * Session storage utilities for tracking current session stats\n */ const SessionStorage = {\n    SESSION_KEY: 'betoctave_session_stats',\n    /**\n   * Get current session data\n   */ getSession: ()=>{\n        if (true) return null;\n        try {\n            const sessionData = localStorage.getItem(SessionStorage.SESSION_KEY);\n            return sessionData ? JSON.parse(sessionData) : null;\n        } catch (error) {\n            console.error('Error reading session data:', error);\n            return null;\n        }\n    },\n    /**\n   * Initialize or reset session\n   */ resetSession: ()=>{\n        if (true) return;\n        const sessionData = {\n            startTime: new Date().toISOString(),\n            resetCount: (SessionStorage.getSession()?.resetCount || 0) + 1\n        };\n        try {\n            localStorage.setItem(SessionStorage.SESSION_KEY, JSON.stringify(sessionData));\n        } catch (error) {\n            console.error('Error saving session data:', error);\n        }\n    },\n    /**\n   * Get session start time\n   */ getSessionStartTime: ()=>{\n        const session = SessionStorage.getSession();\n        if (session?.startTime) {\n            return session.startTime;\n        }\n        // If no session exists, create one and return its start time\n        SessionStorage.resetSession();\n        return SessionStorage.getSession()?.startTime || new Date().toISOString();\n    },\n    /**\n   * Check if session exists\n   */ hasSession: ()=>{\n        return SessionStorage.getSession() !== null;\n    },\n    /**\n   * Initialize session if it doesn't exist\n   */ initializeSession: ()=>{\n        if (!SessionStorage.hasSession()) {\n            console.log('📊 Initializing new session');\n            SessionStorage.resetSession();\n        } else {\n            console.log('📊 Session already exists:', SessionStorage.getSession());\n        }\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/utils.ts\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./node_modules\\next\\dist\\pages\\_error.js */ \"./node_modules/next/dist/pages/_error.js\");\n/* harmony import */ var _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__]);\nprivate_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/_error\",\n        pathname: \"/_error\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_UniversalGameContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/UniversalGameContext */ \"./contexts/UniversalGameContext.tsx\");\n/* harmony import */ var _contexts_MinesGameContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/MinesGameContext */ \"./contexts/MinesGameContext.tsx\");\n/* harmony import */ var _contexts_DiceGameContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/DiceGameContext */ \"./contexts/DiceGameContext.tsx\");\n/* harmony import */ var _contexts_CrashGameContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/CrashGameContext */ \"./contexts/CrashGameContext.tsx\");\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/toast */ \"./components/ui/toast.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__, _contexts_UniversalGameContext__WEBPACK_IMPORTED_MODULE_4__, _contexts_MinesGameContext__WEBPACK_IMPORTED_MODULE_5__, _contexts_DiceGameContext__WEBPACK_IMPORTED_MODULE_6__, _contexts_CrashGameContext__WEBPACK_IMPORTED_MODULE_7__, _components_ui_toast__WEBPACK_IMPORTED_MODULE_8__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__, _contexts_UniversalGameContext__WEBPACK_IMPORTED_MODULE_4__, _contexts_MinesGameContext__WEBPACK_IMPORTED_MODULE_5__, _contexts_DiceGameContext__WEBPACK_IMPORTED_MODULE_6__, _contexts_CrashGameContext__WEBPACK_IMPORTED_MODULE_7__, _components_ui_toast__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"BetOctave - Provably Fair Crypto Gambling\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_app.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_app.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Experience the thrill of provably fair crypto gambling with multiple games, transparent mechanics, and instant payouts.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_app.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_app.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_app.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_8__.ToastProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_UniversalGameContext__WEBPACK_IMPORTED_MODULE_4__.UniversalGameProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_MinesGameContext__WEBPACK_IMPORTED_MODULE_5__.MinesGameProvider, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_DiceGameContext__WEBPACK_IMPORTED_MODULE_6__.DiceGameProvider, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_CrashGameContext__WEBPACK_IMPORTED_MODULE_7__.CrashGameProvider, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                                        ...pageProps\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_app.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_app.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_app.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_app.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_app.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_app.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_app.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"BetOctave - Provably Fair Crypto Gambling Platform\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_document.tsx\",\n                        lineNumber: 7,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"bg-background text-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_document.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_document.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_document.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fZG9jdW1lbnQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE2RDtBQUU5QyxTQUFTSTtJQUN0QixxQkFDRSw4REFBQ0osK0NBQUlBO1FBQUNLLE1BQUs7UUFBS0MsV0FBVTs7MEJBQ3hCLDhEQUFDTCwrQ0FBSUE7O2tDQUNILDhEQUFDTTt3QkFBS0MsTUFBSzt3QkFBY0MsU0FBUTs7Ozs7O2tDQUNqQyw4REFBQ0M7d0JBQUtDLEtBQUk7d0JBQU9DLE1BQUs7Ozs7Ozs7Ozs7OzswQkFFeEIsOERBQUNDO2dCQUFLUCxXQUFVOztrQ0FDZCw4REFBQ0osK0NBQUlBOzs7OztrQ0FDTCw4REFBQ0MscURBQVVBOzs7Ozs7Ozs7Ozs7Ozs7OztBQUluQiIsInNvdXJjZXMiOlsiRTpcXDExMVxcUFJPSkVDVFxcbWluZXMtZ2FtZVxccGFnZXNcXF9kb2N1bWVudC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSHRtbCwgSGVhZCwgTWFpbiwgTmV4dFNjcmlwdCB9IGZyb20gJ25leHQvZG9jdW1lbnQnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBEb2N1bWVudCgpIHtcbiAgcmV0dXJuIChcbiAgICA8SHRtbCBsYW5nPVwiZW5cIiBjbGFzc05hbWU9XCJkYXJrXCI+XG4gICAgICA8SGVhZD5cbiAgICAgICAgPG1ldGEgbmFtZT1cImRlc2NyaXB0aW9uXCIgY29udGVudD1cIkJldE9jdGF2ZSAtIFByb3ZhYmx5IEZhaXIgQ3J5cHRvIEdhbWJsaW5nIFBsYXRmb3JtXCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIGhyZWY9XCIvZmF2aWNvbi5pY29cIiAvPlxuICAgICAgPC9IZWFkPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiYmctYmFja2dyb3VuZCB0ZXh0LWZvcmVncm91bmRcIj5cbiAgICAgICAgPE1haW4gLz5cbiAgICAgICAgPE5leHRTY3JpcHQgLz5cbiAgICAgIDwvYm9keT5cbiAgICA8L0h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiSHRtbCIsIkhlYWQiLCJNYWluIiwiTmV4dFNjcmlwdCIsIkRvY3VtZW50IiwibGFuZyIsImNsYXNzTmFtZSIsIm1ldGEiLCJuYW1lIiwiY29udGVudCIsImxpbmsiLCJyZWwiLCJocmVmIiwiYm9keSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./pages/_document.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "@radix-ui/react-toast":
/*!****************************************!*\
  !*** external "@radix-ui/react-toast" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-toast");;

/***/ }),

/***/ "__barrel_optimize__?names=X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************!*\
  !*** __barrel_optimize__?names=X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_0__["default"])
/* harmony export */ });
/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/x.js */ "./node_modules/lucide-react/dist/esm/icons/x.js");



/***/ }),

/***/ "bcryptjs?a1e7":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("bcryptjs");

/***/ }),

/***/ "class-variance-authority":
/*!*******************************************!*\
  !*** external "class-variance-authority" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("class-variance-authority");;

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = import("clsx");;

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "tailwind-merge":
/*!*********************************!*\
  !*** external "tailwind-merge" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("tailwind-merge");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("./webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();