import { NextApiRequest, NextApiResponse } from 'next';
import { withAuth } from '@/lib/auth';
import { gameDb, userDb, initDatabase } from '@/lib/database';
import { GameFactory } from '@/lib/games/GameFactory';

export default withAuth(async (req: NextApiRequest, res: NextApiResponse, user) => {
  // Initialize database
  initDatabase();

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    const { game_id, game_type } = req.body;

    console.log('💰 Cash out API - Request received:', { game_id, game_type, user_id: user.id });

    // Find the active game for this user
    let activeGame;
    if (game_id && typeof game_id === 'number') {
      activeGame = gameDb.findById(game_id);
    } else {
      // Find any active game for this user
      activeGame = gameDb.findActiveGameByUserId(user.id);
    }

    if (!activeGame) {
      return res.status(400).json({
        success: false,
        error: 'No active game found'
      });
    }

    console.log('💰 Cash out API - Found active game:', activeGame);

    // Verify the game belongs to this user
    if (activeGame.user_id !== user.id) {
      return res.status(403).json({
        success: false,
        error: 'Unauthorized'
      });
    }

    // Check if game is in a state that allows cash out
    if (activeGame.status !== 'active') {
      return res.status(400).json({
        success: false,
        error: 'Game is not active'
      });
    }

    // For crash games, calculate current multiplier and profit
    if (activeGame.game_type === 'crash') {
      // Parse game data to get crash point
      const gameData = JSON.parse(activeGame.game_data || '{}');
      const crashPoint = gameData.crash_point;

      // Calculate current multiplier based on time elapsed
      const currentTime = Date.now();
      const gameStartTime = new Date(activeGame.created_at).getTime();
      const timeElapsedMs = currentTime - gameStartTime;

      // Scale time to create realistic multiplier growth
      // Use a much smaller time scale for the exponential formula
      const timeElapsed = timeElapsedMs / 100; // Scale down by 100x

      // Use the same multiplier calculation as in the provider
      let currentMultiplier = Math.pow(1.002, timeElapsed);

      // Round to 2 decimal places like the provider does
      currentMultiplier = Math.round(currentMultiplier * 100) / 100;

      // Check if we've already crashed
      if (crashPoint && currentMultiplier >= crashPoint) {
        return res.status(400).json({
          success: false,
          error: `Game already crashed at ${crashPoint.toFixed(2)}x`
        });
      }

      const profit = activeGame.bet_amount * currentMultiplier - activeGame.bet_amount;

      console.log('💰 Cash out API - Crash game cash out:', {
        timeElapsedMs,
        timeElapsedScaled: timeElapsed,
        currentMultiplier: currentMultiplier.toFixed(2),
        crashPoint: crashPoint?.toFixed(2) || 'N/A',
        profit: profit.toFixed(2)
      });

      // Update game state
      const updatedGame = gameDb.update(activeGame.id, {
        status: 'cashed_out',
        current_multiplier: currentMultiplier,
        profit: profit,
        cash_out_at: currentMultiplier,
        cashed_out: true
      });

      // Update user balance
      userDb.addToBalance(user.id, 'USDT', profit);

      console.log('💰 Cash out API - Success:', {
        profit: profit.toFixed(2),
        multiplier: currentMultiplier.toFixed(2)
      });

      return res.status(200).json({
        success: true,
        profit: profit,
        multiplier: currentMultiplier,
        gameState: updatedGame,
        message: `Successfully cashed out at ${currentMultiplier.toFixed(2)}x!`
      });
    } else {
      // For other games, use the old logic (mines, dice)
      const { cashOut } = require('@/lib/game-logic');
      const result = await cashOut(activeGame.id, user.id);

      if (result.success) {
        return res.status(200).json({
          success: true,
          profit: result.profit,
          message: 'Successfully cashed out!'
        });
      } else {
        return res.status(400).json({
          success: false,
          error: result.error || 'Failed to cash out'
        });
      }
    }
  } catch (error) {
    console.error('💰 Cash out API error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});
