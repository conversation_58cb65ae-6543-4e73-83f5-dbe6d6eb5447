"use strict";exports.id=405,exports.ids=[405],exports.modules={3480:(e,E,t)=>{e.exports=t(5600)},3546:(e,E,t)=>{t.d(E,{C3:()=>d,DR:()=>R,Gy:()=>n,TS:()=>u,X_:()=>I,Ym:()=>A,dW:()=>N,s4:()=>_});var a=t(7550),s=t.n(a),T=t(3873),r=t.n(T);let i=null;function _(){if(i)return i;let e=r().join(process.cwd(),"data","mines.db");try{let E=t(9021),a=r().dirname(e);return E.existsSync(a)||E.mkdirSync(a,{recursive:!0}),(i=new(s())(e)).pragma("journal_mode = WAL"),i.pragma("synchronous = NORMAL"),i.pragma("cache_size = 1000000"),i.pragma("temp_store = memory"),function(){if(!i)throw Error("Database not initialized");if(i.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT UNIQUE NOT NULL,
      email TEXT UNIQUE NOT NULL,
      password_hash TEXT NOT NULL,
      usdt_balance REAL DEFAULT 0.0,
      ltc_balance REAL DEFAULT 0.0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `),i.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='games'").get()){let e=i.prepare("PRAGMA table_info(games)").all(),E=e.some(e=>["grid_size","mine_count","revealed_cells","mine_positions"].includes(e.name)),t=e.some(e=>"game_type"===e.name),a=e.some(e=>"game_data"===e.name);!E&&t&&a||(console.log("\uD83D\uDD04 Migrating games table to support multiple game types..."),function(){if(!i)throw Error("Database not initialized");try{if(i.prepare("PRAGMA table_info(games)").all().some(e=>["grid_size","mine_count","revealed_cells","mine_positions"].includes(e.name))){console.log("\uD83D\uDCE6 Migrating existing mines games..."),i.exec("PRAGMA foreign_keys = OFF");let e=i.prepare(`
        SELECT * FROM games
      `).all();for(let E of(console.log(`📋 Found ${e.length} existing games to migrate`),i.exec(`
        CREATE TABLE games_new (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER NOT NULL,
          game_type TEXT NOT NULL DEFAULT 'mines',
          bet_amount REAL NOT NULL,
          current_multiplier REAL DEFAULT 1.0,
          status TEXT CHECK(status IN ('active', 'won', 'lost', 'cashed_out', 'cancelled')) DEFAULT 'active',
          server_seed TEXT NOT NULL,
          client_seed TEXT NOT NULL,
          server_seed_hash TEXT DEFAULT '',
          profit REAL DEFAULT 0.0,
          game_data TEXT DEFAULT '{}',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `),e)){let e={grid_size:E.grid_size||25,mine_count:E.mine_count,revealed_cells:JSON.parse(E.revealed_cells||"[]"),mine_positions:JSON.parse(E.mine_positions||"[]")};i.prepare(`
          INSERT INTO games_new (
            id, user_id, game_type, bet_amount, current_multiplier,
            status, server_seed, client_seed, server_seed_hash, profit,
            game_data, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).run(E.id,E.user_id,"mines",E.bet_amount,E.current_multiplier,E.status,E.server_seed,E.client_seed,E.server_seed_hash||"",E.profit,JSON.stringify(e),E.created_at,E.updated_at)}i.exec("DROP TABLE games"),i.exec("ALTER TABLE games_new RENAME TO games"),i.exec(`
        CREATE INDEX IF NOT EXISTS idx_games_user_id ON games(user_id);
        CREATE INDEX IF NOT EXISTS idx_games_status ON games(status);
        CREATE INDEX IF NOT EXISTS idx_games_type ON games(game_type);
        CREATE INDEX IF NOT EXISTS idx_games_user_type ON games(user_id, game_type);
        CREATE INDEX IF NOT EXISTS idx_games_user_status ON games(user_id, status);
      `),i.exec(`
        CREATE TRIGGER IF NOT EXISTS update_games_timestamp
        AFTER UPDATE ON games
        BEGIN
          UPDATE games SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
        END;
      `),i.exec("PRAGMA foreign_keys = ON"),console.log(`✅ Migrated ${e.length} mines games to new schema`)}console.log("✅ Games table migration completed")}catch(e){throw console.error("❌ Games table migration failed:",e),e}}())}else console.log("\uD83D\uDCCB Creating new games table with multi-game support..."),i.exec(`
      CREATE TABLE games (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        game_type TEXT NOT NULL DEFAULT 'mines',
        bet_amount REAL NOT NULL,
        current_multiplier REAL DEFAULT 1.0,
        status TEXT CHECK(status IN ('active', 'won', 'lost', 'cashed_out', 'cancelled')) DEFAULT 'active',
        server_seed TEXT NOT NULL,
        client_seed TEXT NOT NULL,
        server_seed_hash TEXT DEFAULT '',
        profit REAL DEFAULT 0.0,
        game_data TEXT DEFAULT '{}',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);i.exec(`
    CREATE TABLE IF NOT EXISTS transactions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      game_id INTEGER,
      type TEXT CHECK(type IN ('deposit', 'withdraw', 'bet', 'win')) NOT NULL,
      currency TEXT CHECK(currency IN ('USDT', 'LTC')) NOT NULL,
      amount REAL NOT NULL,
      status TEXT CHECK(status IN ('pending', 'completed', 'failed')) DEFAULT 'pending',
      transaction_hash TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id),
      FOREIGN KEY (game_id) REFERENCES games (id)
    )
  `),i.exec(`
    CREATE TABLE IF NOT EXISTS user_statistics (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      game_type TEXT NOT NULL,
      total_games INTEGER DEFAULT 0,
      total_wins INTEGER DEFAULT 0,
      total_losses INTEGER DEFAULT 0,
      total_wagered REAL DEFAULT 0.0,
      total_profit REAL DEFAULT 0.0,
      biggest_win REAL DEFAULT 0.0,
      biggest_loss REAL DEFAULT 0.0,
      highest_multiplier REAL DEFAULT 0.0,
      current_streak INTEGER DEFAULT 0,
      best_win_streak INTEGER DEFAULT 0,
      best_loss_streak INTEGER DEFAULT 0,
      last_played DATETIME,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id),
      UNIQUE(user_id, game_type)
    )
  `),i.exec(`
    CREATE TABLE IF NOT EXISTS leaderboards (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      username TEXT NOT NULL,
      game_type TEXT NOT NULL,
      category TEXT NOT NULL, -- 'profit', 'multiplier', 'streak', 'volume'
      value REAL NOT NULL,
      rank_position INTEGER,
      period TEXT NOT NULL, -- 'daily', 'weekly', 'monthly', 'all_time'
      period_start DATETIME NOT NULL,
      period_end DATETIME NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id),
      UNIQUE(user_id, game_type, category, period, period_start)
    )
  `),i.exec(`
    CREATE TABLE IF NOT EXISTS game_sessions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      session_start DATETIME DEFAULT CURRENT_TIMESTAMP,
      session_end DATETIME,
      total_games INTEGER DEFAULT 0,
      total_wagered REAL DEFAULT 0.0,
      total_profit REAL DEFAULT 0.0,
      games_won INTEGER DEFAULT 0,
      games_lost INTEGER DEFAULT 0,
      biggest_win REAL DEFAULT 0.0,
      biggest_loss REAL DEFAULT 0.0,
      is_active BOOLEAN DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id)
    )
  `),i.exec(`
    CREATE TABLE IF NOT EXISTS achievements (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      code TEXT UNIQUE NOT NULL,
      name TEXT NOT NULL,
      description TEXT NOT NULL,
      icon TEXT NOT NULL,
      category TEXT NOT NULL, -- 'wins', 'profit', 'streak', 'volume', 'special'
      requirement_type TEXT NOT NULL, -- 'count', 'value', 'streak'
      requirement_value REAL NOT NULL,
      reward_type TEXT, -- 'badge', 'bonus', 'title'
      reward_value REAL DEFAULT 0.0,
      is_active BOOLEAN DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `),i.exec(`
    CREATE TABLE IF NOT EXISTS user_achievements (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      achievement_id INTEGER NOT NULL,
      earned_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      progress REAL DEFAULT 0.0,
      is_completed BOOLEAN DEFAULT 0,
      FOREIGN KEY (user_id) REFERENCES users (id),
      FOREIGN KEY (achievement_id) REFERENCES achievements (id),
      UNIQUE(user_id, achievement_id)
    )
  `),i.exec(`
    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
    CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
    CREATE INDEX IF NOT EXISTS idx_games_user_id ON games(user_id);
    CREATE INDEX IF NOT EXISTS idx_games_status ON games(status);
    CREATE INDEX IF NOT EXISTS idx_games_type ON games(game_type);
    CREATE INDEX IF NOT EXISTS idx_games_user_type ON games(user_id, game_type);
    CREATE INDEX IF NOT EXISTS idx_games_user_status ON games(user_id, status);
    CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);
    CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type);

    -- Phase 2 indexes
    CREATE INDEX IF NOT EXISTS idx_user_statistics_user_game ON user_statistics(user_id, game_type);
    CREATE INDEX IF NOT EXISTS idx_leaderboards_game_category ON leaderboards(game_type, category);
    CREATE INDEX IF NOT EXISTS idx_leaderboards_period ON leaderboards(period, period_start);
    CREATE INDEX IF NOT EXISTS idx_leaderboards_rank ON leaderboards(rank_position);
    CREATE INDEX IF NOT EXISTS idx_game_sessions_user_active ON game_sessions(user_id, is_active);
    CREATE INDEX IF NOT EXISTS idx_game_sessions_start ON game_sessions(session_start);
    CREATE INDEX IF NOT EXISTS idx_achievements_category ON achievements(category);
    CREATE INDEX IF NOT EXISTS idx_user_achievements_user ON user_achievements(user_id);
    CREATE INDEX IF NOT EXISTS idx_user_achievements_completed ON user_achievements(is_completed);
  `),i.exec(`
    CREATE TRIGGER IF NOT EXISTS update_users_timestamp
    AFTER UPDATE ON users
    BEGIN
      UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
  `),i.exec(`
    CREATE TRIGGER IF NOT EXISTS update_games_timestamp
    AFTER UPDATE ON games
    BEGIN
      UPDATE games SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
  `),i.exec(`
    CREATE TRIGGER IF NOT EXISTS update_user_statistics_timestamp
    AFTER UPDATE ON user_statistics
    BEGIN
      UPDATE user_statistics SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
  `),i.exec(`
    CREATE TRIGGER IF NOT EXISTS update_leaderboards_timestamp
    AFTER UPDATE ON leaderboards
    BEGIN
      UPDATE leaderboards SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
  `),i.exec(`
    CREATE TRIGGER IF NOT EXISTS update_game_sessions_timestamp
    AFTER UPDATE ON game_sessions
    BEGIN
      UPDATE game_sessions SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
  `)}(),console.log("Database initialized successfully"),i}catch(e){throw console.error("Failed to initialize database:",e),e}}function d(){return i||_()}let n={create:(e,E,t)=>{let a=d().prepare(`
      INSERT INTO users (username, email, password_hash)
      VALUES (?, ?, ?)
    `).run(e,E,t);return n.findById(a.lastInsertRowid)},findById:e=>d().prepare("SELECT * FROM users WHERE id = ?").get(e),findByEmail:e=>d().prepare("SELECT * FROM users WHERE email = ?").get(e),findByUsername:e=>d().prepare("SELECT * FROM users WHERE username = ?").get(e),updateBalance:(e,E,t)=>d().prepare(`UPDATE users SET ${"USDT"===E?"usdt_balance":"ltc_balance"} = ? WHERE id = ?`).run(t,e).changes>0,addToBalance:(e,E,t)=>{let a=d(),s="USDT"===E?"usdt_balance":"ltc_balance";return a.prepare(`UPDATE users SET ${s} = ${s} + ? WHERE id = ?`).run(t,e).changes>0}},N={create:e=>{let E=d(),{game_type:t,user_id:a,bet_amount:s,current_multiplier:T,status:r,server_seed:i,client_seed:_,profit:n,...R}=e,u=E.prepare(`
      INSERT INTO games (
        user_id, game_type, bet_amount, current_multiplier,
        status, server_seed, client_seed, server_seed_hash, profit, game_data
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(a,t,s,T,r,i,_,"",n,JSON.stringify(R));return N.findById(u.lastInsertRowid)},findById:e=>{let E=d().prepare("SELECT * FROM games WHERE id = ?").get(e);return E?N.parseGameData(E):null},update:(e,E)=>{let t=d(),{game_type:a,user_id:s,bet_amount:T,current_multiplier:r,status:i,server_seed:_,client_seed:n,profit:R,...u}=E,A={};if(void 0!==T&&(A.bet_amount=T),void 0!==r&&(A.current_multiplier=r),void 0!==i&&(A.status=i),void 0!==R&&(A.profit=R),Object.keys(u).length>0){let E=N.findById(e);E&&(A.game_data=JSON.stringify({...N.extractGameSpecificData(E),...u}))}if(0===Object.keys(A).length)return!1;let I=Object.keys(A),o=I.map(e=>`${e} = ?`).join(", "),m=I.map(e=>A[e]);return t.prepare(`UPDATE games SET ${o} WHERE id = ?`).run(...m,e).changes>0},findActiveByUserId:e=>{let E=d().prepare("SELECT * FROM games WHERE user_id = ? AND status = ? ORDER BY created_at DESC LIMIT 1").get(e,"active");return E?N.parseGameData(E):null},findByUserId:(e,E=50,t)=>{let a=d(),s="SELECT * FROM games WHERE user_id = ?",T=[e];return t&&(s+=" AND game_type = ?",T.push(t)),s+=" ORDER BY created_at DESC LIMIT ?",T.push(E),a.prepare(s).all(...T).map(e=>N.parseGameData(e))},parseGameData:e=>{let E=JSON.parse(e.game_data||"{}");return{...e,...E}},extractGameSpecificData:e=>{let{id:E,user_id:t,game_type:a,bet_amount:s,current_multiplier:T,status:r,server_seed:i,client_seed:_,profit:d,created_at:n,updated_at:N,...R}=e;return R}},R={create:e=>{let E=d().prepare(`
      INSERT INTO transactions (user_id, game_id, type, currency, amount, status, transaction_hash)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `).run(e.user_id,e.game_id||null,e.type,e.currency,e.amount,e.status,e.transaction_hash||null);return R.findById(E.lastInsertRowid)},findById:e=>d().prepare("SELECT * FROM transactions WHERE id = ?").get(e),findByUserId:(e,E=50)=>d().prepare("SELECT * FROM transactions WHERE user_id = ? ORDER BY created_at DESC LIMIT ?").all(e,E),updateStatus:(e,E)=>d().prepare("UPDATE transactions SET status = ? WHERE id = ?").run(E,e).changes>0},u={create:(e,E)=>(d().prepare(`
      INSERT INTO user_statistics (user_id, game_type)
      VALUES (?, ?)
    `).run(e,E),u.findByUserAndGame(e,E)),findByUserAndGame:(e,E)=>d().prepare("SELECT * FROM user_statistics WHERE user_id = ? AND game_type = ?").get(e,E),findByUser:e=>d().prepare("SELECT * FROM user_statistics WHERE user_id = ?").all(e),updateStats:(e,E,t)=>{let a=d();u.findByUserAndGame(e,E)||u.create(e,E);let s=Object.keys(t).map(e=>`${e} = ?`).join(", "),T=Object.values(t);return a.prepare(`
      UPDATE user_statistics
      SET ${s}, last_played = CURRENT_TIMESTAMP
      WHERE user_id = ? AND game_type = ?
    `).run(...T,e,E).changes>0},incrementStats:(e,E,t)=>{let a=d();u.findByUserAndGame(e,E)||u.create(e,E);let s=Object.keys(t).map(e=>`${e} = ${e} + ?`).join(", "),T=Object.values(t);return a.prepare(`
      UPDATE user_statistics
      SET ${s}, last_played = CURRENT_TIMESTAMP
      WHERE user_id = ? AND game_type = ?
    `).run(...T,e,E).changes>0}},A={updateEntry:(e,E,t,a,s,T)=>{let r,i,_=d(),n=new Date;switch(T){case"daily":i=new Date((r=new Date(n.getFullYear(),n.getMonth(),n.getDate())).getTime()+864e5);break;case"weekly":let N=n.getDay();(r=new Date(n.getTime()-24*N*36e5)).setHours(0,0,0,0),i=new Date(r.getTime()+6048e5);break;case"monthly":r=new Date(n.getFullYear(),n.getMonth(),1),i=new Date(n.getFullYear(),n.getMonth()+1,1);break;default:r=new Date(2024,0,1),i=new Date(2099,11,31)}return _.prepare(`
      INSERT OR REPLACE INTO leaderboards
      (user_id, username, game_type, category, value, period, period_start, period_end)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `).run(e,E,t,a,s,T,r.toISOString(),i.toISOString()).changes>0},getLeaderboard:(e,E,t,a=10)=>d().prepare(`
      SELECT * FROM leaderboards
      WHERE game_type = ? AND category = ? AND period = ?
      ORDER BY value DESC
      LIMIT ?
    `).all(e,E,t,a),getUserRank:(e,E,t,a)=>{let s=d().prepare(`
      SELECT COUNT(*) + 1 as rank FROM leaderboards
      WHERE game_type = ? AND category = ? AND period = ? AND value > (
        SELECT COALESCE(value, 0) FROM leaderboards
        WHERE user_id = ? AND game_type = ? AND category = ? AND period = ?
      )
    `).get(E,t,a,e,E,t,a);return s?.rank||0}},I={startSession:e=>{let E=d();I.endActiveSession(e);let t=E.prepare(`
      INSERT INTO game_sessions (user_id)
      VALUES (?)
    `).run(e);return I.findById(t.lastInsertRowid)},findById:e=>d().prepare("SELECT * FROM game_sessions WHERE id = ?").get(e),findActiveSession:e=>d().prepare("SELECT * FROM game_sessions WHERE user_id = ? AND is_active = 1 ORDER BY session_start DESC LIMIT 1").get(e),updateSession:(e,E)=>{let t=d(),a=Object.keys(E).map(e=>`${e} = ?`).join(", "),s=Object.values(E);return t.prepare(`UPDATE game_sessions SET ${a} WHERE id = ?`).run(...s,e).changes>0},endActiveSession:e=>d().prepare(`
      UPDATE game_sessions
      SET is_active = 0, session_end = CURRENT_TIMESTAMP
      WHERE user_id = ? AND is_active = 1
    `).run(e).changes>0,getUserSessions:(e,E=20)=>d().prepare(`
      SELECT * FROM game_sessions
      WHERE user_id = ?
      ORDER BY session_start DESC
      LIMIT ?
    `).all(e,E)}},6435:(e,E)=>{Object.defineProperty(E,"M",{enumerable:!0,get:function(){return function e(E,t){return t in E?E[t]:"then"in E&&"function"==typeof E.then?E.then(E=>e(E,t)):"function"==typeof E&&"default"===t?E:void 0}}})},8667:(e,E)=>{Object.defineProperty(E,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})}};