[{"E:\\111\\PROJECT\\mines-game\\pages\\api\\auth\\login.ts": "1", "E:\\111\\PROJECT\\mines-game\\pages\\api\\auth\\logout.ts": "2", "E:\\111\\PROJECT\\mines-game\\pages\\api\\auth\\me.ts": "3", "E:\\111\\PROJECT\\mines-game\\pages\\api\\auth\\signup.ts": "4", "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\active.ts": "5", "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\cashout.ts": "6", "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\history.ts": "7", "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\list.ts": "8", "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\move.ts": "9", "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\pick.ts": "10", "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\reset-active.ts": "11", "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\reset-stats.ts": "12", "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\start.ts": "13", "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\stats.ts": "14", "E:\\111\\PROJECT\\mines-game\\pages\\api\\lobby\\stats.ts": "15", "E:\\111\\PROJECT\\mines-game\\pages\\api\\test\\add-balance.ts": "16", "E:\\111\\PROJECT\\mines-game\\pages\\api\\test\\phase2-db.ts": "17", "E:\\111\\PROJECT\\mines-game\\pages\\api\\test\\reset-active-games.ts": "18", "E:\\111\\PROJECT\\mines-game\\pages\\api\\wallet\\deposit.ts": "19", "E:\\111\\PROJECT\\mines-game\\pages\\api\\wallet\\withdraw.ts": "20", "E:\\111\\PROJECT\\mines-game\\pages\\game\\[gameType].tsx": "21", "E:\\111\\PROJECT\\mines-game\\pages\\index.tsx": "22", "E:\\111\\PROJECT\\mines-game\\pages\\lobby.tsx": "23", "E:\\111\\PROJECT\\mines-game\\pages\\login.tsx": "24", "E:\\111\\PROJECT\\mines-game\\pages\\signup.tsx": "25", "E:\\111\\PROJECT\\mines-game\\pages\\_app.tsx": "26", "E:\\111\\PROJECT\\mines-game\\pages\\_document.tsx": "27", "E:\\111\\PROJECT\\mines-game\\components\\game\\crash\\CrashControls.tsx": "28", "E:\\111\\PROJECT\\mines-game\\components\\game\\crash\\CrashDisplay.tsx": "29", "E:\\111\\PROJECT\\mines-game\\components\\game\\dice\\DiceControls.tsx": "30", "E:\\111\\PROJECT\\mines-game\\components\\game\\dice\\DiceDisplay.tsx": "31", "E:\\111\\PROJECT\\mines-game\\components\\game\\GameControls.tsx": "32", "E:\\111\\PROJECT\\mines-game\\components\\game\\GameGrid.tsx": "33", "E:\\111\\PROJECT\\mines-game\\components\\game\\GameHistory.tsx": "34", "E:\\111\\PROJECT\\mines-game\\components\\game\\GameMessage.tsx": "35", "E:\\111\\PROJECT\\mines-game\\components\\game\\LiveStats.tsx": "36", "E:\\111\\PROJECT\\mines-game\\components\\game\\ProfitLossChart.tsx": "37", "E:\\111\\PROJECT\\mines-game\\components\\ui\\badge.tsx": "38", "E:\\111\\PROJECT\\mines-game\\components\\ui\\button.tsx": "39", "E:\\111\\PROJECT\\mines-game\\components\\ui\\card.tsx": "40", "E:\\111\\PROJECT\\mines-game\\components\\ui\\dialog.tsx": "41", "E:\\111\\PROJECT\\mines-game\\components\\ui\\input.tsx": "42", "E:\\111\\PROJECT\\mines-game\\components\\ui\\select.tsx": "43", "E:\\111\\PROJECT\\mines-game\\components\\ui\\tabs.tsx": "44", "E:\\111\\PROJECT\\mines-game\\components\\ui\\toast.tsx": "45", "E:\\111\\PROJECT\\mines-game\\components\\ui\\toaster.tsx": "46", "E:\\111\\PROJECT\\mines-game\\components\\ui\\use-toast.ts": "47", "E:\\111\\PROJECT\\mines-game\\components\\wallet\\WalletModal.tsx": "48", "E:\\111\\PROJECT\\mines-game\\lib\\auth.ts": "49", "E:\\111\\PROJECT\\mines-game\\lib\\crypto.ts": "50", "E:\\111\\PROJECT\\mines-game\\lib\\database.ts": "51", "E:\\111\\PROJECT\\mines-game\\lib\\game-logic.ts": "52", "E:\\111\\PROJECT\\mines-game\\lib\\games\\BaseGameProvider.ts": "53", "E:\\111\\PROJECT\\mines-game\\lib\\games\\crash\\CrashGameProvider.ts": "54", "E:\\111\\PROJECT\\mines-game\\lib\\games\\dice\\DiceGameProvider.ts": "55", "E:\\111\\PROJECT\\mines-game\\lib\\games\\GameFactory.ts": "56", "E:\\111\\PROJECT\\mines-game\\lib\\games\\mines\\MinesGameProvider.ts": "57", "E:\\111\\PROJECT\\mines-game\\lib\\games\\PlaceholderGameProvider.ts": "58", "E:\\111\\PROJECT\\mines-game\\lib\\games\\registry.ts": "59", "E:\\111\\PROJECT\\mines-game\\lib\\sounds.ts": "60", "E:\\111\\PROJECT\\mines-game\\lib\\utils.ts": "61"}, {"size": 1516, "mtime": 1748129429420, "results": "62", "hashOfConfig": "63"}, {"size": 633, "mtime": 1748129429436, "results": "64", "hashOfConfig": "63"}, {"size": 963, "mtime": 1748129429451, "results": "65", "hashOfConfig": "63"}, {"size": 1894, "mtime": 1748129429436, "results": "66", "hashOfConfig": "63"}, {"size": 1277, "mtime": 1748179028035, "results": "67", "hashOfConfig": "63"}, {"size": 4535, "mtime": 1748807716497, "results": "68", "hashOfConfig": "63"}, {"size": 1474, "mtime": 1748179066188, "results": "69", "hashOfConfig": "63"}, {"size": 1349, "mtime": 1748178998967, "results": "70", "hashOfConfig": "63"}, {"size": 3813, "mtime": 1748178869064, "results": "71", "hashOfConfig": "63"}, {"size": 1672, "mtime": 1748165690016, "results": "72", "hashOfConfig": "63"}, {"size": 1344, "mtime": 1748240208584, "results": "73", "hashOfConfig": "63"}, {"size": 1112, "mtime": 1748285876067, "results": "74", "hashOfConfig": "63"}, {"size": 6599, "mtime": 1748807407959, "results": "75", "hashOfConfig": "63"}, {"size": 3611, "mtime": 1748286262163, "results": "76", "hashOfConfig": "63"}, {"size": 2957, "mtime": 1748322475932, "results": "77", "hashOfConfig": "63"}, {"size": 1058, "mtime": 1748805241578, "results": "78", "hashOfConfig": "63"}, {"size": 3099, "mtime": 1748286820923, "results": "79", "hashOfConfig": "63"}, {"size": 986, "mtime": 1748806826420, "results": "80", "hashOfConfig": "63"}, {"size": 2612, "mtime": 1748129429404, "results": "81", "hashOfConfig": "63"}, {"size": 2833, "mtime": 1748129429420, "results": "82", "hashOfConfig": "63"}, {"size": 21857, "mtime": 1748804720029, "results": "83", "hashOfConfig": "63"}, {"size": 7308, "mtime": 1748318203995, "results": "84", "hashOfConfig": "63"}, {"size": 20088, "mtime": 1748330067486, "results": "85", "hashOfConfig": "63"}, {"size": 5440, "mtime": 1748331152726, "results": "86", "hashOfConfig": "63"}, {"size": 9914, "mtime": 1748331170884, "results": "87", "hashOfConfig": "63"}, {"size": 1377, "mtime": 1748804619101, "results": "88", "hashOfConfig": "63"}, {"size": 449, "mtime": 1748177584797, "results": "89", "hashOfConfig": "63"}, {"size": 11147, "mtime": 1748805317368, "results": "90", "hashOfConfig": "63"}, {"size": 9710, "mtime": 1748807960183, "results": "91", "hashOfConfig": "63"}, {"size": 9383, "mtime": 1748330794912, "results": "92", "hashOfConfig": "63"}, {"size": 7733, "mtime": 1748323031712, "results": "93", "hashOfConfig": "63"}, {"size": 10643, "mtime": 1748282216660, "results": "94", "hashOfConfig": "63"}, {"size": 4305, "mtime": 1748282445527, "results": "95", "hashOfConfig": "63"}, {"size": 14830, "mtime": 1748362900491, "results": "96", "hashOfConfig": "63"}, {"size": 5072, "mtime": 1748804785499, "results": "97", "hashOfConfig": "63"}, {"size": 9535, "mtime": 1748285914397, "results": "98", "hashOfConfig": "63"}, {"size": 3669, "mtime": 1748283104239, "results": "99", "hashOfConfig": "63"}, {"size": 1128, "mtime": 1748804743653, "results": "100", "hashOfConfig": "63"}, {"size": 1835, "mtime": 1748129429248, "results": "101", "hashOfConfig": "63"}, {"size": 1877, "mtime": 1748129429264, "results": "102", "hashOfConfig": "63"}, {"size": 3835, "mtime": 1748129429279, "results": "103", "hashOfConfig": "63"}, {"size": 824, "mtime": 1748129429295, "results": "104", "hashOfConfig": "63"}, {"size": 5613, "mtime": 1748195415477, "results": "105", "hashOfConfig": "63"}, {"size": 1883, "mtime": 1748129429295, "results": "106", "hashOfConfig": "63"}, {"size": 5040, "mtime": 1748194096566, "results": "107", "hashOfConfig": "63"}, {"size": 780, "mtime": 1748193847531, "results": "108", "hashOfConfig": "63"}, {"size": 3970, "mtime": 1748193839279, "results": "109", "hashOfConfig": "63"}, {"size": 11050, "mtime": 1748129429295, "results": "110", "hashOfConfig": "63"}, {"size": 7754, "mtime": 1748129429389, "results": "111", "hashOfConfig": "63"}, {"size": 5509, "mtime": 1748129429342, "results": "112", "hashOfConfig": "63"}, {"size": 27915, "mtime": 1748286723493, "results": "113", "hashOfConfig": "63"}, {"size": 10084, "mtime": 1748181831031, "results": "114", "hashOfConfig": "63"}, {"size": 6222, "mtime": 1748322864575, "results": "115", "hashOfConfig": "63"}, {"size": 7856, "mtime": 1748807655439, "results": "116", "hashOfConfig": "63"}, {"size": 5754, "mtime": 1748330109468, "results": "117", "hashOfConfig": "63"}, {"size": 6610, "mtime": 1748807283515, "results": "118", "hashOfConfig": "63"}, {"size": 7161, "mtime": 1748178714317, "results": "119", "hashOfConfig": "63"}, {"size": 4110, "mtime": 1748807351020, "results": "120", "hashOfConfig": "63"}, {"size": 4261, "mtime": 1748178645838, "results": "121", "hashOfConfig": "63"}, {"size": 1278, "mtime": 1748129429358, "results": "122", "hashOfConfig": "63"}, {"size": 7195, "mtime": 1748807795896, "results": "123", "hashOfConfig": "63"}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ennnw8", {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 40, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\111\\PROJECT\\mines-game\\pages\\api\\auth\\login.ts", [], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\auth\\logout.ts", [], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\auth\\me.ts", ["307"], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\auth\\signup.ts", ["308"], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\active.ts", ["309"], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\cashout.ts", ["310", "311"], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\history.ts", [], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\list.ts", ["312"], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\move.ts", ["313", "314"], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\pick.ts", [], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\reset-active.ts", [], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\reset-stats.ts", ["315"], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\start.ts", ["316", "317"], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\stats.ts", [], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\lobby\\stats.ts", ["318", "319", "320"], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\test\\add-balance.ts", [], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\test\\phase2-db.ts", ["321", "322", "323", "324", "325", "326", "327", "328", "329"], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\test\\reset-active-games.ts", ["330", "331"], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\wallet\\deposit.ts", [], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\wallet\\withdraw.ts", [], [], "E:\\111\\PROJECT\\mines-game\\pages\\game\\[gameType].tsx", ["332", "333", "334", "335", "336", "337", "338", "339", "340", "341", "342", "343"], [], "E:\\111\\PROJECT\\mines-game\\pages\\index.tsx", ["344"], [], "E:\\111\\PROJECT\\mines-game\\pages\\lobby.tsx", ["345", "346", "347"], [], "E:\\111\\PROJECT\\mines-game\\pages\\login.tsx", ["348", "349"], [], "E:\\111\\PROJECT\\mines-game\\pages\\signup.tsx", ["350"], [], "E:\\111\\PROJECT\\mines-game\\pages\\_app.tsx", [], [], "E:\\111\\PROJECT\\mines-game\\pages\\_document.tsx", [], [], "E:\\111\\PROJECT\\mines-game\\components\\game\\crash\\CrashControls.tsx", ["351", "352", "353"], [], "E:\\111\\PROJECT\\mines-game\\components\\game\\crash\\CrashDisplay.tsx", ["354"], [], "E:\\111\\PROJECT\\mines-game\\components\\game\\dice\\DiceControls.tsx", ["355", "356", "357", "358", "359", "360", "361", "362"], [], "E:\\111\\PROJECT\\mines-game\\components\\game\\dice\\DiceDisplay.tsx", ["363", "364", "365", "366", "367", "368"], [], "E:\\111\\PROJECT\\mines-game\\components\\game\\GameControls.tsx", ["369", "370", "371", "372", "373", "374"], [], "E:\\111\\PROJECT\\mines-game\\components\\game\\GameGrid.tsx", ["375", "376", "377", "378", "379", "380", "381"], [], "E:\\111\\PROJECT\\mines-game\\components\\game\\GameHistory.tsx", ["382", "383", "384", "385", "386"], [], "E:\\111\\PROJECT\\mines-game\\components\\game\\GameMessage.tsx", [], [], "E:\\111\\PROJECT\\mines-game\\components\\game\\LiveStats.tsx", ["387", "388", "389"], [], "E:\\111\\PROJECT\\mines-game\\components\\game\\ProfitLossChart.tsx", ["390", "391", "392", "393"], [], "E:\\111\\PROJECT\\mines-game\\components\\ui\\badge.tsx", [], [], "E:\\111\\PROJECT\\mines-game\\components\\ui\\button.tsx", [], [], "E:\\111\\PROJECT\\mines-game\\components\\ui\\card.tsx", [], [], "E:\\111\\PROJECT\\mines-game\\components\\ui\\dialog.tsx", [], [], "E:\\111\\PROJECT\\mines-game\\components\\ui\\input.tsx", ["394"], [], "E:\\111\\PROJECT\\mines-game\\components\\ui\\select.tsx", [], [], "E:\\111\\PROJECT\\mines-game\\components\\ui\\tabs.tsx", [], [], "E:\\111\\PROJECT\\mines-game\\components\\ui\\toast.tsx", [], [], "E:\\111\\PROJECT\\mines-game\\components\\ui\\toaster.tsx", [], [], "E:\\111\\PROJECT\\mines-game\\components\\ui\\use-toast.ts", ["395", "396", "397", "398", "399"], [], "E:\\111\\PROJECT\\mines-game\\components\\wallet\\WalletModal.tsx", ["400", "401", "402"], [], "E:\\111\\PROJECT\\mines-game\\lib\\auth.ts", ["403", "404", "405", "406"], [], "E:\\111\\PROJECT\\mines-game\\lib\\crypto.ts", ["407", "408", "409", "410"], [], "E:\\111\\PROJECT\\mines-game\\lib\\database.ts", ["411", "412", "413", "414", "415", "416", "417", "418", "419", "420", "421", "422", "423", "424", "425", "426", "427", "428", "429", "430", "431", "432", "433", "434", "435", "436", "437", "438", "439", "440", "441", "442", "443", "444", "445", "446", "447", "448", "449", "450"], [], "E:\\111\\PROJECT\\mines-game\\lib\\game-logic.ts", ["451"], [], "E:\\111\\PROJECT\\mines-game\\lib\\games\\BaseGameProvider.ts", ["452", "453", "454", "455", "456"], [], "E:\\111\\PROJECT\\mines-game\\lib\\games\\crash\\CrashGameProvider.ts", ["457", "458", "459", "460", "461", "462", "463", "464"], [], "E:\\111\\PROJECT\\mines-game\\lib\\games\\dice\\DiceGameProvider.ts", ["465", "466"], [], "E:\\111\\PROJECT\\mines-game\\lib\\games\\GameFactory.ts", ["467", "468", "469", "470"], [], "E:\\111\\PROJECT\\mines-game\\lib\\games\\mines\\MinesGameProvider.ts", ["471"], [], "E:\\111\\PROJECT\\mines-game\\lib\\games\\PlaceholderGameProvider.ts", ["472", "473", "474", "475", "476"], [], "E:\\111\\PROJECT\\mines-game\\lib\\games\\registry.ts", [], [], "E:\\111\\PROJECT\\mines-game\\lib\\sounds.ts", [], [], "E:\\111\\PROJECT\\mines-game\\lib\\utils.ts", ["477", "478", "479", "480"], [], {"ruleId": "481", "severity": 1, "message": "482", "line": 27, "column": 13, "nodeType": null, "messageId": "483", "endLine": 27, "endColumn": 26}, {"ruleId": "484", "severity": 1, "message": "485", "line": 39, "column": 19, "nodeType": "486", "messageId": "487", "endLine": 39, "endColumn": 42}, {"ruleId": "488", "severity": 1, "message": "489", "line": 29, "column": 24, "nodeType": "490", "messageId": "491", "endLine": 29, "endColumn": 27, "suggestions": "492"}, {"ruleId": "481", "severity": 1, "message": "493", "line": 4, "column": 10, "nodeType": null, "messageId": "483", "endLine": 4, "endColumn": 21}, {"ruleId": "484", "severity": 1, "message": "485", "line": 121, "column": 27, "nodeType": "486", "messageId": "487", "endLine": 121, "endColumn": 54}, {"ruleId": "488", "severity": 1, "message": "489", "line": 22, "column": 58, "nodeType": "490", "messageId": "491", "endLine": 22, "endColumn": 61, "suggestions": "494"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 102, "column": 25, "nodeType": "490", "messageId": "491", "endLine": 102, "endColumn": 28, "suggestions": "495"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 122, "column": 45, "nodeType": "490", "messageId": "491", "endLine": 122, "endColumn": 48, "suggestions": "496"}, {"ruleId": "481", "severity": 1, "message": "497", "line": 3, "column": 24, "nodeType": null, "messageId": "483", "endLine": 3, "endColumn": 30}, {"ruleId": "488", "severity": 1, "message": "489", "line": 91, "column": 31, "nodeType": "490", "messageId": "491", "endLine": 91, "endColumn": 34, "suggestions": "498"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 138, "column": 56, "nodeType": "490", "messageId": "491", "endLine": 138, "endColumn": 59, "suggestions": "499"}, {"ruleId": "481", "severity": 1, "message": "500", "line": 5, "column": 75, "nodeType": null, "messageId": "483", "endLine": 5, "endColumn": 79}, {"ruleId": "488", "severity": 1, "message": "489", "line": 63, "column": 57, "nodeType": "490", "messageId": "491", "endLine": 63, "endColumn": 60, "suggestions": "501"}, {"ruleId": "481", "severity": 1, "message": "502", "line": 63, "column": 62, "nodeType": null, "messageId": "483", "endLine": 63, "endColumn": 67}, {"ruleId": "488", "severity": 1, "message": "489", "line": 26, "column": 28, "nodeType": "490", "messageId": "491", "endLine": 26, "endColumn": 31, "suggestions": "503"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 33, "column": 30, "nodeType": "490", "messageId": "491", "endLine": 33, "endColumn": 33, "suggestions": "504"}, {"ruleId": "481", "severity": 1, "message": "505", "line": 40, "column": 13, "nodeType": null, "messageId": "483", "endLine": 40, "endColumn": 21}, {"ruleId": "488", "severity": 1, "message": "489", "line": 43, "column": 21, "nodeType": "490", "messageId": "491", "endLine": 43, "endColumn": 24, "suggestions": "506"}, {"ruleId": "481", "severity": 1, "message": "507", "line": 51, "column": 13, "nodeType": null, "messageId": "483", "endLine": 51, "endColumn": 30}, {"ruleId": "488", "severity": 1, "message": "489", "line": 54, "column": 21, "nodeType": "490", "messageId": "491", "endLine": 54, "endColumn": 24, "suggestions": "508"}, {"ruleId": "481", "severity": 1, "message": "509", "line": 62, "column": 13, "nodeType": null, "messageId": "483", "endLine": 62, "endColumn": 20}, {"ruleId": "488", "severity": 1, "message": "489", "line": 65, "column": 21, "nodeType": "490", "messageId": "491", "endLine": 65, "endColumn": 24, "suggestions": "510"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 78, "column": 19, "nodeType": "490", "messageId": "491", "endLine": 78, "endColumn": 22, "suggestions": "511"}, {"ruleId": "481", "severity": 1, "message": "497", "line": 2, "column": 10, "nodeType": null, "messageId": "483", "endLine": 2, "endColumn": 16}, {"ruleId": "484", "severity": 1, "message": "485", "line": 13, "column": 16, "nodeType": "486", "messageId": "487", "endLine": 13, "endColumn": 41}, {"ruleId": "481", "severity": 1, "message": "512", "line": 8, "column": 29, "nodeType": null, "messageId": "483", "endLine": 8, "endColumn": 44}, {"ruleId": "481", "severity": 1, "message": "513", "line": 8, "column": 46, "nodeType": null, "messageId": "483", "endLine": 8, "endColumn": 56}, {"ruleId": "481", "severity": 1, "message": "514", "line": 8, "column": 58, "nodeType": null, "messageId": "483", "endLine": 8, "endColumn": 67}, {"ruleId": "481", "severity": 1, "message": "515", "line": 27, "column": 3, "nodeType": null, "messageId": "483", "endLine": 27, "endColumn": 7}, {"ruleId": "488", "severity": 1, "message": "489", "line": 60, "column": 48, "nodeType": "490", "messageId": "491", "endLine": 60, "endColumn": 51, "suggestions": "516"}, {"ruleId": "517", "severity": 1, "message": "518", "line": 90, "column": 6, "nodeType": "519", "endLine": 90, "endColumn": 16, "suggestions": "520"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 98, "column": 47, "nodeType": "490", "messageId": "491", "endLine": 98, "endColumn": 50, "suggestions": "521"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 216, "column": 41, "nodeType": "490", "messageId": "491", "endLine": 216, "endColumn": 44, "suggestions": "522"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 432, "column": 41, "nodeType": "490", "messageId": "491", "endLine": 432, "endColumn": 44, "suggestions": "523"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 448, "column": 41, "nodeType": "490", "messageId": "491", "endLine": 448, "endColumn": 44, "suggestions": "524"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 463, "column": 41, "nodeType": "490", "messageId": "491", "endLine": 463, "endColumn": 44, "suggestions": "525"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 498, "column": 43, "nodeType": "490", "messageId": "491", "endLine": 498, "endColumn": 46, "suggestions": "526"}, {"ruleId": "527", "severity": 1, "message": "528", "line": 168, "column": 67, "nodeType": "529", "messageId": "530", "suggestions": "531"}, {"ruleId": "481", "severity": 1, "message": "512", "line": 5, "column": 29, "nodeType": null, "messageId": "483", "endLine": 5, "endColumn": 44}, {"ruleId": "481", "severity": 1, "message": "532", "line": 7, "column": 16, "nodeType": null, "messageId": "483", "endLine": 7, "endColumn": 27}, {"ruleId": "517", "severity": 1, "message": "533", "line": 88, "column": 6, "nodeType": "519", "endLine": 88, "endColumn": 12, "suggestions": "534"}, {"ruleId": "481", "severity": 1, "message": "535", "line": 38, "column": 14, "nodeType": null, "messageId": "483", "endLine": 38, "endColumn": 17}, {"ruleId": "527", "severity": 1, "message": "528", "line": 133, "column": 20, "nodeType": "529", "messageId": "530", "suggestions": "536"}, {"ruleId": "481", "severity": 1, "message": "535", "line": 63, "column": 14, "nodeType": null, "messageId": "483", "endLine": 63, "endColumn": 17}, {"ruleId": "481", "severity": 1, "message": "537", "line": 5, "column": 10, "nodeType": null, "messageId": "483", "endLine": 5, "endColumn": 15}, {"ruleId": "481", "severity": 1, "message": "538", "line": 7, "column": 30, "nodeType": null, "messageId": "483", "endLine": 7, "endColumn": 35}, {"ruleId": "488", "severity": 1, "message": "489", "line": 20, "column": 24, "nodeType": "490", "messageId": "491", "endLine": 20, "endColumn": 27, "suggestions": "539"}, {"ruleId": "517", "severity": 1, "message": "540", "line": 173, "column": 6, "nodeType": "519", "endLine": 173, "endColumn": 48, "suggestions": "541"}, {"ruleId": "481", "severity": 1, "message": "542", "line": 1, "column": 27, "nodeType": null, "messageId": "483", "endLine": 1, "endColumn": 36}, {"ruleId": "481", "severity": 1, "message": "532", "line": 5, "column": 16, "nodeType": null, "messageId": "483", "endLine": 5, "endColumn": 27}, {"ruleId": "481", "severity": 1, "message": "543", "line": 8, "column": 10, "nodeType": null, "messageId": "483", "endLine": 8, "endColumn": 15}, {"ruleId": "481", "severity": 1, "message": "544", "line": 8, "column": 17, "nodeType": null, "messageId": "483", "endLine": 8, "endColumn": 22}, {"ruleId": "481", "severity": 1, "message": "545", "line": 8, "column": 24, "nodeType": null, "messageId": "483", "endLine": 8, "endColumn": 29}, {"ruleId": "481", "severity": 1, "message": "546", "line": 8, "column": 31, "nodeType": null, "messageId": "483", "endLine": 8, "endColumn": 36}, {"ruleId": "481", "severity": 1, "message": "547", "line": 8, "column": 38, "nodeType": null, "messageId": "483", "endLine": 8, "endColumn": 43}, {"ruleId": "481", "severity": 1, "message": "548", "line": 8, "column": 45, "nodeType": null, "messageId": "483", "endLine": 8, "endColumn": 50}, {"ruleId": "527", "severity": 1, "message": "549", "line": 185, "column": 28, "nodeType": "529", "messageId": "530", "suggestions": "550"}, {"ruleId": "527", "severity": 1, "message": "549", "line": 185, "column": 39, "nodeType": "529", "messageId": "530", "suggestions": "551"}, {"ruleId": "527", "severity": 1, "message": "549", "line": 185, "column": 44, "nodeType": "529", "messageId": "530", "suggestions": "552"}, {"ruleId": "527", "severity": 1, "message": "549", "line": 185, "column": 54, "nodeType": "529", "messageId": "530", "suggestions": "553"}, {"ruleId": "527", "severity": 1, "message": "549", "line": 186, "column": 27, "nodeType": "529", "messageId": "530", "suggestions": "554"}, {"ruleId": "527", "severity": 1, "message": "549", "line": 186, "column": 37, "nodeType": "529", "messageId": "530", "suggestions": "555"}, {"ruleId": "481", "severity": 1, "message": "513", "line": 5, "column": 29, "nodeType": null, "messageId": "483", "endLine": 5, "endColumn": 39}, {"ruleId": "481", "severity": 1, "message": "514", "line": 5, "column": 41, "nodeType": null, "messageId": "483", "endLine": 5, "endColumn": 50}, {"ruleId": "488", "severity": 1, "message": "489", "line": 32, "column": 39, "nodeType": "490", "messageId": "491", "endLine": 32, "endColumn": 42, "suggestions": "556"}, {"ruleId": "481", "severity": 1, "message": "557", "line": 48, "column": 9, "nodeType": null, "messageId": "483", "endLine": 48, "endColumn": 30}, {"ruleId": "481", "severity": 1, "message": "558", "line": 53, "column": 9, "nodeType": null, "messageId": "483", "endLine": 53, "endColumn": 24}, {"ruleId": "481", "severity": 1, "message": "559", "line": 54, "column": 9, "nodeType": null, "messageId": "483", "endLine": 54, "endColumn": 25}, {"ruleId": "488", "severity": 1, "message": "489", "line": 32, "column": 41, "nodeType": "490", "messageId": "491", "endLine": 32, "endColumn": 44, "suggestions": "560"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 52, "column": 41, "nodeType": "490", "messageId": "491", "endLine": 52, "endColumn": 44, "suggestions": "561"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 53, "column": 41, "nodeType": "490", "messageId": "491", "endLine": 53, "endColumn": 44, "suggestions": "562"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 121, "column": 41, "nodeType": "490", "messageId": "491", "endLine": 121, "endColumn": 44, "suggestions": "563"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 122, "column": 45, "nodeType": "490", "messageId": "491", "endLine": 122, "endColumn": 48, "suggestions": "564"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 123, "column": 52, "nodeType": "490", "messageId": "491", "endLine": 123, "endColumn": 55, "suggestions": "565"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 123, "column": 92, "nodeType": "490", "messageId": "491", "endLine": 123, "endColumn": 95, "suggestions": "566"}, {"ruleId": "481", "severity": 1, "message": "513", "line": 4, "column": 29, "nodeType": null, "messageId": "483", "endLine": 4, "endColumn": 39}, {"ruleId": "481", "severity": 1, "message": "514", "line": 4, "column": 41, "nodeType": null, "messageId": "483", "endLine": 4, "endColumn": 50}, {"ruleId": "488", "severity": 1, "message": "489", "line": 132, "column": 38, "nodeType": "490", "messageId": "491", "endLine": 132, "endColumn": 41, "suggestions": "567"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 133, "column": 38, "nodeType": "490", "messageId": "491", "endLine": 133, "endColumn": 41, "suggestions": "568"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 134, "column": 33, "nodeType": "490", "messageId": "491", "endLine": 134, "endColumn": 36, "suggestions": "569"}, {"ruleId": "481", "severity": 1, "message": "570", "line": 8, "column": 3, "nodeType": null, "messageId": "483", "endLine": 8, "endColumn": 13}, {"ruleId": "481", "severity": 1, "message": "571", "line": 9, "column": 3, "nodeType": null, "messageId": "483", "endLine": 9, "endColumn": 15}, {"ruleId": "481", "severity": 1, "message": "572", "line": 16, "column": 26, "nodeType": null, "messageId": "483", "endLine": 16, "endColumn": 39}, {"ruleId": "481", "severity": 1, "message": "573", "line": 31, "column": 45, "nodeType": null, "messageId": "483", "endLine": 31, "endColumn": 50}, {"ruleId": "488", "severity": 1, "message": "489", "line": 31, "column": 54, "nodeType": "490", "messageId": "491", "endLine": 31, "endColumn": 57, "suggestions": "574"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 53, "column": 39, "nodeType": "490", "messageId": "491", "endLine": 53, "endColumn": 42, "suggestions": "575"}, {"ruleId": "481", "severity": 1, "message": "502", "line": 53, "column": 44, "nodeType": null, "messageId": "483", "endLine": 53, "endColumn": 49}, {"ruleId": "576", "severity": 1, "message": "577", "line": 5, "column": 18, "nodeType": "578", "messageId": "579", "endLine": 5, "endColumn": 28, "suggestions": "580"}, {"ruleId": "481", "severity": 1, "message": "581", "line": 2, "column": 13, "nodeType": null, "messageId": "483", "endLine": 2, "endColumn": 28}, {"ruleId": "481", "severity": 1, "message": "582", "line": 3, "column": 10, "nodeType": null, "messageId": "483", "endLine": 3, "endColumn": 13}, {"ruleId": "481", "severity": 1, "message": "583", "line": 3, "column": 20, "nodeType": null, "messageId": "483", "endLine": 3, "endColumn": 32}, {"ruleId": "481", "severity": 1, "message": "584", "line": 5, "column": 10, "nodeType": null, "messageId": "483", "endLine": 5, "endColumn": 12}, {"ruleId": "481", "severity": 1, "message": "585", "line": 17, "column": 7, "nodeType": null, "messageId": "586", "endLine": 17, "endColumn": 18}, {"ruleId": "481", "severity": 1, "message": "587", "line": 8, "column": 56, "nodeType": null, "messageId": "483", "endLine": 8, "endColumn": 68}, {"ruleId": "481", "severity": 1, "message": "588", "line": 57, "column": 14, "nodeType": null, "messageId": "483", "endLine": 57, "endColumn": 19}, {"ruleId": "481", "severity": 1, "message": "588", "line": 102, "column": 14, "nodeType": null, "messageId": "483", "endLine": 102, "endColumn": 19}, {"ruleId": "488", "severity": 1, "message": "489", "line": 42, "column": 45, "nodeType": "490", "messageId": "491", "endLine": 42, "endColumn": 48, "suggestions": "589"}, {"ruleId": "481", "severity": 1, "message": "588", "line": 45, "column": 12, "nodeType": null, "messageId": "483", "endLine": 45, "endColumn": 17}, {"ruleId": "481", "severity": 1, "message": "482", "line": 191, "column": 13, "nodeType": null, "messageId": "483", "endLine": 191, "endColumn": 26}, {"ruleId": "481", "severity": 1, "message": "482", "line": 227, "column": 13, "nodeType": null, "messageId": "483", "endLine": 227, "endColumn": 26}, {"ruleId": "481", "severity": 1, "message": "590", "line": 59, "column": 13, "nodeType": null, "messageId": "483", "endLine": 59, "endColumn": 25}, {"ruleId": "484", "severity": 1, "message": "485", "line": 127, "column": 18, "nodeType": "486", "messageId": "487", "endLine": 127, "endColumn": 37}, {"ruleId": "484", "severity": 1, "message": "485", "line": 135, "column": 18, "nodeType": "486", "messageId": "487", "endLine": 135, "endColumn": 37}, {"ruleId": "481", "severity": 1, "message": "591", "line": 195, "column": 9, "nodeType": null, "messageId": "483", "endLine": 195, "endColumn": 11}, {"ruleId": "484", "severity": 1, "message": "485", "line": 18, "column": 16, "nodeType": "486", "messageId": "487", "endLine": 18, "endColumn": 29}, {"ruleId": "488", "severity": 1, "message": "489", "line": 68, "column": 48, "nodeType": "490", "messageId": "491", "endLine": 68, "endColumn": 51, "suggestions": "592"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 71, "column": 46, "nodeType": "490", "messageId": "491", "endLine": 71, "endColumn": 49, "suggestions": "593"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 72, "column": 46, "nodeType": "490", "messageId": "491", "endLine": 72, "endColumn": 49, "suggestions": "594"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 295, "column": 48, "nodeType": "490", "messageId": "491", "endLine": 295, "endColumn": 51, "suggestions": "595"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 505, "column": 34, "nodeType": "490", "messageId": "491", "endLine": 505, "endColumn": 37, "suggestions": "596"}, {"ruleId": "481", "severity": 1, "message": "597", "line": 516, "column": 13, "nodeType": null, "messageId": "483", "endLine": 516, "endColumn": 22}, {"ruleId": "481", "severity": 1, "message": "598", "line": 516, "column": 24, "nodeType": null, "messageId": "483", "endLine": 516, "endColumn": 31}, {"ruleId": "481", "severity": 1, "message": "599", "line": 516, "column": 73, "nodeType": null, "messageId": "483", "endLine": 516, "endColumn": 84}, {"ruleId": "481", "severity": 1, "message": "600", "line": 516, "column": 86, "nodeType": null, "messageId": "483", "endLine": 516, "endColumn": 97}, {"ruleId": "488", "severity": 1, "message": "489", "line": 518, "column": 23, "nodeType": "490", "messageId": "491", "endLine": 518, "endColumn": 26, "suggestions": "601"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 549, "column": 48, "nodeType": "490", "messageId": "491", "endLine": 549, "endColumn": 51, "suggestions": "602"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 559, "column": 19, "nodeType": "490", "messageId": "491", "endLine": 559, "endColumn": 22, "suggestions": "603"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 570, "column": 42, "nodeType": "490", "messageId": "491", "endLine": 570, "endColumn": 45, "suggestions": "604"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 576, "column": 27, "nodeType": "490", "messageId": "491", "endLine": 576, "endColumn": 30, "suggestions": "605"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 586, "column": 52, "nodeType": "490", "messageId": "491", "endLine": 586, "endColumn": 55, "suggestions": "606"}, {"ruleId": "481", "severity": 1, "message": "607", "line": 587, "column": 13, "nodeType": null, "messageId": "483", "endLine": 587, "endColumn": 15}, {"ruleId": "481", "severity": 1, "message": "598", "line": 587, "column": 17, "nodeType": null, "messageId": "483", "endLine": 587, "endColumn": 24}, {"ruleId": "481", "severity": 1, "message": "597", "line": 587, "column": 26, "nodeType": null, "messageId": "483", "endLine": 587, "endColumn": 35}, {"ruleId": "481", "severity": 1, "message": "608", "line": 587, "column": 37, "nodeType": null, "messageId": "483", "endLine": 587, "endColumn": 47}, {"ruleId": "481", "severity": 1, "message": "609", "line": 587, "column": 49, "nodeType": null, "messageId": "483", "endLine": 587, "endColumn": 67}, {"ruleId": "481", "severity": 1, "message": "610", "line": 587, "column": 69, "nodeType": null, "messageId": "483", "endLine": 587, "endColumn": 75}, {"ruleId": "481", "severity": 1, "message": "599", "line": 587, "column": 77, "nodeType": null, "messageId": "483", "endLine": 587, "endColumn": 88}, {"ruleId": "481", "severity": 1, "message": "600", "line": 587, "column": 90, "nodeType": null, "messageId": "483", "endLine": 587, "endColumn": 101}, {"ruleId": "481", "severity": 1, "message": "611", "line": 587, "column": 103, "nodeType": null, "messageId": "483", "endLine": 587, "endColumn": 109}, {"ruleId": "481", "severity": 1, "message": "612", "line": 587, "column": 111, "nodeType": null, "messageId": "483", "endLine": 587, "endColumn": 121}, {"ruleId": "481", "severity": 1, "message": "613", "line": 587, "column": 123, "nodeType": null, "messageId": "483", "endLine": 587, "endColumn": 133}, {"ruleId": "488", "severity": 1, "message": "489", "line": 640, "column": 47, "nodeType": "490", "messageId": "491", "endLine": 640, "endColumn": 50, "suggestions": "614"}, {"ruleId": "481", "severity": 1, "message": "615", "line": 647, "column": 11, "nodeType": null, "messageId": "483", "endLine": 647, "endColumn": 17}, {"ruleId": "488", "severity": 1, "message": "489", "line": 651, "column": 58, "nodeType": "490", "messageId": "491", "endLine": 651, "endColumn": 61, "suggestions": "616"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 657, "column": 33, "nodeType": "490", "messageId": "491", "endLine": 657, "endColumn": 36, "suggestions": "617"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 663, "column": 60, "nodeType": "490", "messageId": "491", "endLine": 663, "endColumn": 63, "suggestions": "618"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 684, "column": 66, "nodeType": "490", "messageId": "491", "endLine": 684, "endColumn": 69, "suggestions": "619"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 748, "column": 93, "nodeType": "490", "messageId": "491", "endLine": 748, "endColumn": 96, "suggestions": "620"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 770, "column": 96, "nodeType": "490", "messageId": "491", "endLine": 770, "endColumn": 99, "suggestions": "621"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 779, "column": 35, "nodeType": "490", "messageId": "491", "endLine": 779, "endColumn": 38, "suggestions": "622"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 794, "column": 27, "nodeType": "490", "messageId": "491", "endLine": 794, "endColumn": 30, "suggestions": "623"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 800, "column": 40, "nodeType": "490", "messageId": "491", "endLine": 800, "endColumn": 43, "suggestions": "624"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 806, "column": 47, "nodeType": "490", "messageId": "491", "endLine": 806, "endColumn": 50, "suggestions": "625"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 828, "column": 58, "nodeType": "490", "messageId": "491", "endLine": 828, "endColumn": 61, "suggestions": "626"}, {"ruleId": "481", "severity": 1, "message": "627", "line": 1, "column": 21, "nodeType": null, "messageId": "483", "endLine": 1, "endColumn": 25}, {"ruleId": "484", "severity": 1, "message": "485", "line": 68, "column": 20, "nodeType": "486", "messageId": "487", "endLine": 68, "endColumn": 37}, {"ruleId": "488", "severity": 1, "message": "489", "line": 140, "column": 70, "nodeType": "490", "messageId": "491", "endLine": 140, "endColumn": 73, "suggestions": "628"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 151, "column": 46, "nodeType": "490", "messageId": "491", "endLine": 151, "endColumn": 49, "suggestions": "629"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 152, "column": 62, "nodeType": "490", "messageId": "491", "endLine": 152, "endColumn": 65, "suggestions": "630"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 153, "column": 44, "nodeType": "490", "messageId": "491", "endLine": 153, "endColumn": 47, "suggestions": "631"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 29, "column": 37, "nodeType": "490", "messageId": "491", "endLine": 29, "endColumn": 40, "suggestions": "632"}, {"ruleId": "481", "severity": 1, "message": "633", "line": 52, "column": 57, "nodeType": null, "messageId": "483", "endLine": 52, "endColumn": 63}, {"ruleId": "488", "severity": 1, "message": "489", "line": 52, "column": 66, "nodeType": "490", "messageId": "491", "endLine": 52, "endColumn": 69, "suggestions": "634"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 78, "column": 35, "nodeType": "490", "messageId": "491", "endLine": 78, "endColumn": 38, "suggestions": "635"}, {"ruleId": "481", "severity": 1, "message": "636", "line": 140, "column": 54, "nodeType": null, "messageId": "483", "endLine": 140, "endColumn": 61}, {"ruleId": "488", "severity": 1, "message": "489", "line": 140, "column": 63, "nodeType": "490", "messageId": "491", "endLine": 140, "endColumn": 66, "suggestions": "637"}, {"ruleId": "481", "severity": 1, "message": "636", "line": 154, "column": 52, "nodeType": null, "messageId": "483", "endLine": 154, "endColumn": 59}, {"ruleId": "488", "severity": 1, "message": "489", "line": 154, "column": 61, "nodeType": "490", "messageId": "491", "endLine": 154, "endColumn": 64, "suggestions": "638"}, {"ruleId": "481", "severity": 1, "message": "633", "line": 66, "column": 56, "nodeType": null, "messageId": "483", "endLine": 66, "endColumn": 62}, {"ruleId": "488", "severity": 1, "message": "489", "line": 66, "column": 65, "nodeType": "490", "messageId": "491", "endLine": 66, "endColumn": 68, "suggestions": "639"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 82, "column": 13, "nodeType": "490", "messageId": "491", "endLine": 82, "endColumn": 16, "suggestions": "640"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 134, "column": 15, "nodeType": "490", "messageId": "491", "endLine": 134, "endColumn": 18, "suggestions": "641"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 172, "column": 14, "nodeType": "490", "messageId": "491", "endLine": 172, "endColumn": 17, "suggestions": "642"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 206, "column": 56, "nodeType": "490", "messageId": "491", "endLine": 206, "endColumn": 59, "suggestions": "643"}, {"ruleId": "481", "severity": 1, "message": "644", "line": 201, "column": 11, "nodeType": null, "messageId": "483", "endLine": 201, "endColumn": 20}, {"ruleId": "488", "severity": 1, "message": "489", "line": 21, "column": 37, "nodeType": "490", "messageId": "491", "endLine": 21, "endColumn": 40, "suggestions": "645"}, {"ruleId": "481", "severity": 1, "message": "633", "line": 29, "column": 52, "nodeType": null, "messageId": "483", "endLine": 29, "endColumn": 58}, {"ruleId": "488", "severity": 1, "message": "489", "line": 29, "column": 61, "nodeType": "490", "messageId": "491", "endLine": 29, "endColumn": 64, "suggestions": "646"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 36, "column": 35, "nodeType": "490", "messageId": "491", "endLine": 36, "endColumn": 38, "suggestions": "647"}, {"ruleId": "481", "severity": 1, "message": "648", "line": 48, "column": 56, "nodeType": null, "messageId": "483", "endLine": 48, "endColumn": 62}, {"ruleId": "488", "severity": 1, "message": "489", "line": 96, "column": 46, "nodeType": "490", "messageId": "491", "endLine": 96, "endColumn": 49, "suggestions": "649"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 96, "column": 56, "nodeType": "490", "messageId": "491", "endLine": 96, "endColumn": 59, "suggestions": "650"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 110, "column": 46, "nodeType": "490", "messageId": "491", "endLine": 110, "endColumn": 49, "suggestions": "651"}, {"ruleId": "488", "severity": 1, "message": "489", "line": 110, "column": 56, "nodeType": "490", "messageId": "491", "endLine": 110, "endColumn": 59, "suggestions": "652"}, "@typescript-eslint/no-unused-vars", "'password_hash' is assigned a value but never used.", "unusedVar", "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["653", "654"], "'GameFactory' is defined but never used.", ["655", "656"], ["657", "658"], ["659", "660"], "'gameDb' is defined but never used.", ["661", "662"], ["663", "664"], "'user' is defined but never used.", ["665", "666"], "'index' is defined but never used.", ["667", "668"], ["669", "670"], "'testStat' is assigned a value but never used.", ["671", "672"], "'leaderboardUpdate' is assigned a value but never used.", ["673", "674"], "'session' is assigned a value but never used.", ["675", "676"], ["677", "678"], "'CardDescription' is defined but never used.", "'CardHeader' is defined but never used.", "'CardTitle' is defined but never used.", "'Home' is defined but never used.", ["679", "680"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadGameConfig'. Either include it or remove the dependency array.", "ArrayExpression", ["681"], ["682", "683"], ["684", "685"], ["686", "687"], ["688", "689"], ["690", "691"], ["692", "693"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["694", "695", "696", "697"], "'TabsContent' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadLobbyData'. Either include it or remove the dependency array.", ["698"], "'err' is defined but never used.", ["699", "700", "701", "702"], "'Badge' is defined but never used.", "'Timer' is defined but never used.", ["703", "704"], "React Hook useEffect has a missing dependency: 'timeElapsed'. Either include it or remove the dependency array.", ["705"], "'useEffect' is defined but never used.", "'Dice1' is defined but never used.", "'Dice2' is defined but never used.", "'Dice3' is defined but never used.", "'Dice4' is defined but never used.", "'Dice5' is defined but never used.", "'Dice6' is defined but never used.", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["706", "707", "708", "709"], ["710", "711", "712", "713"], ["714", "715", "716", "717"], ["718", "719", "720", "721"], ["722", "723", "724", "725"], ["726", "727", "728", "729"], ["730", "731"], "'handleMineCountChange' is assigned a value but never used.", "'quickBetAmounts' is assigned a value but never used.", "'quickMineAmounts' is assigned a value but never used.", ["732", "733"], ["734", "735"], ["736", "737"], ["738", "739"], ["740", "741"], ["742", "743"], ["744", "745"], ["746", "747"], ["748", "749"], ["750", "751"], "'TrendingUp' is defined but never used.", "'TrendingDown' is defined but never used.", "'API_ENDPOINTS' is defined but never used.", "'label' is defined but never used.", ["752", "753"], ["754", "755"], "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["756"], "'ToastPrimitives' is defined but never used.", "'cva' is defined but never used.", "'VariantProps' is defined but never used.", "'cn' is defined but never used.", "'actionTypes' is assigned a value but only used as a type.", "usedOnlyAsType", "'ExternalLink' is defined but never used.", "'error' is defined but never used.", ["757", "758"], "'extendedHash' is assigned a value but never used.", "'iv' is assigned a value but never used.", ["759", "760"], ["761", "762"], ["763", "764"], ["765", "766"], ["767", "768"], "'game_type' is assigned a value but never used.", "'user_id' is assigned a value but never used.", "'server_seed' is assigned a value but never used.", "'client_seed' is assigned a value but never used.", ["769", "770"], ["771", "772"], ["773", "774"], ["775", "776"], ["777", "778"], ["779", "780"], "'id' is assigned a value but never used.", "'bet_amount' is assigned a value but never used.", "'current_multiplier' is assigned a value but never used.", "'status' is assigned a value but never used.", "'profit' is assigned a value but never used.", "'created_at' is assigned a value but never used.", "'updated_at' is assigned a value but never used.", ["781", "782"], "'result' is assigned a value but never used.", ["783", "784"], ["785", "786"], ["787", "788"], ["789", "790"], ["791", "792"], ["793", "794"], ["795", "796"], ["797", "798"], ["799", "800"], ["801", "802"], ["803", "804"], "'User' is defined but never used.", ["805", "806"], ["807", "808"], ["809", "810"], ["811", "812"], ["813", "814"], "'params' is defined but never used.", ["815", "816"], ["817", "818"], "'payload' is defined but never used.", ["819", "820"], ["821", "822"], ["823", "824"], ["825", "826"], ["827", "828"], ["829", "830"], ["831", "832"], "'positions' is assigned a value but never used.", ["833", "834"], ["835", "836"], ["837", "838"], "'action' is defined but never used.", ["839", "840"], ["841", "842"], ["843", "844"], ["845", "846"], {"messageId": "847", "fix": "848", "desc": "849"}, {"messageId": "850", "fix": "851", "desc": "852"}, {"messageId": "847", "fix": "853", "desc": "849"}, {"messageId": "850", "fix": "854", "desc": "852"}, {"messageId": "847", "fix": "855", "desc": "849"}, {"messageId": "850", "fix": "856", "desc": "852"}, {"messageId": "847", "fix": "857", "desc": "849"}, {"messageId": "850", "fix": "858", "desc": "852"}, {"messageId": "847", "fix": "859", "desc": "849"}, {"messageId": "850", "fix": "860", "desc": "852"}, {"messageId": "847", "fix": "861", "desc": "849"}, {"messageId": "850", "fix": "862", "desc": "852"}, {"messageId": "847", "fix": "863", "desc": "849"}, {"messageId": "850", "fix": "864", "desc": "852"}, {"messageId": "847", "fix": "865", "desc": "849"}, {"messageId": "850", "fix": "866", "desc": "852"}, {"messageId": "847", "fix": "867", "desc": "849"}, {"messageId": "850", "fix": "868", "desc": "852"}, {"messageId": "847", "fix": "869", "desc": "849"}, {"messageId": "850", "fix": "870", "desc": "852"}, {"messageId": "847", "fix": "871", "desc": "849"}, {"messageId": "850", "fix": "872", "desc": "852"}, {"messageId": "847", "fix": "873", "desc": "849"}, {"messageId": "850", "fix": "874", "desc": "852"}, {"messageId": "847", "fix": "875", "desc": "849"}, {"messageId": "850", "fix": "876", "desc": "852"}, {"messageId": "847", "fix": "877", "desc": "849"}, {"messageId": "850", "fix": "878", "desc": "852"}, {"desc": "879", "fix": "880"}, {"messageId": "847", "fix": "881", "desc": "849"}, {"messageId": "850", "fix": "882", "desc": "852"}, {"messageId": "847", "fix": "883", "desc": "849"}, {"messageId": "850", "fix": "884", "desc": "852"}, {"messageId": "847", "fix": "885", "desc": "849"}, {"messageId": "850", "fix": "886", "desc": "852"}, {"messageId": "847", "fix": "887", "desc": "849"}, {"messageId": "850", "fix": "888", "desc": "852"}, {"messageId": "847", "fix": "889", "desc": "849"}, {"messageId": "850", "fix": "890", "desc": "852"}, {"messageId": "847", "fix": "891", "desc": "849"}, {"messageId": "850", "fix": "892", "desc": "852"}, {"messageId": "893", "data": "894", "fix": "895", "desc": "896"}, {"messageId": "893", "data": "897", "fix": "898", "desc": "899"}, {"messageId": "893", "data": "900", "fix": "901", "desc": "902"}, {"messageId": "893", "data": "903", "fix": "904", "desc": "905"}, {"desc": "906", "fix": "907"}, {"messageId": "893", "data": "908", "fix": "909", "desc": "896"}, {"messageId": "893", "data": "910", "fix": "911", "desc": "899"}, {"messageId": "893", "data": "912", "fix": "913", "desc": "902"}, {"messageId": "893", "data": "914", "fix": "915", "desc": "905"}, {"messageId": "847", "fix": "916", "desc": "849"}, {"messageId": "850", "fix": "917", "desc": "852"}, {"desc": "918", "fix": "919"}, {"messageId": "893", "data": "920", "fix": "921", "desc": "922"}, {"messageId": "893", "data": "923", "fix": "924", "desc": "925"}, {"messageId": "893", "data": "926", "fix": "927", "desc": "928"}, {"messageId": "893", "data": "929", "fix": "930", "desc": "931"}, {"messageId": "893", "data": "932", "fix": "933", "desc": "922"}, {"messageId": "893", "data": "934", "fix": "935", "desc": "925"}, {"messageId": "893", "data": "936", "fix": "937", "desc": "928"}, {"messageId": "893", "data": "938", "fix": "939", "desc": "931"}, {"messageId": "893", "data": "940", "fix": "941", "desc": "922"}, {"messageId": "893", "data": "942", "fix": "943", "desc": "925"}, {"messageId": "893", "data": "944", "fix": "945", "desc": "928"}, {"messageId": "893", "data": "946", "fix": "947", "desc": "931"}, {"messageId": "893", "data": "948", "fix": "949", "desc": "922"}, {"messageId": "893", "data": "950", "fix": "951", "desc": "925"}, {"messageId": "893", "data": "952", "fix": "953", "desc": "928"}, {"messageId": "893", "data": "954", "fix": "955", "desc": "931"}, {"messageId": "893", "data": "956", "fix": "957", "desc": "922"}, {"messageId": "893", "data": "958", "fix": "959", "desc": "925"}, {"messageId": "893", "data": "960", "fix": "961", "desc": "928"}, {"messageId": "893", "data": "962", "fix": "963", "desc": "931"}, {"messageId": "893", "data": "964", "fix": "965", "desc": "922"}, {"messageId": "893", "data": "966", "fix": "967", "desc": "925"}, {"messageId": "893", "data": "968", "fix": "969", "desc": "928"}, {"messageId": "893", "data": "970", "fix": "971", "desc": "931"}, {"messageId": "847", "fix": "972", "desc": "849"}, {"messageId": "850", "fix": "973", "desc": "852"}, {"messageId": "847", "fix": "974", "desc": "849"}, {"messageId": "850", "fix": "975", "desc": "852"}, {"messageId": "847", "fix": "976", "desc": "849"}, {"messageId": "850", "fix": "977", "desc": "852"}, {"messageId": "847", "fix": "978", "desc": "849"}, {"messageId": "850", "fix": "979", "desc": "852"}, {"messageId": "847", "fix": "980", "desc": "849"}, {"messageId": "850", "fix": "981", "desc": "852"}, {"messageId": "847", "fix": "982", "desc": "849"}, {"messageId": "850", "fix": "983", "desc": "852"}, {"messageId": "847", "fix": "984", "desc": "849"}, {"messageId": "850", "fix": "985", "desc": "852"}, {"messageId": "847", "fix": "986", "desc": "849"}, {"messageId": "850", "fix": "987", "desc": "852"}, {"messageId": "847", "fix": "988", "desc": "849"}, {"messageId": "850", "fix": "989", "desc": "852"}, {"messageId": "847", "fix": "990", "desc": "849"}, {"messageId": "850", "fix": "991", "desc": "852"}, {"messageId": "847", "fix": "992", "desc": "849"}, {"messageId": "850", "fix": "993", "desc": "852"}, {"messageId": "847", "fix": "994", "desc": "849"}, {"messageId": "850", "fix": "995", "desc": "852"}, {"messageId": "847", "fix": "996", "desc": "849"}, {"messageId": "850", "fix": "997", "desc": "852"}, {"messageId": "998", "fix": "999", "desc": "1000"}, {"messageId": "847", "fix": "1001", "desc": "849"}, {"messageId": "850", "fix": "1002", "desc": "852"}, {"messageId": "847", "fix": "1003", "desc": "849"}, {"messageId": "850", "fix": "1004", "desc": "852"}, {"messageId": "847", "fix": "1005", "desc": "849"}, {"messageId": "850", "fix": "1006", "desc": "852"}, {"messageId": "847", "fix": "1007", "desc": "849"}, {"messageId": "850", "fix": "1008", "desc": "852"}, {"messageId": "847", "fix": "1009", "desc": "849"}, {"messageId": "850", "fix": "1010", "desc": "852"}, {"messageId": "847", "fix": "1011", "desc": "849"}, {"messageId": "850", "fix": "1012", "desc": "852"}, {"messageId": "847", "fix": "1013", "desc": "849"}, {"messageId": "850", "fix": "1014", "desc": "852"}, {"messageId": "847", "fix": "1015", "desc": "849"}, {"messageId": "850", "fix": "1016", "desc": "852"}, {"messageId": "847", "fix": "1017", "desc": "849"}, {"messageId": "850", "fix": "1018", "desc": "852"}, {"messageId": "847", "fix": "1019", "desc": "849"}, {"messageId": "850", "fix": "1020", "desc": "852"}, {"messageId": "847", "fix": "1021", "desc": "849"}, {"messageId": "850", "fix": "1022", "desc": "852"}, {"messageId": "847", "fix": "1023", "desc": "849"}, {"messageId": "850", "fix": "1024", "desc": "852"}, {"messageId": "847", "fix": "1025", "desc": "849"}, {"messageId": "850", "fix": "1026", "desc": "852"}, {"messageId": "847", "fix": "1027", "desc": "849"}, {"messageId": "850", "fix": "1028", "desc": "852"}, {"messageId": "847", "fix": "1029", "desc": "849"}, {"messageId": "850", "fix": "1030", "desc": "852"}, {"messageId": "847", "fix": "1031", "desc": "849"}, {"messageId": "850", "fix": "1032", "desc": "852"}, {"messageId": "847", "fix": "1033", "desc": "849"}, {"messageId": "850", "fix": "1034", "desc": "852"}, {"messageId": "847", "fix": "1035", "desc": "849"}, {"messageId": "850", "fix": "1036", "desc": "852"}, {"messageId": "847", "fix": "1037", "desc": "849"}, {"messageId": "850", "fix": "1038", "desc": "852"}, {"messageId": "847", "fix": "1039", "desc": "849"}, {"messageId": "850", "fix": "1040", "desc": "852"}, {"messageId": "847", "fix": "1041", "desc": "849"}, {"messageId": "850", "fix": "1042", "desc": "852"}, {"messageId": "847", "fix": "1043", "desc": "849"}, {"messageId": "850", "fix": "1044", "desc": "852"}, {"messageId": "847", "fix": "1045", "desc": "849"}, {"messageId": "850", "fix": "1046", "desc": "852"}, {"messageId": "847", "fix": "1047", "desc": "849"}, {"messageId": "850", "fix": "1048", "desc": "852"}, {"messageId": "847", "fix": "1049", "desc": "849"}, {"messageId": "850", "fix": "1050", "desc": "852"}, {"messageId": "847", "fix": "1051", "desc": "849"}, {"messageId": "850", "fix": "1052", "desc": "852"}, {"messageId": "847", "fix": "1053", "desc": "849"}, {"messageId": "850", "fix": "1054", "desc": "852"}, {"messageId": "847", "fix": "1055", "desc": "849"}, {"messageId": "850", "fix": "1056", "desc": "852"}, {"messageId": "847", "fix": "1057", "desc": "849"}, {"messageId": "850", "fix": "1058", "desc": "852"}, {"messageId": "847", "fix": "1059", "desc": "849"}, {"messageId": "850", "fix": "1060", "desc": "852"}, {"messageId": "847", "fix": "1061", "desc": "849"}, {"messageId": "850", "fix": "1062", "desc": "852"}, {"messageId": "847", "fix": "1063", "desc": "849"}, {"messageId": "850", "fix": "1064", "desc": "852"}, {"messageId": "847", "fix": "1065", "desc": "849"}, {"messageId": "850", "fix": "1066", "desc": "852"}, {"messageId": "847", "fix": "1067", "desc": "849"}, {"messageId": "850", "fix": "1068", "desc": "852"}, {"messageId": "847", "fix": "1069", "desc": "849"}, {"messageId": "850", "fix": "1070", "desc": "852"}, {"messageId": "847", "fix": "1071", "desc": "849"}, {"messageId": "850", "fix": "1072", "desc": "852"}, {"messageId": "847", "fix": "1073", "desc": "849"}, {"messageId": "850", "fix": "1074", "desc": "852"}, {"messageId": "847", "fix": "1075", "desc": "849"}, {"messageId": "850", "fix": "1076", "desc": "852"}, {"messageId": "847", "fix": "1077", "desc": "849"}, {"messageId": "850", "fix": "1078", "desc": "852"}, {"messageId": "847", "fix": "1079", "desc": "849"}, {"messageId": "850", "fix": "1080", "desc": "852"}, {"messageId": "847", "fix": "1081", "desc": "849"}, {"messageId": "850", "fix": "1082", "desc": "852"}, {"messageId": "847", "fix": "1083", "desc": "849"}, {"messageId": "850", "fix": "1084", "desc": "852"}, {"messageId": "847", "fix": "1085", "desc": "849"}, {"messageId": "850", "fix": "1086", "desc": "852"}, {"messageId": "847", "fix": "1087", "desc": "849"}, {"messageId": "850", "fix": "1088", "desc": "852"}, {"messageId": "847", "fix": "1089", "desc": "849"}, {"messageId": "850", "fix": "1090", "desc": "852"}, "suggestUnknown", {"range": "1091", "text": "1092"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1093", "text": "1094"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "1095", "text": "1092"}, {"range": "1096", "text": "1094"}, {"range": "1097", "text": "1092"}, {"range": "1098", "text": "1094"}, {"range": "1099", "text": "1092"}, {"range": "1100", "text": "1094"}, {"range": "1101", "text": "1092"}, {"range": "1102", "text": "1094"}, {"range": "1103", "text": "1092"}, {"range": "1104", "text": "1094"}, {"range": "1105", "text": "1092"}, {"range": "1106", "text": "1094"}, {"range": "1107", "text": "1092"}, {"range": "1108", "text": "1094"}, {"range": "1109", "text": "1092"}, {"range": "1110", "text": "1094"}, {"range": "1111", "text": "1092"}, {"range": "1112", "text": "1094"}, {"range": "1113", "text": "1092"}, {"range": "1114", "text": "1094"}, {"range": "1115", "text": "1092"}, {"range": "1116", "text": "1094"}, {"range": "1117", "text": "1092"}, {"range": "1118", "text": "1094"}, {"range": "1119", "text": "1092"}, {"range": "1120", "text": "1094"}, "Update the dependencies array to be: [gameType, loadGameConfig]", {"range": "1121", "text": "1122"}, {"range": "1123", "text": "1092"}, {"range": "1124", "text": "1094"}, {"range": "1125", "text": "1092"}, {"range": "1126", "text": "1094"}, {"range": "1127", "text": "1092"}, {"range": "1128", "text": "1094"}, {"range": "1129", "text": "1092"}, {"range": "1130", "text": "1094"}, {"range": "1131", "text": "1092"}, {"range": "1132", "text": "1094"}, {"range": "1133", "text": "1092"}, {"range": "1134", "text": "1094"}, "replaceWithAlt", {"alt": "1135"}, {"range": "1136", "text": "1137"}, "Replace with `&apos;`.", {"alt": "1138"}, {"range": "1139", "text": "1140"}, "Replace with `&lsquo;`.", {"alt": "1141"}, {"range": "1142", "text": "1143"}, "Replace with `&#39;`.", {"alt": "1144"}, {"range": "1145", "text": "1146"}, "Replace with `&rsquo;`.", "Update the dependencies array to be: [loadLobbyData, user]", {"range": "1147", "text": "1148"}, {"alt": "1135"}, {"range": "1149", "text": "1150"}, {"alt": "1138"}, {"range": "1151", "text": "1152"}, {"alt": "1141"}, {"range": "1153", "text": "1154"}, {"alt": "1144"}, {"range": "1155", "text": "1156"}, {"range": "1157", "text": "1092"}, {"range": "1158", "text": "1094"}, "Update the dependencies array to be: [currentMultiplier, roundPhase, gameState, timeElapsed]", {"range": "1159", "text": "1160"}, {"alt": "1161"}, {"range": "1162", "text": "1163"}, "Replace with `&quot;`.", {"alt": "1164"}, {"range": "1165", "text": "1166"}, "Replace with `&ldquo;`.", {"alt": "1167"}, {"range": "1168", "text": "1169"}, "Replace with `&#34;`.", {"alt": "1170"}, {"range": "1171", "text": "1172"}, "Replace with `&rdquo;`.", {"alt": "1161"}, {"range": "1173", "text": "1174"}, {"alt": "1164"}, {"range": "1175", "text": "1176"}, {"alt": "1167"}, {"range": "1177", "text": "1178"}, {"alt": "1170"}, {"range": "1179", "text": "1180"}, {"alt": "1161"}, {"range": "1181", "text": "1182"}, {"alt": "1164"}, {"range": "1183", "text": "1184"}, {"alt": "1167"}, {"range": "1185", "text": "1186"}, {"alt": "1170"}, {"range": "1187", "text": "1188"}, {"alt": "1161"}, {"range": "1189", "text": "1190"}, {"alt": "1164"}, {"range": "1191", "text": "1192"}, {"alt": "1167"}, {"range": "1193", "text": "1194"}, {"alt": "1170"}, {"range": "1195", "text": "1196"}, {"alt": "1161"}, {"range": "1197", "text": "1198"}, {"alt": "1164"}, {"range": "1199", "text": "1200"}, {"alt": "1167"}, {"range": "1201", "text": "1202"}, {"alt": "1170"}, {"range": "1203", "text": "1204"}, {"alt": "1161"}, {"range": "1205", "text": "1206"}, {"alt": "1164"}, {"range": "1207", "text": "1208"}, {"alt": "1167"}, {"range": "1209", "text": "1210"}, {"alt": "1170"}, {"range": "1211", "text": "1212"}, {"range": "1213", "text": "1092"}, {"range": "1214", "text": "1094"}, {"range": "1215", "text": "1092"}, {"range": "1216", "text": "1094"}, {"range": "1217", "text": "1092"}, {"range": "1218", "text": "1094"}, {"range": "1219", "text": "1092"}, {"range": "1220", "text": "1094"}, {"range": "1221", "text": "1092"}, {"range": "1222", "text": "1094"}, {"range": "1223", "text": "1092"}, {"range": "1224", "text": "1094"}, {"range": "1225", "text": "1092"}, {"range": "1226", "text": "1094"}, {"range": "1227", "text": "1092"}, {"range": "1228", "text": "1094"}, {"range": "1229", "text": "1092"}, {"range": "1230", "text": "1094"}, {"range": "1231", "text": "1092"}, {"range": "1232", "text": "1094"}, {"range": "1233", "text": "1092"}, {"range": "1234", "text": "1094"}, {"range": "1235", "text": "1092"}, {"range": "1236", "text": "1094"}, {"range": "1237", "text": "1092"}, {"range": "1238", "text": "1094"}, "replaceEmptyInterfaceWithSuper", {"range": "1239", "text": "1240"}, "Replace empty interface with a type alias.", {"range": "1241", "text": "1092"}, {"range": "1242", "text": "1094"}, {"range": "1243", "text": "1092"}, {"range": "1244", "text": "1094"}, {"range": "1245", "text": "1092"}, {"range": "1246", "text": "1094"}, {"range": "1247", "text": "1092"}, {"range": "1248", "text": "1094"}, {"range": "1249", "text": "1092"}, {"range": "1250", "text": "1094"}, {"range": "1251", "text": "1092"}, {"range": "1252", "text": "1094"}, {"range": "1253", "text": "1092"}, {"range": "1254", "text": "1094"}, {"range": "1255", "text": "1092"}, {"range": "1256", "text": "1094"}, {"range": "1257", "text": "1092"}, {"range": "1258", "text": "1094"}, {"range": "1259", "text": "1092"}, {"range": "1260", "text": "1094"}, {"range": "1261", "text": "1092"}, {"range": "1262", "text": "1094"}, {"range": "1263", "text": "1092"}, {"range": "1264", "text": "1094"}, {"range": "1265", "text": "1092"}, {"range": "1266", "text": "1094"}, {"range": "1267", "text": "1092"}, {"range": "1268", "text": "1094"}, {"range": "1269", "text": "1092"}, {"range": "1270", "text": "1094"}, {"range": "1271", "text": "1092"}, {"range": "1272", "text": "1094"}, {"range": "1273", "text": "1092"}, {"range": "1274", "text": "1094"}, {"range": "1275", "text": "1092"}, {"range": "1276", "text": "1094"}, {"range": "1277", "text": "1092"}, {"range": "1278", "text": "1094"}, {"range": "1279", "text": "1092"}, {"range": "1280", "text": "1094"}, {"range": "1281", "text": "1092"}, {"range": "1282", "text": "1094"}, {"range": "1283", "text": "1092"}, {"range": "1284", "text": "1094"}, {"range": "1285", "text": "1092"}, {"range": "1286", "text": "1094"}, {"range": "1287", "text": "1092"}, {"range": "1288", "text": "1094"}, {"range": "1289", "text": "1092"}, {"range": "1290", "text": "1094"}, {"range": "1291", "text": "1092"}, {"range": "1292", "text": "1094"}, {"range": "1293", "text": "1092"}, {"range": "1294", "text": "1094"}, {"range": "1295", "text": "1092"}, {"range": "1296", "text": "1094"}, {"range": "1297", "text": "1092"}, {"range": "1298", "text": "1094"}, {"range": "1299", "text": "1092"}, {"range": "1300", "text": "1094"}, {"range": "1301", "text": "1092"}, {"range": "1302", "text": "1094"}, {"range": "1303", "text": "1092"}, {"range": "1304", "text": "1094"}, {"range": "1305", "text": "1092"}, {"range": "1306", "text": "1094"}, {"range": "1307", "text": "1092"}, {"range": "1308", "text": "1094"}, {"range": "1309", "text": "1092"}, {"range": "1310", "text": "1094"}, {"range": "1311", "text": "1092"}, {"range": "1312", "text": "1094"}, {"range": "1313", "text": "1092"}, {"range": "1314", "text": "1094"}, {"range": "1315", "text": "1092"}, {"range": "1316", "text": "1094"}, {"range": "1317", "text": "1092"}, {"range": "1318", "text": "1094"}, {"range": "1319", "text": "1092"}, {"range": "1320", "text": "1094"}, {"range": "1321", "text": "1092"}, {"range": "1322", "text": "1094"}, {"range": "1323", "text": "1092"}, {"range": "1324", "text": "1094"}, {"range": "1325", "text": "1092"}, {"range": "1326", "text": "1094"}, {"range": "1327", "text": "1092"}, {"range": "1328", "text": "1094"}, {"range": "1329", "text": "1092"}, {"range": "1330", "text": "1094"}, [860, 863], "unknown", [860, 863], "never", [644, 647], [644, 647], [2714, 2717], [2714, 2717], [3452, 3455], [3452, 3455], [3852, 3855], [3852, 3855], [5806, 5809], [5806, 5809], [1775, 1778], [1775, 1778], [868, 871], [868, 871], [1039, 1042], [1039, 1042], [1405, 1408], [1405, 1408], [1957, 1960], [1957, 1960], [2448, 2451], [2448, 2451], [2873, 2876], [2873, 2876], [2482, 2485], [2482, 2485], [3176, 3186], "[gameType, loadGameConfig]", [3434, 3437], [3434, 3437], [6786, 6789], [6786, 6789], [14402, 14405], [14402, 14405], [15144, 15147], [15144, 15147], [15795, 15798], [15795, 15798], [17491, 17494], [17491, 17494], "&apos;", [6863, 6956], "\n                Cash out anytime to secure your winnings before it&apos;s too late\n              ", "&lsquo;", [6863, 6956], "\n                Cash out anytime to secure your winnings before it&lsquo;s too late\n              ", "&#39;", [6863, 6956], "\n                Cash out anytime to secure your winnings before it&#39;s too late\n              ", "&rsquo;", [6863, 6956], "\n                Cash out anytime to secure your winnings before it&rsquo;s too late\n              ", [2398, 2404], "[loadL<PERSON><PERSON><PERSON><PERSON>, user]", [4942, 4981], "\n                Don&apos;t have an account?", [4942, 4981], "\n                Don&lsquo;t have an account?", [4942, 4981], "\n                Don&#39;t have an account?", [4942, 4981], "\n                Don&rsquo;t have an account?", [801, 804], [801, 804], [5384, 5426], "[currentMultiplier, roundPhase, gameState, timeElapsed]", "&quot;", [6806, 6843], "2. <PERSON><PERSON> &quot;Roll Under\" or \"Roll Over\"", "&ldquo;", [6806, 6843], "2. <PERSON><PERSON> &ldquo;Roll Under\" or \"Roll Over\"", "&#34;", [6806, 6843], "2. <PERSON><PERSON> &#34;Roll Under\" or \"Roll Over\"", "&rdquo;", [6806, 6843], "2. <PERSON><PERSON> &rdquo;Roll Under\" or \"Roll Over\"", [6806, 6843], "2. <PERSON><PERSON> \"Roll Under&quot; or \"Roll Over\"", [6806, 6843], "2. <PERSON><PERSON> \"Roll Under&ldquo; or \"Roll Over\"", [6806, 6843], "2. <PERSON><PERSON> \"Roll Under&#34; or \"Roll Over\"", [6806, 6843], "2. <PERSON><PERSON> \"Roll Under&rdquo; or \"Roll Over\"", [6806, 6843], "2. <PERSON><PERSON> \"Roll Under\" or &quot;Roll Over\"", [6806, 6843], "2. <PERSON><PERSON> \"Roll Under\" or &ldquo;Roll Over\"", [6806, 6843], "2. <PERSON><PERSON> \"Roll Under\" or &#34;Roll Over\"", [6806, 6843], "2. <PERSON><PERSON> \"Roll Under\" or &rdquo;Roll Over\"", [6806, 6843], "2. <PERSON><PERSON> \"Roll Under\" or \"Roll Over&quot;", [6806, 6843], "2. <PERSON><PERSON> \"Roll Under\" or \"Roll Over&ldquo;", [6806, 6843], "2. <PERSON><PERSON> \"Roll Under\" or \"Roll Over&#34;", [6806, 6843], "2. <PERSON><PERSON> \"Roll Under\" or \"Roll Over&rdquo;", [6865, 6904], "3. <PERSON><PERSON> &quot;<PERSON>\" to see if you win!", [6865, 6904], "3. <PERSON><PERSON> &ldq<PERSON>;<PERSON>\" to see if you win!", [6865, 6904], "3. <PERSON><PERSON> &#34;<PERSON>\" to see if you win!", [6865, 6904], "3. <PERSON><PERSON> &rd<PERSON><PERSON>;<PERSON>\" to see if you win!", [6865, 6904], "3. <PERSON><PERSON> \"Roll Dice&quot; to see if you win!", [6865, 6904], "3. <PERSON><PERSON> \"Roll Dice&ldquo; to see if you win!", [6865, 6904], "3. <PERSON><PERSON> \"Roll Dice&#34; to see if you win!", [6865, 6904], "3. <PERSON><PERSON> \"Roll Dice&rdquo; to see if you win!", [1083, 1086], [1083, 1086], [925, 928], [925, 928], [1517, 1520], [1517, 1520], [1585, 1588], [1585, 1588], [3989, 3992], [3989, 3992], [4067, 4070], [4067, 4070], [4161, 4164], [4161, 4164], [4201, 4204], [4201, 4204], [3866, 3869], [3866, 3869], [3930, 3933], [3930, 3933], [3989, 3992], [3989, 3992], [635, 638], [635, 638], [1499, 1502], [1499, 1502], [73, 150], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>", [1103, 1106], [1103, 1106], [1902, 1905], [1902, 1905], [2051, 2054], [2051, 2054], [2131, 2134], [2131, 2134], [10477, 10480], [10477, 10480], [17031, 17034], [17031, 17034], [17418, 17421], [17418, 17421], [18806, 18809], [18806, 18809], [19081, 19084], [19081, 19084], [19345, 19348], [19345, 19348], [19495, 19498], [19495, 19498], [19750, 19753], [19750, 19753], [21556, 21559], [21556, 21559], [21879, 21882], [21879, 21882], [22093, 22096], [22093, 22096], [22308, 22311], [22308, 22311], [22944, 22947], [22944, 22947], [25208, 25211], [25208, 25211], [26002, 26005], [26002, 26005], [26164, 26167], [26164, 26167], [26524, 26527], [26524, 26527], [26706, 26709], [26706, 26709], [26958, 26961], [26958, 26961], [27673, 27676], [27673, 27676], [4447, 4450], [4447, 4450], [4822, 4825], [4822, 4825], [4898, 4901], [4898, 4901], [4955, 4958], [4955, 4958], [965, 968], [965, 968], [1540, 1543], [1540, 1543], [2356, 2359], [2356, 2359], [4339, 4342], [4339, 4342], [4633, 4636], [4633, 4636], [1897, 1900], [1897, 1900], [2306, 2309], [2306, 2309], [3771, 3774], [3771, 3774], [4756, 4759], [4756, 4759], [5560, 5563], [5560, 5563], [652, 655], [652, 655], [929, 932], [929, 932], [1096, 1099], [1096, 1099], [2568, 2571], [2568, 2571], [2578, 2581], [2578, 2581], [2909, 2912], [2909, 2912], [2919, 2922], [2919, 2922]]