"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/lobby",{

/***/ "(pages-dir-browser)/./pages/lobby.tsx":
/*!*************************!*\
  !*** ./pages/lobby.tsx ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Lobby)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(pages-dir-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(pages-dir-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(pages-dir-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(pages-dir-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/tabs */ \"(pages-dir-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_wallet_WalletModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/wallet/WalletModal */ \"(pages-dir-browser)/./components/wallet/WalletModal.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,Gem,LogOut,Play,Search,Star,TrendingUp,Trophy,Users,Volume2,VolumeX,Wallet!=!lucide-react */ \"(pages-dir-browser)/__barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,Gem,LogOut,Play,Search,Star,TrendingUp,Trophy,Users,Volume2,VolumeX,Wallet!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(pages-dir-browser)/./lib/utils.ts\");\n/* harmony import */ var _lib_sounds__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/sounds */ \"(pages-dir-browser)/./lib/sounds.ts\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/toaster */ \"(pages-dir-browser)/./components/ui/toaster.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(pages-dir-browser)/./components/ui/use-toast.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Lobby() {\n    _s();\n    const { user, logout, loading: authLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast)();\n    // UI state\n    const [showWallet, setShowWallet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [soundEnabled, setSoundEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    // Data state\n    const [games, setGames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [featuredGames, setFeaturedGames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [lobbyStats, setLobbyStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        playersOnline: 0,\n        totalBetsToday: 0,\n        biggestWinToday: 0,\n        totalGamesPlayed: 0\n    });\n    const [recentWinners, setRecentWinners] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Carousel state\n    const [currentFeaturedIndex, setCurrentFeaturedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Lobby.useEffect\": ()=>{\n            if (!authLoading && !user) {\n                router.push('/login');\n            }\n        }\n    }[\"Lobby.useEffect\"], [\n        user,\n        authLoading,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Lobby.useEffect\": ()=>{\n            _lib_sounds__WEBPACK_IMPORTED_MODULE_10__.soundManager.setEnabled(soundEnabled);\n        }\n    }[\"Lobby.useEffect\"], [\n        soundEnabled\n    ]);\n    // Load games and lobby data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Lobby.useEffect\": ()=>{\n            if (user) {\n                loadLobbyData();\n            }\n        }\n    }[\"Lobby.useEffect\"], [\n        user\n    ]);\n    // Auto-rotate featured games carousel\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Lobby.useEffect\": ()=>{\n            if (featuredGames.length > 1) {\n                const interval = setInterval({\n                    \"Lobby.useEffect.interval\": ()=>{\n                        setCurrentFeaturedIndex({\n                            \"Lobby.useEffect.interval\": (prev)=>prev === featuredGames.length - 1 ? 0 : prev + 1\n                        }[\"Lobby.useEffect.interval\"]);\n                    }\n                }[\"Lobby.useEffect.interval\"], 5000);\n                return ({\n                    \"Lobby.useEffect\": ()=>clearInterval(interval)\n                })[\"Lobby.useEffect\"];\n            }\n        }\n    }[\"Lobby.useEffect\"], [\n        featuredGames.length\n    ]);\n    const loadLobbyData = async ()=>{\n        try {\n            setLoading(true);\n            // Load available games\n            const gamesResponse = await fetch('/api/game/list?active=true');\n            const gamesData = await gamesResponse.json();\n            if (gamesData.success) {\n                setGames(gamesData.games);\n                setFeaturedGames(gamesData.games.filter((game)=>game.isFeatured));\n            }\n            // Load lobby statistics\n            const statsResponse = await fetch('/api/lobby/stats');\n            const statsData = await statsResponse.json();\n            if (statsData.success) {\n                setLobbyStats({\n                    playersOnline: statsData.stats.playersOnline,\n                    totalBetsToday: statsData.stats.totalBetsToday,\n                    biggestWinToday: statsData.stats.biggestWinToday,\n                    totalGamesPlayed: statsData.stats.totalGamesPlayed\n                });\n                setRecentWinners(statsData.stats.recentWinners);\n            } else {\n                // Fallback to mock data if API fails\n                setLobbyStats({\n                    playersOnline: Math.floor(Math.random() * 500) + 100,\n                    totalBetsToday: Math.floor(Math.random() * 10000) + 5000,\n                    biggestWinToday: Math.floor(Math.random() * 50000) + 10000,\n                    totalGamesPlayed: Math.floor(Math.random() * 100000) + 50000\n                });\n                setRecentWinners([]);\n            }\n        } catch (error) {\n            console.error('Failed to load lobby data:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load lobby data\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleGameSelect = (gameId)=>{\n        _lib_sounds__WEBPACK_IMPORTED_MODULE_10__.soundManager.play('click');\n        if (gameId === 'mines') {\n            router.push('/game/mines');\n        } else if (gameId === 'dice') {\n            router.push('/game/dice');\n        } else {\n            toast({\n                title: \"Coming Soon\",\n                description: \"\".concat(gameId, \" will be available soon!\"),\n                variant: \"info\"\n            });\n        }\n    };\n    const filteredGames = games.filter((game)=>{\n        const matchesSearch = game.name.toLowerCase().includes(searchQuery.toLowerCase()) || game.description.toLowerCase().includes(searchQuery.toLowerCase());\n        const matchesCategory = selectedCategory === 'all' || game.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    const nextFeatured = ()=>{\n        setCurrentFeaturedIndex((prev)=>prev === featuredGames.length - 1 ? 0 : prev + 1);\n    };\n    const prevFeatured = ()=>{\n        setCurrentFeaturedIndex((prev)=>prev === 0 ? featuredGames.length - 1 : prev - 1);\n    };\n    if (authLoading || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-white\"\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                lineNumber: 189,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n            lineNumber: 188,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null; // Will redirect to login\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gray-800/50 border-b border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Gem, {\n                                        className: \"h-6 w-6 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-white\",\n                                        children: \"BetOctave\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-700/50 px-3 py-1 rounded-md text-white text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-300\",\n                                                children: [\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(user.usdt_balance),\n                                                    \" \"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-orange-400\",\n                                                children: \"₿\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowWallet(true),\n                                        className: \"bg-blue-600 hover:bg-blue-700 text-white border-blue-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Wallet, {\n                                                className: \"h-4 w-4 sm:mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Wallet\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSoundEnabled(!soundEnabled),\n                                        className: \"border-gray-500 text-gray-400 hover:bg-gray-600 hover:text-white\",\n                                        children: soundEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Volume2, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 33\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.VolumeX, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 67\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: logout,\n                                        className: \"border-gray-500 text-gray-400 hover:bg-gray-600 hover:text-white\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.LogOut, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"bg-gray-800/80 border-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            className: \"pb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-white flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.TrendingUp, {\n                                                        className: \"h-5 w-5 mr-2 text-green-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Live Stats\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Users, {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"Players Online\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400 font-semibold\",\n                                                            children: lobbyStats.playersOnline\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Trophy, {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"Biggest Win\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-yellow-400 font-semibold\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(lobbyStats.biggestWinToday)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Clock, {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 277,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"Bets Today\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-400 font-semibold\",\n                                                            children: lobbyStats.totalBetsToday.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"bg-gray-800/80 border-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            className: \"pb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-white flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Trophy, {\n                                                        className: \"h-5 w-5 mr-2 text-yellow-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Recent Winners\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-3\",\n                                            children: recentWinners.map((winner)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-2 bg-gray-700/50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-sm font-medium\",\n                                                                    children: winner.username\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-400 text-xs\",\n                                                                    children: winner.game\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 298,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-green-400 text-sm font-semibold\",\n                                                                    children: [\n                                                                        winner.multiplier,\n                                                                        \"x\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 301,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-300 text-xs\",\n                                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(winner.winAmount)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 302,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, winner.id, true, {\n                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3 space-y-6\",\n                            children: [\n                                featuredGames.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"bg-gray-800/80 border-gray-600 overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            className: \"pb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-white flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Star, {\n                                                        className: \"h-5 w-5 mr-2 text-yellow-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Featured Games\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"p-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"overflow-hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex transition-transform duration-500 ease-in-out\",\n                                                            style: {\n                                                                transform: \"translateX(-\".concat(currentFeaturedIndex * 100, \"%)\")\n                                                            },\n                                                            children: featuredGames.map((game)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full flex-shrink-0\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative bg-gradient-to-r from-purple-600 to-pink-600 p-6 text-white\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                            className: \"text-2xl font-bold mb-2\",\n                                                                                            children: game.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                            lineNumber: 333,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-purple-100 mb-4\",\n                                                                                            children: game.description\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                            lineNumber: 334,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-4 mb-4\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"bg-white/20 px-2 py-1 rounded text-sm\",\n                                                                                                    children: [\n                                                                                                        \"Max \",\n                                                                                                        game.maxMultiplier,\n                                                                                                        \"x\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                                    lineNumber: 336,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"bg-white/20 px-2 py-1 rounded text-sm\",\n                                                                                                    children: [\n                                                                                                        \"Min \",\n                                                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(game.minBet)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                                    lineNumber: 339,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                            lineNumber: 335,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                            onClick: ()=>handleGameSelect(game.id),\n                                                                                            className: \"bg-white text-purple-600 hover:bg-gray-100\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Play, {\n                                                                                                    className: \"h-4 w-4 mr-2\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                                    lineNumber: 347,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                \"Play Now\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                            lineNumber: 343,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                    lineNumber: 332,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-6xl opacity-20\",\n                                                                                    children: game.icon\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                    lineNumber: 351,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                            lineNumber: 331,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, game.id, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 329,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    featuredGames.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                onClick: prevFeatured,\n                                                                className: \"absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/20 hover:bg-black/40 text-white\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.ChevronLeft, {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                onClick: nextFeatured,\n                                                                className: \"absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/20 hover:bg-black/40 text-white\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.ChevronRight, {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"bg-gray-800/80 border-gray-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Search, {\n                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            placeholder: \"Search games...\",\n                                                            value: searchQuery,\n                                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                                            className: \"pl-10 bg-gray-700 border-gray-600 text-white placeholder-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.Tabs, {\n                                                    value: selectedCategory,\n                                                    onValueChange: setSelectedCategory,\n                                                    className: \"w-full sm:w-auto\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsList, {\n                                                        className: \"bg-gray-700 border-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                                                value: \"all\",\n                                                                className: \"data-[state=active]:bg-purple-600\",\n                                                                children: \"All\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                                                value: \"originals\",\n                                                                className: \"data-[state=active]:bg-purple-600\",\n                                                                children: \"Originals\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 402,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                                                value: \"slots\",\n                                                                className: \"data-[state=active]:bg-purple-600\",\n                                                                children: \"Slots\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                                                value: \"live\",\n                                                                className: \"data-[state=active]:bg-purple-600\",\n                                                                children: \"Live\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                                                value: \"table\",\n                                                                className: \"data-[state=active]:bg-purple-600\",\n                                                                children: \"Table\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                    children: filteredGames.map((game)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                            className: \"bg-gray-800/80 border-gray-600 hover:border-purple-500 transition-all duration-200 cursor-pointer group\",\n                                            onClick: ()=>handleGameSelect(game.id),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-3xl\",\n                                                                children: game.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    game.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Star, {\n                                                                        className: \"h-4 w-4 text-yellow-400 fill-current\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                        lineNumber: 425,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    game.isNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"bg-green-500 text-white text-xs px-2 py-1 rounded\",\n                                                                        children: \"NEW\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-white font-semibold mb-2 group-hover:text-purple-400 transition-colors\",\n                                                        children: game.name\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm mb-3 line-clamp-2\",\n                                                        children: game.description\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between text-xs text-gray-500 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Min: \",\n                                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(game.minBet)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Max: \",\n                                                                    game.maxMultiplier,\n                                                                    \"x\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1 mb-3\",\n                                                        children: game.features.slice(0, 2).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded\",\n                                                                children: feature\n                                                            }, index, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        className: \"w-full bg-purple-600 hover:bg-purple-700 text-white group-hover:bg-purple-500\",\n                                                        size: \"sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Play, {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            game.id === 'mines' || game.id === 'dice' ? 'Play Now' : 'Coming Soon'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, game.id, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 13\n                                }, this),\n                                filteredGames.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"bg-gray-800/80 border-gray-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"p-8 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-400 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Search, {\n                                                        className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-2\",\n                                                        children: \"No games found\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Try adjusting your search or filter criteria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>{\n                                                    setSearchQuery('');\n                                                    setSelectedCategory('all');\n                                                },\n                                                className: \"border-gray-500 text-gray-400 hover:bg-gray-600 hover:text-white\",\n                                                children: \"Clear Filters\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallet_WalletModal__WEBPACK_IMPORTED_MODULE_8__.WalletModal, {\n                user: user,\n                isOpen: showWallet,\n                onClose: ()=>setShowWallet(false),\n                onBalanceUpdate: ()=>setShowWallet(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                lineNumber: 496,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_11__.Toaster, {}, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                lineNumber: 504,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, this);\n}\n_s(Lobby, \"7b7MCvOwkmg2mAQJBgrlLN8qFVk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast\n    ];\n});\n_c = Lobby;\nvar _c;\n$RefreshReg$(_c, \"Lobby\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL3BhZ2VzL2xvYmJ5LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUE0QztBQUNKO0FBQ1M7QUFDRDtBQUNpRDtBQUNuRDtBQUNrQztBQUNsQjtBQWlCeEM7QUFDdUI7QUFDRDtBQUNNO0FBQ0c7QUFtQnRDLFNBQVNnQzs7SUFDdEIsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLE1BQU0sRUFBRUMsU0FBU0MsV0FBVyxFQUFFLEdBQUdqQyw4REFBT0E7SUFDdEQsTUFBTWtDLFNBQVNuQyxzREFBU0E7SUFDeEIsTUFBTSxFQUFFb0MsS0FBSyxFQUFFLEdBQUdQLG1FQUFRQTtJQUUxQixXQUFXO0lBQ1gsTUFBTSxDQUFDUSxZQUFZQyxjQUFjLEdBQUd2QywrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUN3QyxjQUFjQyxnQkFBZ0IsR0FBR3pDLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQzBDLGFBQWFDLGVBQWUsR0FBRzNDLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQzRDLGtCQUFrQkMsb0JBQW9CLEdBQUc3QywrQ0FBUUEsQ0FBQztJQUV6RCxhQUFhO0lBQ2IsTUFBTSxDQUFDOEMsT0FBT0MsU0FBUyxHQUFHL0MsK0NBQVFBLENBQWUsRUFBRTtJQUNuRCxNQUFNLENBQUNnRCxlQUFlQyxpQkFBaUIsR0FBR2pELCtDQUFRQSxDQUFlLEVBQUU7SUFDbkUsTUFBTSxDQUFDa0QsWUFBWUMsY0FBYyxHQUFHbkQsK0NBQVFBLENBQWE7UUFDdkRvRCxlQUFlO1FBQ2ZDLGdCQUFnQjtRQUNoQkMsaUJBQWlCO1FBQ2pCQyxrQkFBa0I7SUFDcEI7SUFDQSxNQUFNLENBQUNDLGVBQWVDLGlCQUFpQixHQUFHekQsK0NBQVFBLENBQWlCLEVBQUU7SUFDckUsTUFBTSxDQUFDa0MsU0FBU3dCLFdBQVcsR0FBRzFELCtDQUFRQSxDQUFDO0lBRXZDLGlCQUFpQjtJQUNqQixNQUFNLENBQUMyRCxzQkFBc0JDLHdCQUF3QixHQUFHNUQsK0NBQVFBLENBQUM7SUFFakVELGdEQUFTQTsyQkFBQztZQUNSLElBQUksQ0FBQ29DLGVBQWUsQ0FBQ0gsTUFBTTtnQkFDekJJLE9BQU95QixJQUFJLENBQUM7WUFDZDtRQUNGOzBCQUFHO1FBQUM3QjtRQUFNRztRQUFhQztLQUFPO0lBRTlCckMsZ0RBQVNBOzJCQUFDO1lBQ1I2QixzREFBWUEsQ0FBQ2tDLFVBQVUsQ0FBQ3RCO1FBQzFCOzBCQUFHO1FBQUNBO0tBQWE7SUFFakIsNEJBQTRCO0lBQzVCekMsZ0RBQVNBOzJCQUFDO1lBQ1IsSUFBSWlDLE1BQU07Z0JBQ1IrQjtZQUNGO1FBQ0Y7MEJBQUc7UUFBQy9CO0tBQUs7SUFFVCxzQ0FBc0M7SUFDdENqQyxnREFBU0E7MkJBQUM7WUFDUixJQUFJaUQsY0FBY2dCLE1BQU0sR0FBRyxHQUFHO2dCQUM1QixNQUFNQyxXQUFXQztnREFBWTt3QkFDM0JOO3dEQUF3QixDQUFDTyxPQUN2QkEsU0FBU25CLGNBQWNnQixNQUFNLEdBQUcsSUFBSSxJQUFJRyxPQUFPOztvQkFFbkQ7K0NBQUc7Z0JBQ0g7dUNBQU8sSUFBTUMsY0FBY0g7O1lBQzdCO1FBQ0Y7MEJBQUc7UUFBQ2pCLGNBQWNnQixNQUFNO0tBQUM7SUFFekIsTUFBTUQsZ0JBQWdCO1FBQ3BCLElBQUk7WUFDRkwsV0FBVztZQUVYLHVCQUF1QjtZQUN2QixNQUFNVyxnQkFBZ0IsTUFBTUMsTUFBTTtZQUNsQyxNQUFNQyxZQUFZLE1BQU1GLGNBQWNHLElBQUk7WUFFMUMsSUFBSUQsVUFBVUUsT0FBTyxFQUFFO2dCQUNyQjFCLFNBQVN3QixVQUFVekIsS0FBSztnQkFDeEJHLGlCQUFpQnNCLFVBQVV6QixLQUFLLENBQUM0QixNQUFNLENBQUMsQ0FBQ0MsT0FBcUJBLEtBQUtDLFVBQVU7WUFDL0U7WUFFQSx3QkFBd0I7WUFDeEIsTUFBTUMsZ0JBQWdCLE1BQU1QLE1BQU07WUFDbEMsTUFBTVEsWUFBWSxNQUFNRCxjQUFjTCxJQUFJO1lBRTFDLElBQUlNLFVBQVVMLE9BQU8sRUFBRTtnQkFDckJ0QixjQUFjO29CQUNaQyxlQUFlMEIsVUFBVUMsS0FBSyxDQUFDM0IsYUFBYTtvQkFDNUNDLGdCQUFnQnlCLFVBQVVDLEtBQUssQ0FBQzFCLGNBQWM7b0JBQzlDQyxpQkFBaUJ3QixVQUFVQyxLQUFLLENBQUN6QixlQUFlO29CQUNoREMsa0JBQWtCdUIsVUFBVUMsS0FBSyxDQUFDeEIsZ0JBQWdCO2dCQUNwRDtnQkFDQUUsaUJBQWlCcUIsVUFBVUMsS0FBSyxDQUFDdkIsYUFBYTtZQUNoRCxPQUFPO2dCQUNMLHFDQUFxQztnQkFDckNMLGNBQWM7b0JBQ1pDLGVBQWU0QixLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBSyxPQUFPO29CQUNqRDdCLGdCQUFnQjJCLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsTUFBTSxLQUFLLFNBQVM7b0JBQ3BENUIsaUJBQWlCMEIsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUssU0FBUztvQkFDckQzQixrQkFBa0J5QixLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBSyxVQUFVO2dCQUN6RDtnQkFDQXpCLGlCQUFpQixFQUFFO1lBQ3JCO1FBRUYsRUFBRSxPQUFPMEIsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsOEJBQThCQTtZQUM1QzlDLE1BQU07Z0JBQ0pnRCxPQUFPO2dCQUNQQyxhQUFhO2dCQUNiQyxTQUFTO1lBQ1g7UUFDRixTQUFVO1lBQ1I3QixXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU04QixtQkFBbUIsQ0FBQ0M7UUFDeEI3RCxzREFBWUEsQ0FBQzhELElBQUksQ0FBQztRQUVsQixJQUFJRCxXQUFXLFNBQVM7WUFDdEJyRCxPQUFPeUIsSUFBSSxDQUFDO1FBQ2QsT0FBTyxJQUFJNEIsV0FBVyxRQUFRO1lBQzVCckQsT0FBT3lCLElBQUksQ0FBQztRQUNkLE9BQU87WUFDTHhCLE1BQU07Z0JBQ0pnRCxPQUFPO2dCQUNQQyxhQUFhLEdBQVUsT0FBUEcsUUFBTztnQkFDdkJGLFNBQVM7WUFDWDtRQUNGO0lBQ0Y7SUFFQSxNQUFNSSxnQkFBZ0I3QyxNQUFNNEIsTUFBTSxDQUFDQyxDQUFBQTtRQUNqQyxNQUFNaUIsZ0JBQWdCakIsS0FBS2tCLElBQUksQ0FBQ0MsV0FBVyxHQUFHQyxRQUFRLENBQUNyRCxZQUFZb0QsV0FBVyxPQUN6RG5CLEtBQUtXLFdBQVcsQ0FBQ1EsV0FBVyxHQUFHQyxRQUFRLENBQUNyRCxZQUFZb0QsV0FBVztRQUNwRixNQUFNRSxrQkFBa0JwRCxxQkFBcUIsU0FBUytCLEtBQUtzQixRQUFRLEtBQUtyRDtRQUN4RSxPQUFPZ0QsaUJBQWlCSTtJQUMxQjtJQUVBLE1BQU1FLGVBQWU7UUFDbkJ0Qyx3QkFBd0IsQ0FBQ08sT0FDdkJBLFNBQVNuQixjQUFjZ0IsTUFBTSxHQUFHLElBQUksSUFBSUcsT0FBTztJQUVuRDtJQUVBLE1BQU1nQyxlQUFlO1FBQ25CdkMsd0JBQXdCLENBQUNPLE9BQ3ZCQSxTQUFTLElBQUluQixjQUFjZ0IsTUFBTSxHQUFHLElBQUlHLE9BQU87SUFFbkQ7SUFFQSxJQUFJaEMsZUFBZUQsU0FBUztRQUMxQixxQkFDRSw4REFBQ2tFO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOzs7Ozs7Ozs7OztJQUdyQjtJQUVBLElBQUksQ0FBQ3JFLE1BQU07UUFDVCxPQUFPLE1BQU0seUJBQXlCO0lBQ3hDO0lBRUEscUJBQ0UsOERBQUNvRTtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0M7Z0JBQU9ELFdBQVU7MEJBQ2hCLDRFQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0U7d0JBQUlGLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUN4RiwrS0FBR0E7d0NBQUN3RixXQUFVOzs7Ozs7a0RBQ2YsOERBQUNHO3dDQUFLSCxXQUFVO2tEQUErQjs7Ozs7Ozs7Ozs7OzBDQUdqRCw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNHO2dEQUFLSCxXQUFVOztvREFBaUIxRSwwREFBY0EsQ0FBQ0ssS0FBS3lFLFlBQVk7b0RBQUU7Ozs7Ozs7MERBQ25FLDhEQUFDRDtnREFBS0gsV0FBVTswREFBa0I7Ozs7Ozs7Ozs7OztrREFHcEMsOERBQUNsRyx5REFBTUE7d0NBQ0xvRixTQUFRO3dDQUNSbUIsTUFBSzt3Q0FDTEMsU0FBUyxJQUFNcEUsY0FBYzt3Q0FDN0I4RCxXQUFVOzswREFFViw4REFBQ3RGLGtMQUFNQTtnREFBQ3NGLFdBQVU7Ozs7OzswREFDbEIsOERBQUNHO2dEQUFLSCxXQUFVOzBEQUFtQjs7Ozs7Ozs7Ozs7O2tEQUdyQyw4REFBQ2xHLHlEQUFNQTt3Q0FDTG9GLFNBQVE7d0NBQ1JtQixNQUFLO3dDQUNMQyxTQUFTLElBQU1sRSxnQkFBZ0IsQ0FBQ0Q7d0NBQ2hDNkQsV0FBVTtrREFFVDdELDZCQUFlLDhEQUFDeEIsbUxBQU9BOzRDQUFDcUYsV0FBVTs7Ozs7aUVBQWUsOERBQUNwRixtTEFBT0E7NENBQUNvRixXQUFVOzs7Ozs7Ozs7OztrREFHdkUsOERBQUNsRyx5REFBTUE7d0NBQ0xvRixTQUFRO3dDQUNSbUIsTUFBSzt3Q0FDTEMsU0FBUzFFO3dDQUNUb0UsV0FBVTtrREFFViw0RUFBQ3ZGLGtMQUFNQTs0Q0FBQ3VGLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRNUIsOERBQUNPO2dCQUFLUCxXQUFVOzBCQUNkLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBRWIsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FFYiw4REFBQ2pHLHFEQUFJQTtvQ0FBQ2lHLFdBQVU7O3NEQUNkLDhEQUFDL0YsMkRBQVVBOzRDQUFDK0YsV0FBVTtzREFDcEIsNEVBQUM5RiwwREFBU0E7Z0RBQUM4RixXQUFVOztrRUFDbkIsOERBQUNsRixzTEFBVUE7d0RBQUNrRixXQUFVOzs7Ozs7b0RBQWdDOzs7Ozs7Ozs7Ozs7c0RBSTFELDhEQUFDaEcsNERBQVdBOzRDQUFDZ0csV0FBVTs7OERBQ3JCLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ2pGLGlMQUFLQTtvRUFBQ2lGLFdBQVU7Ozs7Ozs4RUFDakIsOERBQUNHO29FQUFLSCxXQUFVOzhFQUFVOzs7Ozs7Ozs7Ozs7c0VBRTVCLDhEQUFDRzs0REFBS0gsV0FBVTtzRUFBZ0NuRCxXQUFXRSxhQUFhOzs7Ozs7Ozs7Ozs7OERBRTFFLDhEQUFDZ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNoRixrTEFBTUE7b0VBQUNnRixXQUFVOzs7Ozs7OEVBQ2xCLDhEQUFDRztvRUFBS0gsV0FBVTs4RUFBVTs7Ozs7Ozs7Ozs7O3NFQUU1Qiw4REFBQ0c7NERBQUtILFdBQVU7c0VBQWlDMUUsMERBQWNBLENBQUN1QixXQUFXSSxlQUFlOzs7Ozs7Ozs7Ozs7OERBRTVGLDhEQUFDOEM7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUMvRSxpTEFBS0E7b0VBQUMrRSxXQUFVOzs7Ozs7OEVBQ2pCLDhEQUFDRztvRUFBS0gsV0FBVTs4RUFBVTs7Ozs7Ozs7Ozs7O3NFQUU1Qiw4REFBQ0c7NERBQUtILFdBQVU7c0VBQStCbkQsV0FBV0csY0FBYyxDQUFDd0QsY0FBYzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU03Riw4REFBQ3pHLHFEQUFJQTtvQ0FBQ2lHLFdBQVU7O3NEQUNkLDhEQUFDL0YsMkRBQVVBOzRDQUFDK0YsV0FBVTtzREFDcEIsNEVBQUM5RiwwREFBU0E7Z0RBQUM4RixXQUFVOztrRUFDbkIsOERBQUNoRixrTEFBTUE7d0RBQUNnRixXQUFVOzs7Ozs7b0RBQWlDOzs7Ozs7Ozs7Ozs7c0RBSXZELDhEQUFDaEcsNERBQVdBOzRDQUFDZ0csV0FBVTtzREFDcEI3QyxjQUFjc0QsR0FBRyxDQUFDLENBQUNDLHVCQUNsQiw4REFBQ1g7b0RBQW9CQyxXQUFVOztzRUFDN0IsOERBQUNEOzs4RUFDQyw4REFBQ0E7b0VBQUlDLFdBQVU7OEVBQWtDVSxPQUFPQyxRQUFROzs7Ozs7OEVBQ2hFLDhEQUFDWjtvRUFBSUMsV0FBVTs4RUFBeUJVLE9BQU9wQyxJQUFJOzs7Ozs7Ozs7Ozs7c0VBRXJELDhEQUFDeUI7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDtvRUFBSUMsV0FBVTs7d0VBQXdDVSxPQUFPRSxVQUFVO3dFQUFDOzs7Ozs7OzhFQUN6RSw4REFBQ2I7b0VBQUlDLFdBQVU7OEVBQXlCMUUsMERBQWNBLENBQUNvRixPQUFPRyxTQUFTOzs7Ozs7Ozs7Ozs7O21EQVBqRUgsT0FBT0ksRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FnQjNCLDhEQUFDZjs0QkFBSUMsV0FBVTs7Z0NBRVpyRCxjQUFjZ0IsTUFBTSxHQUFHLG1CQUN0Qiw4REFBQzVELHFEQUFJQTtvQ0FBQ2lHLFdBQVU7O3NEQUNkLDhEQUFDL0YsMkRBQVVBOzRDQUFDK0YsV0FBVTtzREFDcEIsNEVBQUM5RiwwREFBU0E7Z0RBQUM4RixXQUFVOztrRUFDbkIsOERBQUM5RSxnTEFBSUE7d0RBQUM4RSxXQUFVOzs7Ozs7b0RBQWlDOzs7Ozs7Ozs7Ozs7c0RBSXJELDhEQUFDaEcsNERBQVdBOzRDQUFDZ0csV0FBVTtzREFDckIsNEVBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUNEOzREQUNDQyxXQUFVOzREQUNWZSxPQUFPO2dFQUFFQyxXQUFXLGVBQTBDLE9BQTNCMUQsdUJBQXVCLEtBQUk7NERBQUk7c0VBRWpFWCxjQUFjOEQsR0FBRyxDQUFDLENBQUNuQyxxQkFDbEIsOERBQUN5QjtvRUFBa0JDLFdBQVU7OEVBQzNCLDRFQUFDRDt3RUFBSUMsV0FBVTtrRkFDYiw0RUFBQ0Q7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDRDs7c0dBQ0MsOERBQUNrQjs0RkFBR2pCLFdBQVU7c0dBQTJCMUIsS0FBS2tCLElBQUk7Ozs7OztzR0FDbEQsOERBQUMwQjs0RkFBRWxCLFdBQVU7c0dBQXdCMUIsS0FBS1csV0FBVzs7Ozs7O3NHQUNyRCw4REFBQ2M7NEZBQUlDLFdBQVU7OzhHQUNiLDhEQUFDRztvR0FBS0gsV0FBVTs7d0dBQXdDO3dHQUNqRDFCLEtBQUs2QyxhQUFhO3dHQUFDOzs7Ozs7OzhHQUUxQiw4REFBQ2hCO29HQUFLSCxXQUFVOzt3R0FBd0M7d0dBQ2pEMUUsMERBQWNBLENBQUNnRCxLQUFLOEMsTUFBTTs7Ozs7Ozs7Ozs7OztzR0FHbkMsOERBQUN0SCx5REFBTUE7NEZBQ0x3RyxTQUFTLElBQU1uQixpQkFBaUJiLEtBQUt3QyxFQUFFOzRGQUN2Q2QsV0FBVTs7OEdBRVYsOERBQUM3RSxnTEFBSUE7b0dBQUM2RSxXQUFVOzs7Ozs7Z0dBQWlCOzs7Ozs7Ozs7Ozs7OzhGQUlyQyw4REFBQ0Q7b0ZBQUlDLFdBQVU7OEZBQ1oxQixLQUFLK0MsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7bUVBdkJSL0MsS0FBS3dDLEVBQUU7Ozs7Ozs7Ozs7Ozs7OztvREFnQ3RCbkUsY0FBY2dCLE1BQU0sR0FBRyxtQkFDdEI7OzBFQUNFLDhEQUFDN0QseURBQU1BO2dFQUNMb0YsU0FBUTtnRUFDUm1CLE1BQUs7Z0VBQ0xDLFNBQVNSO2dFQUNURSxXQUFVOzBFQUVWLDRFQUFDNUUsdUxBQVdBO29FQUFDNEUsV0FBVTs7Ozs7Ozs7Ozs7MEVBRXpCLDhEQUFDbEcseURBQU1BO2dFQUNMb0YsU0FBUTtnRUFDUm1CLE1BQUs7Z0VBQ0xDLFNBQVNUO2dFQUNURyxXQUFVOzBFQUVWLDRFQUFDM0Usd0xBQVlBO29FQUFDMkUsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQVV0Qyw4REFBQ2pHLHFEQUFJQTtvQ0FBQ2lHLFdBQVU7OENBQ2QsNEVBQUNoRyw0REFBV0E7d0NBQUNnRyxXQUFVO2tEQUNyQiw0RUFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNuRixrTEFBTUE7NERBQUNtRixXQUFVOzs7Ozs7c0VBQ2xCLDhEQUFDN0YsdURBQUtBOzREQUNKbUgsYUFBWTs0REFDWkMsT0FBT2xGOzREQUNQbUYsVUFBVSxDQUFDQyxJQUFNbkYsZUFBZW1GLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzs0REFDOUN2QixXQUFVOzs7Ozs7Ozs7Ozs7OERBR2QsOERBQUM1RixxREFBSUE7b0RBQUNtSCxPQUFPaEY7b0RBQWtCb0YsZUFBZW5GO29EQUFxQndELFdBQVU7OERBQzNFLDRFQUFDM0YseURBQVFBO3dEQUFDMkYsV0FBVTs7MEVBQ2xCLDhEQUFDMUYsNERBQVdBO2dFQUFDaUgsT0FBTTtnRUFBTXZCLFdBQVU7MEVBQW9DOzs7Ozs7MEVBQ3ZFLDhEQUFDMUYsNERBQVdBO2dFQUFDaUgsT0FBTTtnRUFBWXZCLFdBQVU7MEVBQW9DOzs7Ozs7MEVBQzdFLDhEQUFDMUYsNERBQVdBO2dFQUFDaUgsT0FBTTtnRUFBUXZCLFdBQVU7MEVBQW9DOzs7Ozs7MEVBQ3pFLDhEQUFDMUYsNERBQVdBO2dFQUFDaUgsT0FBTTtnRUFBT3ZCLFdBQVU7MEVBQW9DOzs7Ozs7MEVBQ3hFLDhEQUFDMUYsNERBQVdBO2dFQUFDaUgsT0FBTTtnRUFBUXZCLFdBQVU7MEVBQW9DOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBUW5GLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDWlYsY0FBY21CLEdBQUcsQ0FBQyxDQUFDbkMscUJBQ2xCLDhEQUFDdkUscURBQUlBOzRDQUVIaUcsV0FBVTs0Q0FDVk0sU0FBUyxJQUFNbkIsaUJBQWlCYixLQUFLd0MsRUFBRTtzREFFdkMsNEVBQUM5Ryw0REFBV0E7Z0RBQUNnRyxXQUFVOztrRUFDckIsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7MEVBQVkxQixLQUFLK0MsSUFBSTs7Ozs7OzBFQUNwQyw4REFBQ3RCO2dFQUFJQyxXQUFVOztvRUFDWjFCLEtBQUtDLFVBQVUsa0JBQ2QsOERBQUNyRCxnTEFBSUE7d0VBQUM4RSxXQUFVOzs7Ozs7b0VBRWpCMUIsS0FBS3NELEtBQUssa0JBQ1QsOERBQUN6Qjt3RUFBS0gsV0FBVTtrRkFBb0Q7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFLMUUsOERBQUNpQjt3REFBR2pCLFdBQVU7a0VBQ1gxQixLQUFLa0IsSUFBSTs7Ozs7O2tFQUdaLDhEQUFDMEI7d0RBQUVsQixXQUFVO2tFQUNWMUIsS0FBS1csV0FBVzs7Ozs7O2tFQUduQiw4REFBQ2M7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRzs7b0VBQUs7b0VBQU03RSwwREFBY0EsQ0FBQ2dELEtBQUs4QyxNQUFNOzs7Ozs7OzBFQUN0Qyw4REFBQ2pCOztvRUFBSztvRUFBTTdCLEtBQUs2QyxhQUFhO29FQUFDOzs7Ozs7Ozs7Ozs7O2tFQUdqQyw4REFBQ3BCO3dEQUFJQyxXQUFVO2tFQUNaMUIsS0FBS3VELFFBQVEsQ0FBQ0MsS0FBSyxDQUFDLEdBQUcsR0FBR3JCLEdBQUcsQ0FBQyxDQUFDc0IsU0FBU0Msc0JBQ3ZDLDhEQUFDN0I7Z0VBRUNILFdBQVU7MEVBRVQrQjsrREFISUM7Ozs7Ozs7Ozs7a0VBUVgsOERBQUNsSSx5REFBTUE7d0RBQ0xrRyxXQUFVO3dEQUNWSyxNQUFLOzswRUFFTCw4REFBQ2xGLGdMQUFJQTtnRUFBQzZFLFdBQVU7Ozs7Ozs0REFDZDFCLEtBQUt3QyxFQUFFLEtBQUssV0FBV3hDLEtBQUt3QyxFQUFFLEtBQUssU0FBVSxhQUFhOzs7Ozs7Ozs7Ozs7OzJDQTlDM0R4QyxLQUFLd0MsRUFBRTs7Ozs7Ozs7OztnQ0FzRGpCeEIsY0FBYzNCLE1BQU0sS0FBSyxtQkFDeEIsOERBQUM1RCxxREFBSUE7b0NBQUNpRyxXQUFVOzhDQUNkLDRFQUFDaEcsNERBQVdBO3dDQUFDZ0csV0FBVTs7MERBQ3JCLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNuRixrTEFBTUE7d0RBQUNtRixXQUFVOzs7Ozs7a0VBQ2xCLDhEQUFDaUI7d0RBQUdqQixXQUFVO2tFQUE2Qjs7Ozs7O2tFQUMzQyw4REFBQ2tCO2tFQUFFOzs7Ozs7Ozs7Ozs7MERBRUwsOERBQUNwSCx5REFBTUE7Z0RBQ0xvRixTQUFRO2dEQUNSb0IsU0FBUztvREFDUGhFLGVBQWU7b0RBQ2ZFLG9CQUFvQjtnREFDdEI7Z0RBQ0F3RCxXQUFVOzBEQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVdiLDhEQUFDekYsdUVBQVdBO2dCQUNWb0IsTUFBTUE7Z0JBQ05zRyxRQUFRaEc7Z0JBQ1JpRyxTQUFTLElBQU1oRyxjQUFjO2dCQUM3QmlHLGlCQUFpQixJQUFNakcsY0FBYzs7Ozs7OzBCQUl2Qyw4REFBQ1YsNERBQU9BOzs7Ozs7Ozs7OztBQUdkO0dBM2N3QkU7O1FBQ3lCN0IsMERBQU9BO1FBQ3ZDRCxrREFBU0E7UUFDTjZCLCtEQUFRQTs7O0tBSEpDIiwic291cmNlcyI6WyJFOlxcMTExXFxQUk9KRUNUXFxtaW5lcy1nYW1lXFxwYWdlc1xcbG9iYnkudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L3JvdXRlcic7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCc7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJztcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkRGVzY3JpcHRpb24sIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9jYXJkJztcbmltcG9ydCB7IElucHV0IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2lucHV0JztcbmltcG9ydCB7IFRhYnMsIFRhYnNDb250ZW50LCBUYWJzTGlzdCwgVGFic1RyaWdnZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvdGFicyc7XG5pbXBvcnQgeyBXYWxsZXRNb2RhbCB9IGZyb20gJ0AvY29tcG9uZW50cy93YWxsZXQvV2FsbGV0TW9kYWwnO1xuaW1wb3J0IHtcbiAgR2VtLFxuICBMb2dPdXQsXG4gIFdhbGxldCxcbiAgVm9sdW1lMixcbiAgVm9sdW1lWCxcbiAgU2VhcmNoLFxuICBUcmVuZGluZ1VwLFxuICBVc2VycyxcbiAgVHJvcGh5LFxuICBDbG9jayxcbiAgU3RhcixcbiAgUGxheSxcbiAgQ2hldnJvbkxlZnQsXG4gIENoZXZyb25SaWdodCxcbiAgUGx1c1xufSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgZm9ybWF0Q3VycmVuY3kgfSBmcm9tICdAL2xpYi91dGlscyc7XG5pbXBvcnQgeyBzb3VuZE1hbmFnZXIgfSBmcm9tICdAL2xpYi9zb3VuZHMnO1xuaW1wb3J0IHsgVG9hc3RlciB9IGZyb20gJ0AvY29tcG9uZW50cy91aS90b2FzdGVyJztcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3VzZS10b2FzdCc7XG5pbXBvcnQgeyBHYW1lQ29uZmlnIH0gZnJvbSAnQC90eXBlcyc7XG5cbmludGVyZmFjZSBMb2JieVN0YXRzIHtcbiAgcGxheWVyc09ubGluZTogbnVtYmVyO1xuICB0b3RhbEJldHNUb2RheTogbnVtYmVyO1xuICBiaWdnZXN0V2luVG9kYXk6IG51bWJlcjtcbiAgdG90YWxHYW1lc1BsYXllZDogbnVtYmVyO1xufVxuXG5pbnRlcmZhY2UgUmVjZW50V2lubmVyIHtcbiAgaWQ6IHN0cmluZztcbiAgdXNlcm5hbWU6IHN0cmluZztcbiAgZ2FtZTogc3RyaW5nO1xuICBtdWx0aXBsaWVyOiBudW1iZXI7XG4gIHdpbkFtb3VudDogbnVtYmVyO1xuICB0aW1lc3RhbXA6IHN0cmluZztcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9iYnkoKSB7XG4gIGNvbnN0IHsgdXNlciwgbG9nb3V0LCBsb2FkaW5nOiBhdXRoTG9hZGluZyB9ID0gdXNlQXV0aCgpO1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgY29uc3QgeyB0b2FzdCB9ID0gdXNlVG9hc3QoKTtcblxuICAvLyBVSSBzdGF0ZVxuICBjb25zdCBbc2hvd1dhbGxldCwgc2V0U2hvd1dhbGxldF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzb3VuZEVuYWJsZWQsIHNldFNvdW5kRW5hYmxlZF0gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW3NlYXJjaFF1ZXJ5LCBzZXRTZWFyY2hRdWVyeV0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtzZWxlY3RlZENhdGVnb3J5LCBzZXRTZWxlY3RlZENhdGVnb3J5XSA9IHVzZVN0YXRlKCdhbGwnKTtcblxuICAvLyBEYXRhIHN0YXRlXG4gIGNvbnN0IFtnYW1lcywgc2V0R2FtZXNdID0gdXNlU3RhdGU8R2FtZUNvbmZpZ1tdPihbXSk7XG4gIGNvbnN0IFtmZWF0dXJlZEdhbWVzLCBzZXRGZWF0dXJlZEdhbWVzXSA9IHVzZVN0YXRlPEdhbWVDb25maWdbXT4oW10pO1xuICBjb25zdCBbbG9iYnlTdGF0cywgc2V0TG9iYnlTdGF0c10gPSB1c2VTdGF0ZTxMb2JieVN0YXRzPih7XG4gICAgcGxheWVyc09ubGluZTogMCxcbiAgICB0b3RhbEJldHNUb2RheTogMCxcbiAgICBiaWdnZXN0V2luVG9kYXk6IDAsXG4gICAgdG90YWxHYW1lc1BsYXllZDogMFxuICB9KTtcbiAgY29uc3QgW3JlY2VudFdpbm5lcnMsIHNldFJlY2VudFdpbm5lcnNdID0gdXNlU3RhdGU8UmVjZW50V2lubmVyW10+KFtdKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG5cbiAgLy8gQ2Fyb3VzZWwgc3RhdGVcbiAgY29uc3QgW2N1cnJlbnRGZWF0dXJlZEluZGV4LCBzZXRDdXJyZW50RmVhdHVyZWRJbmRleF0gPSB1c2VTdGF0ZSgwKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghYXV0aExvYWRpbmcgJiYgIXVzZXIpIHtcbiAgICAgIHJvdXRlci5wdXNoKCcvbG9naW4nKTtcbiAgICB9XG4gIH0sIFt1c2VyLCBhdXRoTG9hZGluZywgcm91dGVyXSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzb3VuZE1hbmFnZXIuc2V0RW5hYmxlZChzb3VuZEVuYWJsZWQpO1xuICB9LCBbc291bmRFbmFibGVkXSk7XG5cbiAgLy8gTG9hZCBnYW1lcyBhbmQgbG9iYnkgZGF0YVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICh1c2VyKSB7XG4gICAgICBsb2FkTG9iYnlEYXRhKCk7XG4gICAgfVxuICB9LCBbdXNlcl0pO1xuXG4gIC8vIEF1dG8tcm90YXRlIGZlYXR1cmVkIGdhbWVzIGNhcm91c2VsXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGZlYXR1cmVkR2FtZXMubGVuZ3RoID4gMSkge1xuICAgICAgY29uc3QgaW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7XG4gICAgICAgIHNldEN1cnJlbnRGZWF0dXJlZEluZGV4KChwcmV2KSA9PlxuICAgICAgICAgIHByZXYgPT09IGZlYXR1cmVkR2FtZXMubGVuZ3RoIC0gMSA/IDAgOiBwcmV2ICsgMVxuICAgICAgICApO1xuICAgICAgfSwgNTAwMCk7XG4gICAgICByZXR1cm4gKCkgPT4gY2xlYXJJbnRlcnZhbChpbnRlcnZhbCk7XG4gICAgfVxuICB9LCBbZmVhdHVyZWRHYW1lcy5sZW5ndGhdKTtcblxuICBjb25zdCBsb2FkTG9iYnlEYXRhID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xuXG4gICAgICAvLyBMb2FkIGF2YWlsYWJsZSBnYW1lc1xuICAgICAgY29uc3QgZ2FtZXNSZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2dhbWUvbGlzdD9hY3RpdmU9dHJ1ZScpO1xuICAgICAgY29uc3QgZ2FtZXNEYXRhID0gYXdhaXQgZ2FtZXNSZXNwb25zZS5qc29uKCk7XG5cbiAgICAgIGlmIChnYW1lc0RhdGEuc3VjY2Vzcykge1xuICAgICAgICBzZXRHYW1lcyhnYW1lc0RhdGEuZ2FtZXMpO1xuICAgICAgICBzZXRGZWF0dXJlZEdhbWVzKGdhbWVzRGF0YS5nYW1lcy5maWx0ZXIoKGdhbWU6IEdhbWVDb25maWcpID0+IGdhbWUuaXNGZWF0dXJlZCkpO1xuICAgICAgfVxuXG4gICAgICAvLyBMb2FkIGxvYmJ5IHN0YXRpc3RpY3NcbiAgICAgIGNvbnN0IHN0YXRzUmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9sb2JieS9zdGF0cycpO1xuICAgICAgY29uc3Qgc3RhdHNEYXRhID0gYXdhaXQgc3RhdHNSZXNwb25zZS5qc29uKCk7XG5cbiAgICAgIGlmIChzdGF0c0RhdGEuc3VjY2Vzcykge1xuICAgICAgICBzZXRMb2JieVN0YXRzKHtcbiAgICAgICAgICBwbGF5ZXJzT25saW5lOiBzdGF0c0RhdGEuc3RhdHMucGxheWVyc09ubGluZSxcbiAgICAgICAgICB0b3RhbEJldHNUb2RheTogc3RhdHNEYXRhLnN0YXRzLnRvdGFsQmV0c1RvZGF5LFxuICAgICAgICAgIGJpZ2dlc3RXaW5Ub2RheTogc3RhdHNEYXRhLnN0YXRzLmJpZ2dlc3RXaW5Ub2RheSxcbiAgICAgICAgICB0b3RhbEdhbWVzUGxheWVkOiBzdGF0c0RhdGEuc3RhdHMudG90YWxHYW1lc1BsYXllZFxuICAgICAgICB9KTtcbiAgICAgICAgc2V0UmVjZW50V2lubmVycyhzdGF0c0RhdGEuc3RhdHMucmVjZW50V2lubmVycyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBGYWxsYmFjayB0byBtb2NrIGRhdGEgaWYgQVBJIGZhaWxzXG4gICAgICAgIHNldExvYmJ5U3RhdHMoe1xuICAgICAgICAgIHBsYXllcnNPbmxpbmU6IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDUwMCkgKyAxMDAsXG4gICAgICAgICAgdG90YWxCZXRzVG9kYXk6IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDEwMDAwKSArIDUwMDAsXG4gICAgICAgICAgYmlnZ2VzdFdpblRvZGF5OiBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiA1MDAwMCkgKyAxMDAwMCxcbiAgICAgICAgICB0b3RhbEdhbWVzUGxheWVkOiBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAxMDAwMDApICsgNTAwMDBcbiAgICAgICAgfSk7XG4gICAgICAgIHNldFJlY2VudFdpbm5lcnMoW10pO1xuICAgICAgfVxuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGxvYmJ5IGRhdGE6JywgZXJyb3IpO1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCJFcnJvclwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJGYWlsZWQgdG8gbG9hZCBsb2JieSBkYXRhXCIsXG4gICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIixcbiAgICAgIH0pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlR2FtZVNlbGVjdCA9IChnYW1lSWQ6IHN0cmluZykgPT4ge1xuICAgIHNvdW5kTWFuYWdlci5wbGF5KCdjbGljaycpO1xuXG4gICAgaWYgKGdhbWVJZCA9PT0gJ21pbmVzJykge1xuICAgICAgcm91dGVyLnB1c2goJy9nYW1lL21pbmVzJyk7XG4gICAgfSBlbHNlIGlmIChnYW1lSWQgPT09ICdkaWNlJykge1xuICAgICAgcm91dGVyLnB1c2goJy9nYW1lL2RpY2UnKTtcbiAgICB9IGVsc2Uge1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCJDb21pbmcgU29vblwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogYCR7Z2FtZUlkfSB3aWxsIGJlIGF2YWlsYWJsZSBzb29uIWAsXG4gICAgICAgIHZhcmlhbnQ6IFwiaW5mb1wiLFxuICAgICAgfSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGZpbHRlcmVkR2FtZXMgPSBnYW1lcy5maWx0ZXIoZ2FtZSA9PiB7XG4gICAgY29uc3QgbWF0Y2hlc1NlYXJjaCA9IGdhbWUubmFtZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFF1ZXJ5LnRvTG93ZXJDYXNlKCkpIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAgZ2FtZS5kZXNjcmlwdGlvbi50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFF1ZXJ5LnRvTG93ZXJDYXNlKCkpO1xuICAgIGNvbnN0IG1hdGNoZXNDYXRlZ29yeSA9IHNlbGVjdGVkQ2F0ZWdvcnkgPT09ICdhbGwnIHx8IGdhbWUuY2F0ZWdvcnkgPT09IHNlbGVjdGVkQ2F0ZWdvcnk7XG4gICAgcmV0dXJuIG1hdGNoZXNTZWFyY2ggJiYgbWF0Y2hlc0NhdGVnb3J5O1xuICB9KTtcblxuICBjb25zdCBuZXh0RmVhdHVyZWQgPSAoKSA9PiB7XG4gICAgc2V0Q3VycmVudEZlYXR1cmVkSW5kZXgoKHByZXYpID0+XG4gICAgICBwcmV2ID09PSBmZWF0dXJlZEdhbWVzLmxlbmd0aCAtIDEgPyAwIDogcHJldiArIDFcbiAgICApO1xuICB9O1xuXG4gIGNvbnN0IHByZXZGZWF0dXJlZCA9ICgpID0+IHtcbiAgICBzZXRDdXJyZW50RmVhdHVyZWRJbmRleCgocHJldikgPT5cbiAgICAgIHByZXYgPT09IDAgPyBmZWF0dXJlZEdhbWVzLmxlbmd0aCAtIDEgOiBwcmV2IC0gMVxuICAgICk7XG4gIH07XG5cbiAgaWYgKGF1dGhMb2FkaW5nIHx8IGxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ncmF5LTkwMCB2aWEtcHVycGxlLTkwMCB0by12aW9sZXQtOTAwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTMyIHctMzIgYm9yZGVyLWItMiBib3JkZXItd2hpdGVcIj48L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICBpZiAoIXVzZXIpIHtcbiAgICByZXR1cm4gbnVsbDsgLy8gV2lsbCByZWRpcmVjdCB0byBsb2dpblxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTkwMFwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwiYmctZ3JheS04MDAvNTAgYm9yZGVyLWIgYm9yZGVyLWdyYXktNzAwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS0zXCI+XG4gICAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgIDxHZW0gY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LXB1cnBsZS00MDBcIiAvPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+QmV0T2N0YXZlPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS03MDAvNTAgcHgtMyBweS0xIHJvdW5kZWQtbWQgdGV4dC13aGl0ZSB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMFwiPntmb3JtYXRDdXJyZW5jeSh1c2VyLnVzZHRfYmFsYW5jZSl9IDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LW9yYW5nZS00MDBcIj7igr88L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93V2FsbGV0KHRydWUpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNjAwIGhvdmVyOmJnLWJsdWUtNzAwIHRleHQtd2hpdGUgYm9yZGVyLWJsdWUtNjAwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxXYWxsZXQgY2xhc3NOYW1lPVwiaC00IHctNCBzbTptci0yXCIgLz5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJoaWRkZW4gc206aW5saW5lXCI+V2FsbGV0PC9zcGFuPlxuICAgICAgICAgICAgICA8L0J1dHRvbj5cblxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U291bmRFbmFibGVkKCFzb3VuZEVuYWJsZWQpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlci1ncmF5LTUwMCB0ZXh0LWdyYXktNDAwIGhvdmVyOmJnLWdyYXktNjAwIGhvdmVyOnRleHQtd2hpdGVcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge3NvdW5kRW5hYmxlZCA/IDxWb2x1bWUyIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPiA6IDxWb2x1bWVYIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPn1cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2xvZ291dH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXItZ3JheS01MDAgdGV4dC1ncmF5LTQwMCBob3ZlcjpiZy1ncmF5LTYwMCBob3Zlcjp0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxMb2dPdXQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9uYXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9oZWFkZXI+XG5cbiAgICAgIHsvKiBNYWluIENvbnRlbnQgKi99XG4gICAgICA8bWFpbiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBweC00IHB5LTZcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGxnOmdyaWQtY29scy00IGdhcC02XCI+XG4gICAgICAgICAgey8qIExlZnQgU2lkZWJhciAtIFN0YXRzICYgUmVjZW50IFdpbm5lcnMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi0xIHNwYWNlLXktNlwiPlxuICAgICAgICAgICAgey8qIExpdmUgU3RhdGlzdGljcyAqL31cbiAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImJnLWdyYXktODAwLzgwIGJvcmRlci1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJwYi0zXCI+XG4gICAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8VHJlbmRpbmdVcCBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTIgdGV4dC1ncmVlbi00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgTGl2ZSBTdGF0c1xuICAgICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LWdyYXktMzAwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxVc2VycyBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+UGxheWVycyBPbmxpbmU8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNDAwIGZvbnQtc2VtaWJvbGRcIj57bG9iYnlTdGF0cy5wbGF5ZXJzT25saW5lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LWdyYXktMzAwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxUcm9waHkgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPkJpZ2dlc3QgV2luPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy00MDAgZm9udC1zZW1pYm9sZFwiPntmb3JtYXRDdXJyZW5jeShsb2JieVN0YXRzLmJpZ2dlc3RXaW5Ub2RheSl9PC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHRleHQtZ3JheS0zMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPENsb2NrIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc21cIj5CZXRzIFRvZGF5PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNDAwIGZvbnQtc2VtaWJvbGRcIj57bG9iYnlTdGF0cy50b3RhbEJldHNUb2RheS50b0xvY2FsZVN0cmluZygpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgICAgey8qIFJlY2VudCBXaW5uZXJzICovfVxuICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiYmctZ3JheS04MDAvODAgYm9yZGVyLWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cInBiLTNcIj5cbiAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxUcm9waHkgY2xhc3NOYW1lPVwiaC01IHctNSBtci0yIHRleHQteWVsbG93LTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICBSZWNlbnQgV2lubmVyc1xuICAgICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICB7cmVjZW50V2lubmVycy5tYXAoKHdpbm5lcikgPT4gKFxuICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e3dpbm5lci5pZH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtMiBiZy1ncmF5LTcwMC81MCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQtc20gZm9udC1tZWRpdW1cIj57d2lubmVyLnVzZXJuYW1lfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LXhzXCI+e3dpbm5lci5nYW1lfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTQwMCB0ZXh0LXNtIGZvbnQtc2VtaWJvbGRcIj57d2lubmVyLm11bHRpcGxpZXJ9eDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCB0ZXh0LXhzXCI+e2Zvcm1hdEN1cnJlbmN5KHdpbm5lci53aW5BbW91bnQpfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIE1haW4gQ29udGVudCBBcmVhICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6Y29sLXNwYW4tMyBzcGFjZS15LTZcIj5cbiAgICAgICAgICAgIHsvKiBGZWF0dXJlZCBHYW1lcyBDYXJvdXNlbCAqL31cbiAgICAgICAgICAgIHtmZWF0dXJlZEdhbWVzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMC84MCBib3JkZXItZ3JheS02MDAgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwicGItM1wiPlxuICAgICAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxTdGFyIGNsYXNzTmFtZT1cImgtNSB3LTUgbXItMiB0ZXh0LXllbGxvdy00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICBGZWF0dXJlZCBHYW1lc1xuICAgICAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTBcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTUwMCBlYXNlLWluLW91dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyB0cmFuc2Zvcm06IGB0cmFuc2xhdGVYKC0ke2N1cnJlbnRGZWF0dXJlZEluZGV4ICogMTAwfSUpYCB9fVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtmZWF0dXJlZEdhbWVzLm1hcCgoZ2FtZSkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17Z2FtZS5pZH0gY2xhc3NOYW1lPVwidy1mdWxsIGZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1wdXJwbGUtNjAwIHRvLXBpbmstNjAwIHAtNiB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgbWItMlwiPntnYW1lLm5hbWV9PC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXB1cnBsZS0xMDAgbWItNFwiPntnYW1lLmRlc2NyaXB0aW9ufTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNCBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJiZy13aGl0ZS8yMCBweC0yIHB5LTEgcm91bmRlZCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE1heCB7Z2FtZS5tYXhNdWx0aXBsaWVyfXhcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJnLXdoaXRlLzIwIHB4LTIgcHktMSByb3VuZGVkIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgTWluIHtmb3JtYXRDdXJyZW5jeShnYW1lLm1pbkJldCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlR2FtZVNlbGVjdChnYW1lLmlkKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXdoaXRlIHRleHQtcHVycGxlLTYwMCBob3ZlcjpiZy1ncmF5LTEwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFBsYXkgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFBsYXkgTm93XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtNnhsIG9wYWNpdHktMjBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2FtZS5pY29ufVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7ZmVhdHVyZWRHYW1lcy5sZW5ndGggPiAxICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtwcmV2RmVhdHVyZWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMiB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIGJnLWJsYWNrLzIwIGhvdmVyOmJnLWJsYWNrLzQwIHRleHQtd2hpdGVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hldnJvbkxlZnQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17bmV4dEZlYXR1cmVkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSByaWdodC0yIHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteS0xLzIgYmctYmxhY2svMjAgaG92ZXI6YmctYmxhY2svNDAgdGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGV2cm9uUmlnaHQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHsvKiBTZWFyY2ggYW5kIEZpbHRlciAqL31cbiAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImJnLWdyYXktODAwLzgwIGJvcmRlci1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IGdhcC00XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICA8U2VhcmNoIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIGgtNCB3LTQgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VhcmNoIGdhbWVzLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c2VhcmNoUXVlcnl9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWFyY2hRdWVyeShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicGwtMTAgYmctZ3JheS03MDAgYm9yZGVyLWdyYXktNjAwIHRleHQtd2hpdGUgcGxhY2Vob2xkZXItZ3JheS00MDBcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8VGFicyB2YWx1ZT17c2VsZWN0ZWRDYXRlZ29yeX0gb25WYWx1ZUNoYW5nZT17c2V0U2VsZWN0ZWRDYXRlZ29yeX0gY2xhc3NOYW1lPVwidy1mdWxsIHNtOnctYXV0b1wiPlxuICAgICAgICAgICAgICAgICAgICA8VGFic0xpc3QgY2xhc3NOYW1lPVwiYmctZ3JheS03MDAgYm9yZGVyLWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwiYWxsXCIgY2xhc3NOYW1lPVwiZGF0YS1bc3RhdGU9YWN0aXZlXTpiZy1wdXJwbGUtNjAwXCI+QWxsPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJvcmlnaW5hbHNcIiBjbGFzc05hbWU9XCJkYXRhLVtzdGF0ZT1hY3RpdmVdOmJnLXB1cnBsZS02MDBcIj5PcmlnaW5hbHM8L1RhYnNUcmlnZ2VyPlxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cInNsb3RzXCIgY2xhc3NOYW1lPVwiZGF0YS1bc3RhdGU9YWN0aXZlXTpiZy1wdXJwbGUtNjAwXCI+U2xvdHM8L1RhYnNUcmlnZ2VyPlxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cImxpdmVcIiBjbGFzc05hbWU9XCJkYXRhLVtzdGF0ZT1hY3RpdmVdOmJnLXB1cnBsZS02MDBcIj5MaXZlPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJ0YWJsZVwiIGNsYXNzTmFtZT1cImRhdGEtW3N0YXRlPWFjdGl2ZV06YmctcHVycGxlLTYwMFwiPlRhYmxlPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgICAgPC9UYWJzTGlzdD5cbiAgICAgICAgICAgICAgICAgIDwvVGFicz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgICAgey8qIEdhbWVzIEdyaWQgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgc206Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLTRcIj5cbiAgICAgICAgICAgICAge2ZpbHRlcmVkR2FtZXMubWFwKChnYW1lKSA9PiAoXG4gICAgICAgICAgICAgICAgPENhcmRcbiAgICAgICAgICAgICAgICAgIGtleT17Z2FtZS5pZH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYXktODAwLzgwIGJvcmRlci1ncmF5LTYwMCBob3Zlcjpib3JkZXItcHVycGxlLTUwMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgY3Vyc29yLXBvaW50ZXIgZ3JvdXBcIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlR2FtZVNlbGVjdChnYW1lLmlkKX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtM3hsXCI+e2dhbWUuaWNvbn08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2dhbWUuaXNGZWF0dXJlZCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxTdGFyIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC15ZWxsb3ctNDAwIGZpbGwtY3VycmVudFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAge2dhbWUuaXNOZXcgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJiZy1ncmVlbi01MDAgdGV4dC13aGl0ZSB0ZXh0LXhzIHB4LTIgcHktMSByb3VuZGVkXCI+TkVXPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1zZW1pYm9sZCBtYi0yIGdyb3VwLWhvdmVyOnRleHQtcHVycGxlLTQwMCB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICAgICAgICAgIHtnYW1lLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgIDwvaDM+XG5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LXNtIG1iLTMgbGluZS1jbGFtcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2dhbWUuZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICAgIDwvcD5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiB0ZXh0LXhzIHRleHQtZ3JheS01MDAgbWItM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPk1pbjoge2Zvcm1hdEN1cnJlbmN5KGdhbWUubWluQmV0KX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+TWF4OiB7Z2FtZS5tYXhNdWx0aXBsaWVyfXg8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAgZ2FwLTEgbWItM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIHtnYW1lLmZlYXR1cmVzLnNsaWNlKDAsIDIpLm1hcCgoZmVhdHVyZSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYXktNzAwIHRleHQtZ3JheS0zMDAgdGV4dC14cyBweC0yIHB5LTEgcm91bmRlZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtmZWF0dXJlfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLXB1cnBsZS02MDAgaG92ZXI6YmctcHVycGxlLTcwMCB0ZXh0LXdoaXRlIGdyb3VwLWhvdmVyOmJnLXB1cnBsZS01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8UGxheSBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIHsoZ2FtZS5pZCA9PT0gJ21pbmVzJyB8fCBnYW1lLmlkID09PSAnZGljZScpID8gJ1BsYXkgTm93JyA6ICdDb21pbmcgU29vbid9XG4gICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBObyBHYW1lcyBGb3VuZCAqL31cbiAgICAgICAgICAgIHtmaWx0ZXJlZEdhbWVzLmxlbmd0aCA9PT0gMCAmJiAoXG4gICAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImJnLWdyYXktODAwLzgwIGJvcmRlci1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTggdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxTZWFyY2ggY2xhc3NOYW1lPVwiaC0xMiB3LTEyIG14LWF1dG8gbWItNCBvcGFjaXR5LTUwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBtYi0yXCI+Tm8gZ2FtZXMgZm91bmQ8L2gzPlxuICAgICAgICAgICAgICAgICAgICA8cD5UcnkgYWRqdXN0aW5nIHlvdXIgc2VhcmNoIG9yIGZpbHRlciBjcml0ZXJpYTwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICBzZXRTZWFyY2hRdWVyeSgnJyk7XG4gICAgICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRDYXRlZ29yeSgnYWxsJyk7XG4gICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlci1ncmF5LTUwMCB0ZXh0LWdyYXktNDAwIGhvdmVyOmJnLWdyYXktNjAwIGhvdmVyOnRleHQtd2hpdGVcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICBDbGVhciBGaWx0ZXJzXG4gICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvbWFpbj5cblxuICAgICAgey8qIE1vZGFscyAqL31cbiAgICAgIDxXYWxsZXRNb2RhbFxuICAgICAgICB1c2VyPXt1c2VyfVxuICAgICAgICBpc09wZW49e3Nob3dXYWxsZXR9XG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHNldFNob3dXYWxsZXQoZmFsc2UpfVxuICAgICAgICBvbkJhbGFuY2VVcGRhdGU9eygpID0+IHNldFNob3dXYWxsZXQoZmFsc2UpfVxuICAgICAgLz5cblxuICAgICAgey8qIFRvYXN0IE5vdGlmaWNhdGlvbnMgKi99XG4gICAgICA8VG9hc3RlciAvPlxuICAgIDwvZGl2PlxuICApO1xufSJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsInVzZVJvdXRlciIsInVzZUF1dGgiLCJCdXR0b24iLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiSW5wdXQiLCJUYWJzIiwiVGFic0xpc3QiLCJUYWJzVHJpZ2dlciIsIldhbGxldE1vZGFsIiwiR2VtIiwiTG9nT3V0IiwiV2FsbGV0IiwiVm9sdW1lMiIsIlZvbHVtZVgiLCJTZWFyY2giLCJUcmVuZGluZ1VwIiwiVXNlcnMiLCJUcm9waHkiLCJDbG9jayIsIlN0YXIiLCJQbGF5IiwiQ2hldnJvbkxlZnQiLCJDaGV2cm9uUmlnaHQiLCJmb3JtYXRDdXJyZW5jeSIsInNvdW5kTWFuYWdlciIsIlRvYXN0ZXIiLCJ1c2VUb2FzdCIsIkxvYmJ5IiwidXNlciIsImxvZ291dCIsImxvYWRpbmciLCJhdXRoTG9hZGluZyIsInJvdXRlciIsInRvYXN0Iiwic2hvd1dhbGxldCIsInNldFNob3dXYWxsZXQiLCJzb3VuZEVuYWJsZWQiLCJzZXRTb3VuZEVuYWJsZWQiLCJzZWFyY2hRdWVyeSIsInNldFNlYXJjaFF1ZXJ5Iiwic2VsZWN0ZWRDYXRlZ29yeSIsInNldFNlbGVjdGVkQ2F0ZWdvcnkiLCJnYW1lcyIsInNldEdhbWVzIiwiZmVhdHVyZWRHYW1lcyIsInNldEZlYXR1cmVkR2FtZXMiLCJsb2JieVN0YXRzIiwic2V0TG9iYnlTdGF0cyIsInBsYXllcnNPbmxpbmUiLCJ0b3RhbEJldHNUb2RheSIsImJpZ2dlc3RXaW5Ub2RheSIsInRvdGFsR2FtZXNQbGF5ZWQiLCJyZWNlbnRXaW5uZXJzIiwic2V0UmVjZW50V2lubmVycyIsInNldExvYWRpbmciLCJjdXJyZW50RmVhdHVyZWRJbmRleCIsInNldEN1cnJlbnRGZWF0dXJlZEluZGV4IiwicHVzaCIsInNldEVuYWJsZWQiLCJsb2FkTG9iYnlEYXRhIiwibGVuZ3RoIiwiaW50ZXJ2YWwiLCJzZXRJbnRlcnZhbCIsInByZXYiLCJjbGVhckludGVydmFsIiwiZ2FtZXNSZXNwb25zZSIsImZldGNoIiwiZ2FtZXNEYXRhIiwianNvbiIsInN1Y2Nlc3MiLCJmaWx0ZXIiLCJnYW1lIiwiaXNGZWF0dXJlZCIsInN0YXRzUmVzcG9uc2UiLCJzdGF0c0RhdGEiLCJzdGF0cyIsIk1hdGgiLCJmbG9vciIsInJhbmRvbSIsImVycm9yIiwiY29uc29sZSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJ2YXJpYW50IiwiaGFuZGxlR2FtZVNlbGVjdCIsImdhbWVJZCIsInBsYXkiLCJmaWx0ZXJlZEdhbWVzIiwibWF0Y2hlc1NlYXJjaCIsIm5hbWUiLCJ0b0xvd2VyQ2FzZSIsImluY2x1ZGVzIiwibWF0Y2hlc0NhdGVnb3J5IiwiY2F0ZWdvcnkiLCJuZXh0RmVhdHVyZWQiLCJwcmV2RmVhdHVyZWQiLCJkaXYiLCJjbGFzc05hbWUiLCJoZWFkZXIiLCJuYXYiLCJzcGFuIiwidXNkdF9iYWxhbmNlIiwic2l6ZSIsIm9uQ2xpY2siLCJtYWluIiwidG9Mb2NhbGVTdHJpbmciLCJtYXAiLCJ3aW5uZXIiLCJ1c2VybmFtZSIsIm11bHRpcGxpZXIiLCJ3aW5BbW91bnQiLCJpZCIsInN0eWxlIiwidHJhbnNmb3JtIiwiaDMiLCJwIiwibWF4TXVsdGlwbGllciIsIm1pbkJldCIsImljb24iLCJwbGFjZWhvbGRlciIsInZhbHVlIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0Iiwib25WYWx1ZUNoYW5nZSIsImlzTmV3IiwiZmVhdHVyZXMiLCJzbGljZSIsImZlYXR1cmUiLCJpbmRleCIsImlzT3BlbiIsIm9uQ2xvc2UiLCJvbkJhbGFuY2VVcGRhdGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/lobby.tsx\n"));

/***/ })

});