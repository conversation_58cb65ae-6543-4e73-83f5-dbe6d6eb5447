"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/lobby",{

/***/ "(pages-dir-browser)/./pages/lobby.tsx":
/*!*************************!*\
  !*** ./pages/lobby.tsx ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Lobby)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(pages-dir-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(pages-dir-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(pages-dir-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(pages-dir-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/tabs */ \"(pages-dir-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_wallet_WalletModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/wallet/WalletModal */ \"(pages-dir-browser)/./components/wallet/WalletModal.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,Gem,LogOut,Play,Search,Star,TrendingUp,Trophy,Users,Volume2,VolumeX,Wallet!=!lucide-react */ \"(pages-dir-browser)/__barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,Gem,LogOut,Play,Search,Star,TrendingUp,Trophy,Users,Volume2,VolumeX,Wallet!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(pages-dir-browser)/./lib/utils.ts\");\n/* harmony import */ var _lib_sounds__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/sounds */ \"(pages-dir-browser)/./lib/sounds.ts\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/toaster */ \"(pages-dir-browser)/./components/ui/toaster.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(pages-dir-browser)/./components/ui/use-toast.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Lobby() {\n    _s();\n    const { user, logout, loading: authLoading, refreshUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast)();\n    // UI state\n    const [showWallet, setShowWallet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [soundEnabled, setSoundEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    // Data state\n    const [games, setGames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [featuredGames, setFeaturedGames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [lobbyStats, setLobbyStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        playersOnline: 0,\n        totalBetsToday: 0,\n        biggestWinToday: 0,\n        totalGamesPlayed: 0\n    });\n    const [recentWinners, setRecentWinners] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Carousel state\n    const [currentFeaturedIndex, setCurrentFeaturedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Lobby.useEffect\": ()=>{\n            if (!authLoading && !user) {\n                router.push('/login');\n            }\n        }\n    }[\"Lobby.useEffect\"], [\n        user,\n        authLoading,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Lobby.useEffect\": ()=>{\n            _lib_sounds__WEBPACK_IMPORTED_MODULE_10__.soundManager.setEnabled(soundEnabled);\n        }\n    }[\"Lobby.useEffect\"], [\n        soundEnabled\n    ]);\n    // Load games and lobby data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Lobby.useEffect\": ()=>{\n            if (user) {\n                loadLobbyData();\n            }\n        }\n    }[\"Lobby.useEffect\"], [\n        user\n    ]);\n    // Auto-rotate featured games carousel\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Lobby.useEffect\": ()=>{\n            if (featuredGames.length > 1) {\n                const interval = setInterval({\n                    \"Lobby.useEffect.interval\": ()=>{\n                        setCurrentFeaturedIndex({\n                            \"Lobby.useEffect.interval\": (prev)=>prev === featuredGames.length - 1 ? 0 : prev + 1\n                        }[\"Lobby.useEffect.interval\"]);\n                    }\n                }[\"Lobby.useEffect.interval\"], 5000);\n                return ({\n                    \"Lobby.useEffect\": ()=>clearInterval(interval)\n                })[\"Lobby.useEffect\"];\n            }\n        }\n    }[\"Lobby.useEffect\"], [\n        featuredGames.length\n    ]);\n    const loadLobbyData = async ()=>{\n        try {\n            setLoading(true);\n            // Load available games\n            const gamesResponse = await fetch('/api/game/list?active=true');\n            const gamesData = await gamesResponse.json();\n            if (gamesData.success) {\n                setGames(gamesData.games);\n                setFeaturedGames(gamesData.games.filter((game)=>game.isFeatured));\n            }\n            // Load lobby statistics\n            const statsResponse = await fetch('/api/lobby/stats');\n            const statsData = await statsResponse.json();\n            if (statsData.success) {\n                setLobbyStats({\n                    playersOnline: statsData.stats.playersOnline,\n                    totalBetsToday: statsData.stats.totalBetsToday,\n                    biggestWinToday: statsData.stats.biggestWinToday,\n                    totalGamesPlayed: statsData.stats.totalGamesPlayed\n                });\n                setRecentWinners(statsData.stats.recentWinners);\n            } else {\n                // Fallback to mock data if API fails\n                setLobbyStats({\n                    playersOnline: Math.floor(Math.random() * 500) + 100,\n                    totalBetsToday: Math.floor(Math.random() * 10000) + 5000,\n                    biggestWinToday: Math.floor(Math.random() * 50000) + 10000,\n                    totalGamesPlayed: Math.floor(Math.random() * 100000) + 50000\n                });\n                setRecentWinners([]);\n            }\n        } catch (error) {\n            console.error('Failed to load lobby data:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load lobby data\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleGameSelect = (gameId)=>{\n        _lib_sounds__WEBPACK_IMPORTED_MODULE_10__.soundManager.play('click');\n        if (gameId === 'mines') {\n            router.push('/game/mines');\n        } else if (gameId === 'dice') {\n            router.push('/game/dice');\n        } else {\n            toast({\n                title: \"Coming Soon\",\n                description: \"\".concat(gameId, \" will be available soon!\"),\n                variant: \"info\"\n            });\n        }\n    };\n    const handleAddFunds = async ()=>{\n        try {\n            const response = await fetch('/api/test/add-balance', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    amount: 100\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Refresh user data to get updated balance\n                await refreshUser();\n                toast({\n                    title: \"Funds Added!\",\n                    description: \"Successfully added $100 to your balance\",\n                    variant: \"default\"\n                });\n                _lib_sounds__WEBPACK_IMPORTED_MODULE_10__.soundManager.play('win');\n            } else {\n                throw new Error(data.error || 'Failed to add funds');\n            }\n        } catch (error) {\n            console.error('Failed to add funds:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to add funds. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const filteredGames = games.filter((game)=>{\n        const matchesSearch = game.name.toLowerCase().includes(searchQuery.toLowerCase()) || game.description.toLowerCase().includes(searchQuery.toLowerCase());\n        const matchesCategory = selectedCategory === 'all' || game.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    const nextFeatured = ()=>{\n        setCurrentFeaturedIndex((prev)=>prev === featuredGames.length - 1 ? 0 : prev + 1);\n    };\n    const prevFeatured = ()=>{\n        setCurrentFeaturedIndex((prev)=>prev === 0 ? featuredGames.length - 1 : prev - 1);\n    };\n    if (authLoading || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-white\"\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                lineNumber: 225,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n            lineNumber: 224,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null; // Will redirect to login\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gray-800/50 border-b border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Gem, {\n                                        className: \"h-6 w-6 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-white\",\n                                        children: \"BetOctave\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-700/50 px-3 py-1 rounded-md text-white text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-300\",\n                                                children: [\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(user.usdt_balance),\n                                                    \" \"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-orange-400\",\n                                                children: \"₿\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowWallet(true),\n                                        className: \"bg-blue-600 hover:bg-blue-700 text-white border-blue-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Wallet, {\n                                                className: \"h-4 w-4 sm:mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Wallet\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSoundEnabled(!soundEnabled),\n                                        className: \"border-gray-500 text-gray-400 hover:bg-gray-600 hover:text-white\",\n                                        children: soundEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Volume2, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 33\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.VolumeX, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 67\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: logout,\n                                        className: \"border-gray-500 text-gray-400 hover:bg-gray-600 hover:text-white\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.LogOut, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"bg-gray-800/80 border-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            className: \"pb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-white flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.TrendingUp, {\n                                                        className: \"h-5 w-5 mr-2 text-green-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Live Stats\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Users, {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 299,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"Players Online\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400 font-semibold\",\n                                                            children: lobbyStats.playersOnline\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Trophy, {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 306,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"Biggest Win\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 307,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-yellow-400 font-semibold\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(lobbyStats.biggestWinToday)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Clock, {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 313,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"Bets Today\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-400 font-semibold\",\n                                                            children: lobbyStats.totalBetsToday.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"bg-gray-800/80 border-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            className: \"pb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-white flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Trophy, {\n                                                        className: \"h-5 w-5 mr-2 text-yellow-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Recent Winners\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-3\",\n                                            children: recentWinners.map((winner)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-2 bg-gray-700/50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white text-sm font-medium\",\n                                                                    children: winner.username\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-400 text-xs\",\n                                                                    children: winner.game\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-green-400 text-sm font-semibold\",\n                                                                    children: [\n                                                                        winner.multiplier,\n                                                                        \"x\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-300 text-xs\",\n                                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(winner.winAmount)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, winner.id, true, {\n                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3 space-y-6\",\n                            children: [\n                                featuredGames.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"bg-gray-800/80 border-gray-600 overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            className: \"pb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-white flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Star, {\n                                                        className: \"h-5 w-5 mr-2 text-yellow-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Featured Games\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"p-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"overflow-hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex transition-transform duration-500 ease-in-out\",\n                                                            style: {\n                                                                transform: \"translateX(-\".concat(currentFeaturedIndex * 100, \"%)\")\n                                                            },\n                                                            children: featuredGames.map((game)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full flex-shrink-0\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative bg-gradient-to-r from-purple-600 to-pink-600 p-6 text-white\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                            className: \"text-2xl font-bold mb-2\",\n                                                                                            children: game.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                            lineNumber: 369,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-purple-100 mb-4\",\n                                                                                            children: game.description\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                            lineNumber: 370,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-4 mb-4\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"bg-white/20 px-2 py-1 rounded text-sm\",\n                                                                                                    children: [\n                                                                                                        \"Max \",\n                                                                                                        game.maxMultiplier,\n                                                                                                        \"x\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                                    lineNumber: 372,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"bg-white/20 px-2 py-1 rounded text-sm\",\n                                                                                                    children: [\n                                                                                                        \"Min \",\n                                                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(game.minBet)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                                    lineNumber: 375,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                            lineNumber: 371,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                            onClick: ()=>handleGameSelect(game.id),\n                                                                                            className: \"bg-white text-purple-600 hover:bg-gray-100\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Play, {\n                                                                                                    className: \"h-4 w-4 mr-2\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                                    lineNumber: 383,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                \"Play Now\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                            lineNumber: 379,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                    lineNumber: 368,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-6xl opacity-20\",\n                                                                                    children: game.icon\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                                    lineNumber: 387,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                            lineNumber: 367,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                        lineNumber: 366,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, game.id, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    featuredGames.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                onClick: prevFeatured,\n                                                                className: \"absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/20 hover:bg-black/40 text-white\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.ChevronLeft, {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 405,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 399,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                onClick: nextFeatured,\n                                                                className: \"absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/20 hover:bg-black/40 text-white\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.ChevronRight, {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                    lineNumber: 413,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"bg-gray-800/80 border-gray-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Search, {\n                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            placeholder: \"Search games...\",\n                                                            value: searchQuery,\n                                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                                            className: \"pl-10 bg-gray-700 border-gray-600 text-white placeholder-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.Tabs, {\n                                                    value: selectedCategory,\n                                                    onValueChange: setSelectedCategory,\n                                                    className: \"w-full sm:w-auto\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsList, {\n                                                        className: \"bg-gray-700 border-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                                                value: \"all\",\n                                                                className: \"data-[state=active]:bg-purple-600\",\n                                                                children: \"All\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                                                value: \"originals\",\n                                                                className: \"data-[state=active]:bg-purple-600\",\n                                                                children: \"Originals\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 438,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                                                value: \"slots\",\n                                                                className: \"data-[state=active]:bg-purple-600\",\n                                                                children: \"Slots\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                                                value: \"live\",\n                                                                className: \"data-[state=active]:bg-purple-600\",\n                                                                children: \"Live\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                                                value: \"table\",\n                                                                className: \"data-[state=active]:bg-purple-600\",\n                                                                children: \"Table\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                    children: filteredGames.map((game)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                            className: \"bg-gray-800/80 border-gray-600 hover:border-purple-500 transition-all duration-200 cursor-pointer group\",\n                                            onClick: ()=>handleGameSelect(game.id),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-3xl\",\n                                                                children: game.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    game.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Star, {\n                                                                        className: \"h-4 w-4 text-yellow-400 fill-current\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                        lineNumber: 461,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    game.isNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"bg-green-500 text-white text-xs px-2 py-1 rounded\",\n                                                                        children: \"NEW\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                        lineNumber: 464,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-white font-semibold mb-2 group-hover:text-purple-400 transition-colors\",\n                                                        children: game.name\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm mb-3 line-clamp-2\",\n                                                        children: game.description\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between text-xs text-gray-500 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Min: \",\n                                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(game.minBet)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Max: \",\n                                                                    game.maxMultiplier,\n                                                                    \"x\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1 mb-3\",\n                                                        children: game.features.slice(0, 2).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded\",\n                                                                children: feature\n                                                            }, index, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 484,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        className: \"w-full bg-purple-600 hover:bg-purple-700 text-white group-hover:bg-purple-500\",\n                                                        size: \"sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Play, {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            game.id === 'mines' || game.id === 'dice' ? 'Play Now' : 'Coming Soon'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 493,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, game.id, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, this),\n                                filteredGames.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"bg-gray-800/80 border-gray-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"p-8 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-400 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_Gem_LogOut_Play_Search_Star_TrendingUp_Trophy_Users_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Search, {\n                                                        className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 510,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-2\",\n                                                        children: \"No games found\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 511,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Try adjusting your search or filter criteria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>{\n                                                    setSearchQuery('');\n                                                    setSelectedCategory('all');\n                                                },\n                                                className: \"border-gray-500 text-gray-400 hover:bg-gray-600 hover:text-white\",\n                                                children: \"Clear Filters\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                lineNumber: 284,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallet_WalletModal__WEBPACK_IMPORTED_MODULE_8__.WalletModal, {\n                user: user,\n                isOpen: showWallet,\n                onClose: ()=>setShowWallet(false),\n                onBalanceUpdate: ()=>setShowWallet(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                lineNumber: 532,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_11__.Toaster, {}, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n                lineNumber: 540,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\lobby.tsx\",\n        lineNumber: 235,\n        columnNumber: 5\n    }, this);\n}\n_s(Lobby, \"0yhwNJPYqg8QKQXyNYnAuIniKGs=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast\n    ];\n});\n_c = Lobby;\nvar _c;\n$RefreshReg$(_c, \"Lobby\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL3BhZ2VzL2xvYmJ5LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUE0QztBQUNKO0FBQ1M7QUFDRDtBQUNpRDtBQUNuRDtBQUNrQztBQUNsQjtBQWlCeEM7QUFDdUI7QUFDRDtBQUNNO0FBQ0c7QUFtQnRDLFNBQVNnQzs7SUFDdEIsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLE1BQU0sRUFBRUMsU0FBU0MsV0FBVyxFQUFFQyxXQUFXLEVBQUUsR0FBR2xDLDhEQUFPQTtJQUNuRSxNQUFNbUMsU0FBU3BDLHNEQUFTQTtJQUN4QixNQUFNLEVBQUVxQyxLQUFLLEVBQUUsR0FBR1IsbUVBQVFBO0lBRTFCLFdBQVc7SUFDWCxNQUFNLENBQUNTLFlBQVlDLGNBQWMsR0FBR3hDLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ3lDLGNBQWNDLGdCQUFnQixHQUFHMUMsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDMkMsYUFBYUMsZUFBZSxHQUFHNUMsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDNkMsa0JBQWtCQyxvQkFBb0IsR0FBRzlDLCtDQUFRQSxDQUFDO0lBRXpELGFBQWE7SUFDYixNQUFNLENBQUMrQyxPQUFPQyxTQUFTLEdBQUdoRCwrQ0FBUUEsQ0FBZSxFQUFFO0lBQ25ELE1BQU0sQ0FBQ2lELGVBQWVDLGlCQUFpQixHQUFHbEQsK0NBQVFBLENBQWUsRUFBRTtJQUNuRSxNQUFNLENBQUNtRCxZQUFZQyxjQUFjLEdBQUdwRCwrQ0FBUUEsQ0FBYTtRQUN2RHFELGVBQWU7UUFDZkMsZ0JBQWdCO1FBQ2hCQyxpQkFBaUI7UUFDakJDLGtCQUFrQjtJQUNwQjtJQUNBLE1BQU0sQ0FBQ0MsZUFBZUMsaUJBQWlCLEdBQUcxRCwrQ0FBUUEsQ0FBaUIsRUFBRTtJQUNyRSxNQUFNLENBQUNrQyxTQUFTeUIsV0FBVyxHQUFHM0QsK0NBQVFBLENBQUM7SUFFdkMsaUJBQWlCO0lBQ2pCLE1BQU0sQ0FBQzRELHNCQUFzQkMsd0JBQXdCLEdBQUc3RCwrQ0FBUUEsQ0FBQztJQUVqRUQsZ0RBQVNBOzJCQUFDO1lBQ1IsSUFBSSxDQUFDb0MsZUFBZSxDQUFDSCxNQUFNO2dCQUN6QkssT0FBT3lCLElBQUksQ0FBQztZQUNkO1FBQ0Y7MEJBQUc7UUFBQzlCO1FBQU1HO1FBQWFFO0tBQU87SUFFOUJ0QyxnREFBU0E7MkJBQUM7WUFDUjZCLHNEQUFZQSxDQUFDbUMsVUFBVSxDQUFDdEI7UUFDMUI7MEJBQUc7UUFBQ0E7S0FBYTtJQUVqQiw0QkFBNEI7SUFDNUIxQyxnREFBU0E7MkJBQUM7WUFDUixJQUFJaUMsTUFBTTtnQkFDUmdDO1lBQ0Y7UUFDRjswQkFBRztRQUFDaEM7S0FBSztJQUVULHNDQUFzQztJQUN0Q2pDLGdEQUFTQTsyQkFBQztZQUNSLElBQUlrRCxjQUFjZ0IsTUFBTSxHQUFHLEdBQUc7Z0JBQzVCLE1BQU1DLFdBQVdDO2dEQUFZO3dCQUMzQk47d0RBQXdCLENBQUNPLE9BQ3ZCQSxTQUFTbkIsY0FBY2dCLE1BQU0sR0FBRyxJQUFJLElBQUlHLE9BQU87O29CQUVuRDsrQ0FBRztnQkFDSDt1Q0FBTyxJQUFNQyxjQUFjSDs7WUFDN0I7UUFDRjswQkFBRztRQUFDakIsY0FBY2dCLE1BQU07S0FBQztJQUV6QixNQUFNRCxnQkFBZ0I7UUFDcEIsSUFBSTtZQUNGTCxXQUFXO1lBRVgsdUJBQXVCO1lBQ3ZCLE1BQU1XLGdCQUFnQixNQUFNQyxNQUFNO1lBQ2xDLE1BQU1DLFlBQVksTUFBTUYsY0FBY0csSUFBSTtZQUUxQyxJQUFJRCxVQUFVRSxPQUFPLEVBQUU7Z0JBQ3JCMUIsU0FBU3dCLFVBQVV6QixLQUFLO2dCQUN4QkcsaUJBQWlCc0IsVUFBVXpCLEtBQUssQ0FBQzRCLE1BQU0sQ0FBQyxDQUFDQyxPQUFxQkEsS0FBS0MsVUFBVTtZQUMvRTtZQUVBLHdCQUF3QjtZQUN4QixNQUFNQyxnQkFBZ0IsTUFBTVAsTUFBTTtZQUNsQyxNQUFNUSxZQUFZLE1BQU1ELGNBQWNMLElBQUk7WUFFMUMsSUFBSU0sVUFBVUwsT0FBTyxFQUFFO2dCQUNyQnRCLGNBQWM7b0JBQ1pDLGVBQWUwQixVQUFVQyxLQUFLLENBQUMzQixhQUFhO29CQUM1Q0MsZ0JBQWdCeUIsVUFBVUMsS0FBSyxDQUFDMUIsY0FBYztvQkFDOUNDLGlCQUFpQndCLFVBQVVDLEtBQUssQ0FBQ3pCLGVBQWU7b0JBQ2hEQyxrQkFBa0J1QixVQUFVQyxLQUFLLENBQUN4QixnQkFBZ0I7Z0JBQ3BEO2dCQUNBRSxpQkFBaUJxQixVQUFVQyxLQUFLLENBQUN2QixhQUFhO1lBQ2hELE9BQU87Z0JBQ0wscUNBQXFDO2dCQUNyQ0wsY0FBYztvQkFDWkMsZUFBZTRCLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsTUFBTSxLQUFLLE9BQU87b0JBQ2pEN0IsZ0JBQWdCMkIsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUssU0FBUztvQkFDcEQ1QixpQkFBaUIwQixLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBSyxTQUFTO29CQUNyRDNCLGtCQUFrQnlCLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsTUFBTSxLQUFLLFVBQVU7Z0JBQ3pEO2dCQUNBekIsaUJBQWlCLEVBQUU7WUFDckI7UUFFRixFQUFFLE9BQU8wQixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw4QkFBOEJBO1lBQzVDOUMsTUFBTTtnQkFDSmdELE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDWDtRQUNGLFNBQVU7WUFDUjdCLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTThCLG1CQUFtQixDQUFDQztRQUN4QjlELHNEQUFZQSxDQUFDK0QsSUFBSSxDQUFDO1FBRWxCLElBQUlELFdBQVcsU0FBUztZQUN0QnJELE9BQU95QixJQUFJLENBQUM7UUFDZCxPQUFPLElBQUk0QixXQUFXLFFBQVE7WUFDNUJyRCxPQUFPeUIsSUFBSSxDQUFDO1FBQ2QsT0FBTztZQUNMeEIsTUFBTTtnQkFDSmdELE9BQU87Z0JBQ1BDLGFBQWEsR0FBVSxPQUFQRyxRQUFPO2dCQUN2QkYsU0FBUztZQUNYO1FBQ0Y7SUFDRjtJQUVBLE1BQU1JLGlCQUFpQjtRQUNyQixJQUFJO1lBQ0YsTUFBTUMsV0FBVyxNQUFNdEIsTUFBTSx5QkFBeUI7Z0JBQ3BEdUIsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQUVDLFFBQVE7Z0JBQUk7WUFDckM7WUFFQSxNQUFNQyxPQUFPLE1BQU1QLFNBQVNwQixJQUFJO1lBRWhDLElBQUkyQixLQUFLMUIsT0FBTyxFQUFFO2dCQUNoQiwyQ0FBMkM7Z0JBQzNDLE1BQU10QztnQkFFTkUsTUFBTTtvQkFDSmdELE9BQU87b0JBQ1BDLGFBQWE7b0JBQ2JDLFNBQVM7Z0JBQ1g7Z0JBRUE1RCxzREFBWUEsQ0FBQytELElBQUksQ0FBQztZQUNwQixPQUFPO2dCQUNMLE1BQU0sSUFBSVUsTUFBTUQsS0FBS2hCLEtBQUssSUFBSTtZQUNoQztRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsd0JBQXdCQTtZQUN0QzlDLE1BQU07Z0JBQ0pnRCxPQUFPO2dCQUNQQyxhQUFhO2dCQUNiQyxTQUFTO1lBQ1g7UUFDRjtJQUNGO0lBRUEsTUFBTWMsZ0JBQWdCdkQsTUFBTTRCLE1BQU0sQ0FBQ0MsQ0FBQUE7UUFDakMsTUFBTTJCLGdCQUFnQjNCLEtBQUs0QixJQUFJLENBQUNDLFdBQVcsR0FBR0MsUUFBUSxDQUFDL0QsWUFBWThELFdBQVcsT0FDekQ3QixLQUFLVyxXQUFXLENBQUNrQixXQUFXLEdBQUdDLFFBQVEsQ0FBQy9ELFlBQVk4RCxXQUFXO1FBQ3BGLE1BQU1FLGtCQUFrQjlELHFCQUFxQixTQUFTK0IsS0FBS2dDLFFBQVEsS0FBSy9EO1FBQ3hFLE9BQU8wRCxpQkFBaUJJO0lBQzFCO0lBRUEsTUFBTUUsZUFBZTtRQUNuQmhELHdCQUF3QixDQUFDTyxPQUN2QkEsU0FBU25CLGNBQWNnQixNQUFNLEdBQUcsSUFBSSxJQUFJRyxPQUFPO0lBRW5EO0lBRUEsTUFBTTBDLGVBQWU7UUFDbkJqRCx3QkFBd0IsQ0FBQ08sT0FDdkJBLFNBQVMsSUFBSW5CLGNBQWNnQixNQUFNLEdBQUcsSUFBSUcsT0FBTztJQUVuRDtJQUVBLElBQUlqQyxlQUFlRCxTQUFTO1FBQzFCLHFCQUNFLDhEQUFDNkU7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7Ozs7Ozs7Ozs7O0lBR3JCO0lBRUEsSUFBSSxDQUFDaEYsTUFBTTtRQUNULE9BQU8sTUFBTSx5QkFBeUI7SUFDeEM7SUFFQSxxQkFDRSw4REFBQytFO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDQztnQkFBT0QsV0FBVTswQkFDaEIsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRTt3QkFBSUYsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ25HLCtLQUFHQTt3Q0FBQ21HLFdBQVU7Ozs7OztrREFDZiw4REFBQ0c7d0NBQUtILFdBQVU7a0RBQStCOzs7Ozs7Ozs7Ozs7MENBR2pELDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0c7Z0RBQUtILFdBQVU7O29EQUFpQnJGLDBEQUFjQSxDQUFDSyxLQUFLb0YsWUFBWTtvREFBRTs7Ozs7OzswREFDbkUsOERBQUNEO2dEQUFLSCxXQUFVOzBEQUFrQjs7Ozs7Ozs7Ozs7O2tEQUdwQyw4REFBQzdHLHlEQUFNQTt3Q0FDTHFGLFNBQVE7d0NBQ1I2QixNQUFLO3dDQUNMQyxTQUFTLElBQU05RSxjQUFjO3dDQUM3QndFLFdBQVU7OzBEQUVWLDhEQUFDakcsa0xBQU1BO2dEQUFDaUcsV0FBVTs7Ozs7OzBEQUNsQiw4REFBQ0c7Z0RBQUtILFdBQVU7MERBQW1COzs7Ozs7Ozs7Ozs7a0RBR3JDLDhEQUFDN0cseURBQU1BO3dDQUNMcUYsU0FBUTt3Q0FDUjZCLE1BQUs7d0NBQ0xDLFNBQVMsSUFBTTVFLGdCQUFnQixDQUFDRDt3Q0FDaEN1RSxXQUFVO2tEQUVUdkUsNkJBQWUsOERBQUN6QixtTEFBT0E7NENBQUNnRyxXQUFVOzs7OztpRUFBZSw4REFBQy9GLG1MQUFPQTs0Q0FBQytGLFdBQVU7Ozs7Ozs7Ozs7O2tEQUd2RSw4REFBQzdHLHlEQUFNQTt3Q0FDTHFGLFNBQVE7d0NBQ1I2QixNQUFLO3dDQUNMQyxTQUFTckY7d0NBQ1QrRSxXQUFVO2tEQUVWLDRFQUFDbEcsa0xBQU1BOzRDQUFDa0csV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVE1Qiw4REFBQ087Z0JBQUtQLFdBQVU7MEJBQ2QsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FFYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUViLDhEQUFDNUcscURBQUlBO29DQUFDNEcsV0FBVTs7c0RBQ2QsOERBQUMxRywyREFBVUE7NENBQUMwRyxXQUFVO3NEQUNwQiw0RUFBQ3pHLDBEQUFTQTtnREFBQ3lHLFdBQVU7O2tFQUNuQiw4REFBQzdGLHNMQUFVQTt3REFBQzZGLFdBQVU7Ozs7OztvREFBZ0M7Ozs7Ozs7Ozs7OztzREFJMUQsOERBQUMzRyw0REFBV0E7NENBQUMyRyxXQUFVOzs4REFDckIsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDNUYsaUxBQUtBO29FQUFDNEYsV0FBVTs7Ozs7OzhFQUNqQiw4REFBQ0c7b0VBQUtILFdBQVU7OEVBQVU7Ozs7Ozs7Ozs7OztzRUFFNUIsOERBQUNHOzREQUFLSCxXQUFVO3NFQUFnQzdELFdBQVdFLGFBQWE7Ozs7Ozs7Ozs7Ozs4REFFMUUsOERBQUMwRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQzNGLGtMQUFNQTtvRUFBQzJGLFdBQVU7Ozs7Ozs4RUFDbEIsOERBQUNHO29FQUFLSCxXQUFVOzhFQUFVOzs7Ozs7Ozs7Ozs7c0VBRTVCLDhEQUFDRzs0REFBS0gsV0FBVTtzRUFBaUNyRiwwREFBY0EsQ0FBQ3dCLFdBQVdJLGVBQWU7Ozs7Ozs7Ozs7Ozs4REFFNUYsOERBQUN3RDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQzFGLGlMQUFLQTtvRUFBQzBGLFdBQVU7Ozs7Ozs4RUFDakIsOERBQUNHO29FQUFLSCxXQUFVOzhFQUFVOzs7Ozs7Ozs7Ozs7c0VBRTVCLDhEQUFDRzs0REFBS0gsV0FBVTtzRUFBK0I3RCxXQUFXRyxjQUFjLENBQUNrRSxjQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBTTdGLDhEQUFDcEgscURBQUlBO29DQUFDNEcsV0FBVTs7c0RBQ2QsOERBQUMxRywyREFBVUE7NENBQUMwRyxXQUFVO3NEQUNwQiw0RUFBQ3pHLDBEQUFTQTtnREFBQ3lHLFdBQVU7O2tFQUNuQiw4REFBQzNGLGtMQUFNQTt3REFBQzJGLFdBQVU7Ozs7OztvREFBaUM7Ozs7Ozs7Ozs7OztzREFJdkQsOERBQUMzRyw0REFBV0E7NENBQUMyRyxXQUFVO3NEQUNwQnZELGNBQWNnRSxHQUFHLENBQUMsQ0FBQ0MsdUJBQ2xCLDhEQUFDWDtvREFBb0JDLFdBQVU7O3NFQUM3Qiw4REFBQ0Q7OzhFQUNDLDhEQUFDQTtvRUFBSUMsV0FBVTs4RUFBa0NVLE9BQU9DLFFBQVE7Ozs7Ozs4RUFDaEUsOERBQUNaO29FQUFJQyxXQUFVOzhFQUF5QlUsT0FBTzlDLElBQUk7Ozs7Ozs7Ozs7OztzRUFFckQsOERBQUNtQzs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEO29FQUFJQyxXQUFVOzt3RUFBd0NVLE9BQU9FLFVBQVU7d0VBQUM7Ozs7Ozs7OEVBQ3pFLDhEQUFDYjtvRUFBSUMsV0FBVTs4RUFBeUJyRiwwREFBY0EsQ0FBQytGLE9BQU9HLFNBQVM7Ozs7Ozs7Ozs7Ozs7bURBUGpFSCxPQUFPSSxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQWdCM0IsOERBQUNmOzRCQUFJQyxXQUFVOztnQ0FFWi9ELGNBQWNnQixNQUFNLEdBQUcsbUJBQ3RCLDhEQUFDN0QscURBQUlBO29DQUFDNEcsV0FBVTs7c0RBQ2QsOERBQUMxRywyREFBVUE7NENBQUMwRyxXQUFVO3NEQUNwQiw0RUFBQ3pHLDBEQUFTQTtnREFBQ3lHLFdBQVU7O2tFQUNuQiw4REFBQ3pGLGdMQUFJQTt3REFBQ3lGLFdBQVU7Ozs7OztvREFBaUM7Ozs7Ozs7Ozs7OztzREFJckQsOERBQUMzRyw0REFBV0E7NENBQUMyRyxXQUFVO3NEQUNyQiw0RUFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTtrRUFDYiw0RUFBQ0Q7NERBQ0NDLFdBQVU7NERBQ1ZlLE9BQU87Z0VBQUVDLFdBQVcsZUFBMEMsT0FBM0JwRSx1QkFBdUIsS0FBSTs0REFBSTtzRUFFakVYLGNBQWN3RSxHQUFHLENBQUMsQ0FBQzdDLHFCQUNsQiw4REFBQ21DO29FQUFrQkMsV0FBVTs4RUFDM0IsNEVBQUNEO3dFQUFJQyxXQUFVO2tGQUNiLDRFQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUNEOztzR0FDQyw4REFBQ2tCOzRGQUFHakIsV0FBVTtzR0FBMkJwQyxLQUFLNEIsSUFBSTs7Ozs7O3NHQUNsRCw4REFBQzBCOzRGQUFFbEIsV0FBVTtzR0FBd0JwQyxLQUFLVyxXQUFXOzs7Ozs7c0dBQ3JELDhEQUFDd0I7NEZBQUlDLFdBQVU7OzhHQUNiLDhEQUFDRztvR0FBS0gsV0FBVTs7d0dBQXdDO3dHQUNqRHBDLEtBQUt1RCxhQUFhO3dHQUFDOzs7Ozs7OzhHQUUxQiw4REFBQ2hCO29HQUFLSCxXQUFVOzt3R0FBd0M7d0dBQ2pEckYsMERBQWNBLENBQUNpRCxLQUFLd0QsTUFBTTs7Ozs7Ozs7Ozs7OztzR0FHbkMsOERBQUNqSSx5REFBTUE7NEZBQ0xtSCxTQUFTLElBQU03QixpQkFBaUJiLEtBQUtrRCxFQUFFOzRGQUN2Q2QsV0FBVTs7OEdBRVYsOERBQUN4RixnTEFBSUE7b0dBQUN3RixXQUFVOzs7Ozs7Z0dBQWlCOzs7Ozs7Ozs7Ozs7OzhGQUlyQyw4REFBQ0Q7b0ZBQUlDLFdBQVU7OEZBQ1pwQyxLQUFLeUQsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7bUVBdkJSekQsS0FBS2tELEVBQUU7Ozs7Ozs7Ozs7Ozs7OztvREFnQ3RCN0UsY0FBY2dCLE1BQU0sR0FBRyxtQkFDdEI7OzBFQUNFLDhEQUFDOUQseURBQU1BO2dFQUNMcUYsU0FBUTtnRUFDUjZCLE1BQUs7Z0VBQ0xDLFNBQVNSO2dFQUNURSxXQUFVOzBFQUVWLDRFQUFDdkYsdUxBQVdBO29FQUFDdUYsV0FBVTs7Ozs7Ozs7Ozs7MEVBRXpCLDhEQUFDN0cseURBQU1BO2dFQUNMcUYsU0FBUTtnRUFDUjZCLE1BQUs7Z0VBQ0xDLFNBQVNUO2dFQUNURyxXQUFVOzBFQUVWLDRFQUFDdEYsd0xBQVlBO29FQUFDc0YsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQVV0Qyw4REFBQzVHLHFEQUFJQTtvQ0FBQzRHLFdBQVU7OENBQ2QsNEVBQUMzRyw0REFBV0E7d0NBQUMyRyxXQUFVO2tEQUNyQiw0RUFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUM5RixrTEFBTUE7NERBQUM4RixXQUFVOzs7Ozs7c0VBQ2xCLDhEQUFDeEcsdURBQUtBOzREQUNKOEgsYUFBWTs0REFDWkMsT0FBTzVGOzREQUNQNkYsVUFBVSxDQUFDQyxJQUFNN0YsZUFBZTZGLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzs0REFDOUN2QixXQUFVOzs7Ozs7Ozs7Ozs7OERBR2QsOERBQUN2RyxxREFBSUE7b0RBQUM4SCxPQUFPMUY7b0RBQWtCOEYsZUFBZTdGO29EQUFxQmtFLFdBQVU7OERBQzNFLDRFQUFDdEcseURBQVFBO3dEQUFDc0csV0FBVTs7MEVBQ2xCLDhEQUFDckcsNERBQVdBO2dFQUFDNEgsT0FBTTtnRUFBTXZCLFdBQVU7MEVBQW9DOzs7Ozs7MEVBQ3ZFLDhEQUFDckcsNERBQVdBO2dFQUFDNEgsT0FBTTtnRUFBWXZCLFdBQVU7MEVBQW9DOzs7Ozs7MEVBQzdFLDhEQUFDckcsNERBQVdBO2dFQUFDNEgsT0FBTTtnRUFBUXZCLFdBQVU7MEVBQW9DOzs7Ozs7MEVBQ3pFLDhEQUFDckcsNERBQVdBO2dFQUFDNEgsT0FBTTtnRUFBT3ZCLFdBQVU7MEVBQW9DOzs7Ozs7MEVBQ3hFLDhEQUFDckcsNERBQVdBO2dFQUFDNEgsT0FBTTtnRUFBUXZCLFdBQVU7MEVBQW9DOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBUW5GLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDWlYsY0FBY21CLEdBQUcsQ0FBQyxDQUFDN0MscUJBQ2xCLDhEQUFDeEUscURBQUlBOzRDQUVINEcsV0FBVTs0Q0FDVk0sU0FBUyxJQUFNN0IsaUJBQWlCYixLQUFLa0QsRUFBRTtzREFFdkMsNEVBQUN6SCw0REFBV0E7Z0RBQUMyRyxXQUFVOztrRUFDckIsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7MEVBQVlwQyxLQUFLeUQsSUFBSTs7Ozs7OzBFQUNwQyw4REFBQ3RCO2dFQUFJQyxXQUFVOztvRUFDWnBDLEtBQUtDLFVBQVUsa0JBQ2QsOERBQUN0RCxnTEFBSUE7d0VBQUN5RixXQUFVOzs7Ozs7b0VBRWpCcEMsS0FBS2dFLEtBQUssa0JBQ1QsOERBQUN6Qjt3RUFBS0gsV0FBVTtrRkFBb0Q7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFLMUUsOERBQUNpQjt3REFBR2pCLFdBQVU7a0VBQ1hwQyxLQUFLNEIsSUFBSTs7Ozs7O2tFQUdaLDhEQUFDMEI7d0RBQUVsQixXQUFVO2tFQUNWcEMsS0FBS1csV0FBVzs7Ozs7O2tFQUduQiw4REFBQ3dCO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0c7O29FQUFLO29FQUFNeEYsMERBQWNBLENBQUNpRCxLQUFLd0QsTUFBTTs7Ozs7OzswRUFDdEMsOERBQUNqQjs7b0VBQUs7b0VBQU12QyxLQUFLdUQsYUFBYTtvRUFBQzs7Ozs7Ozs7Ozs7OztrRUFHakMsOERBQUNwQjt3REFBSUMsV0FBVTtrRUFDWnBDLEtBQUtpRSxRQUFRLENBQUNDLEtBQUssQ0FBQyxHQUFHLEdBQUdyQixHQUFHLENBQUMsQ0FBQ3NCLFNBQVNDLHNCQUN2Qyw4REFBQzdCO2dFQUVDSCxXQUFVOzBFQUVUK0I7K0RBSElDOzs7Ozs7Ozs7O2tFQVFYLDhEQUFDN0kseURBQU1BO3dEQUNMNkcsV0FBVTt3REFDVkssTUFBSzs7MEVBRUwsOERBQUM3RixnTEFBSUE7Z0VBQUN3RixXQUFVOzs7Ozs7NERBQ2RwQyxLQUFLa0QsRUFBRSxLQUFLLFdBQVdsRCxLQUFLa0QsRUFBRSxLQUFLLFNBQVUsYUFBYTs7Ozs7Ozs7Ozs7OzsyQ0E5QzNEbEQsS0FBS2tELEVBQUU7Ozs7Ozs7Ozs7Z0NBc0RqQnhCLGNBQWNyQyxNQUFNLEtBQUssbUJBQ3hCLDhEQUFDN0QscURBQUlBO29DQUFDNEcsV0FBVTs4Q0FDZCw0RUFBQzNHLDREQUFXQTt3Q0FBQzJHLFdBQVU7OzBEQUNyQiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDOUYsa0xBQU1BO3dEQUFDOEYsV0FBVTs7Ozs7O2tFQUNsQiw4REFBQ2lCO3dEQUFHakIsV0FBVTtrRUFBNkI7Ozs7OztrRUFDM0MsOERBQUNrQjtrRUFBRTs7Ozs7Ozs7Ozs7OzBEQUVMLDhEQUFDL0gseURBQU1BO2dEQUNMcUYsU0FBUTtnREFDUjhCLFNBQVM7b0RBQ1AxRSxlQUFlO29EQUNmRSxvQkFBb0I7Z0RBQ3RCO2dEQUNBa0UsV0FBVTswREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFXYiw4REFBQ3BHLHVFQUFXQTtnQkFDVm9CLE1BQU1BO2dCQUNOaUgsUUFBUTFHO2dCQUNSMkcsU0FBUyxJQUFNMUcsY0FBYztnQkFDN0IyRyxpQkFBaUIsSUFBTTNHLGNBQWM7Ozs7OzswQkFJdkMsOERBQUNYLDREQUFPQTs7Ozs7Ozs7Ozs7QUFHZDtHQS9ld0JFOztRQUNzQzdCLDBEQUFPQTtRQUNwREQsa0RBQVNBO1FBQ042QiwrREFBUUE7OztLQUhKQyIsInNvdXJjZXMiOlsiRTpcXDExMVxcUFJPSkVDVFxcbWluZXMtZ2FtZVxccGFnZXNcXGxvYmJ5LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9yb3V0ZXInO1xuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJ0AvY29udGV4dHMvQXV0aENvbnRleHQnO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbic7XG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZERlc2NyaXB0aW9uLCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY2FyZCc7XG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9pbnB1dCc7XG5pbXBvcnQgeyBUYWJzLCBUYWJzQ29udGVudCwgVGFic0xpc3QsIFRhYnNUcmlnZ2VyIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3RhYnMnO1xuaW1wb3J0IHsgV2FsbGV0TW9kYWwgfSBmcm9tICdAL2NvbXBvbmVudHMvd2FsbGV0L1dhbGxldE1vZGFsJztcbmltcG9ydCB7XG4gIEdlbSxcbiAgTG9nT3V0LFxuICBXYWxsZXQsXG4gIFZvbHVtZTIsXG4gIFZvbHVtZVgsXG4gIFNlYXJjaCxcbiAgVHJlbmRpbmdVcCxcbiAgVXNlcnMsXG4gIFRyb3BoeSxcbiAgQ2xvY2ssXG4gIFN0YXIsXG4gIFBsYXksXG4gIENoZXZyb25MZWZ0LFxuICBDaGV2cm9uUmlnaHQsXG4gIFBsdXNcbn0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IGZvcm1hdEN1cnJlbmN5IH0gZnJvbSAnQC9saWIvdXRpbHMnO1xuaW1wb3J0IHsgc291bmRNYW5hZ2VyIH0gZnJvbSAnQC9saWIvc291bmRzJztcbmltcG9ydCB7IFRvYXN0ZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvdG9hc3Rlcic7XG5pbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS91c2UtdG9hc3QnO1xuaW1wb3J0IHsgR2FtZUNvbmZpZyB9IGZyb20gJ0AvdHlwZXMnO1xuXG5pbnRlcmZhY2UgTG9iYnlTdGF0cyB7XG4gIHBsYXllcnNPbmxpbmU6IG51bWJlcjtcbiAgdG90YWxCZXRzVG9kYXk6IG51bWJlcjtcbiAgYmlnZ2VzdFdpblRvZGF5OiBudW1iZXI7XG4gIHRvdGFsR2FtZXNQbGF5ZWQ6IG51bWJlcjtcbn1cblxuaW50ZXJmYWNlIFJlY2VudFdpbm5lciB7XG4gIGlkOiBzdHJpbmc7XG4gIHVzZXJuYW1lOiBzdHJpbmc7XG4gIGdhbWU6IHN0cmluZztcbiAgbXVsdGlwbGllcjogbnVtYmVyO1xuICB3aW5BbW91bnQ6IG51bWJlcjtcbiAgdGltZXN0YW1wOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYmJ5KCkge1xuICBjb25zdCB7IHVzZXIsIGxvZ291dCwgbG9hZGluZzogYXV0aExvYWRpbmcsIHJlZnJlc2hVc2VyIH0gPSB1c2VBdXRoKCk7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuICBjb25zdCB7IHRvYXN0IH0gPSB1c2VUb2FzdCgpO1xuXG4gIC8vIFVJIHN0YXRlXG4gIGNvbnN0IFtzaG93V2FsbGV0LCBzZXRTaG93V2FsbGV0XSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3NvdW5kRW5hYmxlZCwgc2V0U291bmRFbmFibGVkXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbc2VhcmNoUXVlcnksIHNldFNlYXJjaFF1ZXJ5XSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3NlbGVjdGVkQ2F0ZWdvcnksIHNldFNlbGVjdGVkQ2F0ZWdvcnldID0gdXNlU3RhdGUoJ2FsbCcpO1xuXG4gIC8vIERhdGEgc3RhdGVcbiAgY29uc3QgW2dhbWVzLCBzZXRHYW1lc10gPSB1c2VTdGF0ZTxHYW1lQ29uZmlnW10+KFtdKTtcbiAgY29uc3QgW2ZlYXR1cmVkR2FtZXMsIHNldEZlYXR1cmVkR2FtZXNdID0gdXNlU3RhdGU8R2FtZUNvbmZpZ1tdPihbXSk7XG4gIGNvbnN0IFtsb2JieVN0YXRzLCBzZXRMb2JieVN0YXRzXSA9IHVzZVN0YXRlPExvYmJ5U3RhdHM+KHtcbiAgICBwbGF5ZXJzT25saW5lOiAwLFxuICAgIHRvdGFsQmV0c1RvZGF5OiAwLFxuICAgIGJpZ2dlc3RXaW5Ub2RheTogMCxcbiAgICB0b3RhbEdhbWVzUGxheWVkOiAwXG4gIH0pO1xuICBjb25zdCBbcmVjZW50V2lubmVycywgc2V0UmVjZW50V2lubmVyc10gPSB1c2VTdGF0ZTxSZWNlbnRXaW5uZXJbXT4oW10pO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcblxuICAvLyBDYXJvdXNlbCBzdGF0ZVxuICBjb25zdCBbY3VycmVudEZlYXR1cmVkSW5kZXgsIHNldEN1cnJlbnRGZWF0dXJlZEluZGV4XSA9IHVzZVN0YXRlKDApO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFhdXRoTG9hZGluZyAmJiAhdXNlcikge1xuICAgICAgcm91dGVyLnB1c2goJy9sb2dpbicpO1xuICAgIH1cbiAgfSwgW3VzZXIsIGF1dGhMb2FkaW5nLCByb3V0ZXJdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNvdW5kTWFuYWdlci5zZXRFbmFibGVkKHNvdW5kRW5hYmxlZCk7XG4gIH0sIFtzb3VuZEVuYWJsZWRdKTtcblxuICAvLyBMb2FkIGdhbWVzIGFuZCBsb2JieSBkYXRhXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHVzZXIpIHtcbiAgICAgIGxvYWRMb2JieURhdGEoKTtcbiAgICB9XG4gIH0sIFt1c2VyXSk7XG5cbiAgLy8gQXV0by1yb3RhdGUgZmVhdHVyZWQgZ2FtZXMgY2Fyb3VzZWxcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoZmVhdHVyZWRHYW1lcy5sZW5ndGggPiAxKSB7XG4gICAgICBjb25zdCBpbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHtcbiAgICAgICAgc2V0Q3VycmVudEZlYXR1cmVkSW5kZXgoKHByZXYpID0+XG4gICAgICAgICAgcHJldiA9PT0gZmVhdHVyZWRHYW1lcy5sZW5ndGggLSAxID8gMCA6IHByZXYgKyAxXG4gICAgICAgICk7XG4gICAgICB9LCA1MDAwKTtcbiAgICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKGludGVydmFsKTtcbiAgICB9XG4gIH0sIFtmZWF0dXJlZEdhbWVzLmxlbmd0aF0pO1xuXG4gIGNvbnN0IGxvYWRMb2JieURhdGEgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG5cbiAgICAgIC8vIExvYWQgYXZhaWxhYmxlIGdhbWVzXG4gICAgICBjb25zdCBnYW1lc1Jlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvZ2FtZS9saXN0P2FjdGl2ZT10cnVlJyk7XG4gICAgICBjb25zdCBnYW1lc0RhdGEgPSBhd2FpdCBnYW1lc1Jlc3BvbnNlLmpzb24oKTtcblxuICAgICAgaWYgKGdhbWVzRGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIHNldEdhbWVzKGdhbWVzRGF0YS5nYW1lcyk7XG4gICAgICAgIHNldEZlYXR1cmVkR2FtZXMoZ2FtZXNEYXRhLmdhbWVzLmZpbHRlcigoZ2FtZTogR2FtZUNvbmZpZykgPT4gZ2FtZS5pc0ZlYXR1cmVkKSk7XG4gICAgICB9XG5cbiAgICAgIC8vIExvYWQgbG9iYnkgc3RhdGlzdGljc1xuICAgICAgY29uc3Qgc3RhdHNSZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2xvYmJ5L3N0YXRzJyk7XG4gICAgICBjb25zdCBzdGF0c0RhdGEgPSBhd2FpdCBzdGF0c1Jlc3BvbnNlLmpzb24oKTtcblxuICAgICAgaWYgKHN0YXRzRGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIHNldExvYmJ5U3RhdHMoe1xuICAgICAgICAgIHBsYXllcnNPbmxpbmU6IHN0YXRzRGF0YS5zdGF0cy5wbGF5ZXJzT25saW5lLFxuICAgICAgICAgIHRvdGFsQmV0c1RvZGF5OiBzdGF0c0RhdGEuc3RhdHMudG90YWxCZXRzVG9kYXksXG4gICAgICAgICAgYmlnZ2VzdFdpblRvZGF5OiBzdGF0c0RhdGEuc3RhdHMuYmlnZ2VzdFdpblRvZGF5LFxuICAgICAgICAgIHRvdGFsR2FtZXNQbGF5ZWQ6IHN0YXRzRGF0YS5zdGF0cy50b3RhbEdhbWVzUGxheWVkXG4gICAgICAgIH0pO1xuICAgICAgICBzZXRSZWNlbnRXaW5uZXJzKHN0YXRzRGF0YS5zdGF0cy5yZWNlbnRXaW5uZXJzKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIEZhbGxiYWNrIHRvIG1vY2sgZGF0YSBpZiBBUEkgZmFpbHNcbiAgICAgICAgc2V0TG9iYnlTdGF0cyh7XG4gICAgICAgICAgcGxheWVyc09ubGluZTogTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogNTAwKSArIDEwMCxcbiAgICAgICAgICB0b3RhbEJldHNUb2RheTogTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogMTAwMDApICsgNTAwMCxcbiAgICAgICAgICBiaWdnZXN0V2luVG9kYXk6IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDUwMDAwKSArIDEwMDAwLFxuICAgICAgICAgIHRvdGFsR2FtZXNQbGF5ZWQ6IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDEwMDAwMCkgKyA1MDAwMFxuICAgICAgICB9KTtcbiAgICAgICAgc2V0UmVjZW50V2lubmVycyhbXSk7XG4gICAgICB9XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGxvYWQgbG9iYnkgZGF0YTonLCBlcnJvcik7XG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiBcIkVycm9yXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIkZhaWxlZCB0byBsb2FkIGxvYmJ5IGRhdGFcIixcbiAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxuICAgICAgfSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVHYW1lU2VsZWN0ID0gKGdhbWVJZDogc3RyaW5nKSA9PiB7XG4gICAgc291bmRNYW5hZ2VyLnBsYXkoJ2NsaWNrJyk7XG5cbiAgICBpZiAoZ2FtZUlkID09PSAnbWluZXMnKSB7XG4gICAgICByb3V0ZXIucHVzaCgnL2dhbWUvbWluZXMnKTtcbiAgICB9IGVsc2UgaWYgKGdhbWVJZCA9PT0gJ2RpY2UnKSB7XG4gICAgICByb3V0ZXIucHVzaCgnL2dhbWUvZGljZScpO1xuICAgIH0gZWxzZSB7XG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiBcIkNvbWluZyBTb29uXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBgJHtnYW1lSWR9IHdpbGwgYmUgYXZhaWxhYmxlIHNvb24hYCxcbiAgICAgICAgdmFyaWFudDogXCJpbmZvXCIsXG4gICAgICB9KTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQWRkRnVuZHMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvdGVzdC9hZGQtYmFsYW5jZScsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IGFtb3VudDogMTAwIH0pLFxuICAgICAgfSk7XG5cbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgLy8gUmVmcmVzaCB1c2VyIGRhdGEgdG8gZ2V0IHVwZGF0ZWQgYmFsYW5jZVxuICAgICAgICBhd2FpdCByZWZyZXNoVXNlcigpO1xuXG4gICAgICAgIHRvYXN0KHtcbiAgICAgICAgICB0aXRsZTogXCJGdW5kcyBBZGRlZCFcIixcbiAgICAgICAgICBkZXNjcmlwdGlvbjogXCJTdWNjZXNzZnVsbHkgYWRkZWQgJDEwMCB0byB5b3VyIGJhbGFuY2VcIixcbiAgICAgICAgICB2YXJpYW50OiBcImRlZmF1bHRcIixcbiAgICAgICAgfSk7XG5cbiAgICAgICAgc291bmRNYW5hZ2VyLnBsYXkoJ3dpbicpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGRhdGEuZXJyb3IgfHwgJ0ZhaWxlZCB0byBhZGQgZnVuZHMnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGFkZCBmdW5kczonLCBlcnJvcik7XG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiBcIkVycm9yXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIkZhaWxlZCB0byBhZGQgZnVuZHMuIFBsZWFzZSB0cnkgYWdhaW4uXCIsXG4gICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIixcbiAgICAgIH0pO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBmaWx0ZXJlZEdhbWVzID0gZ2FtZXMuZmlsdGVyKGdhbWUgPT4ge1xuICAgIGNvbnN0IG1hdGNoZXNTZWFyY2ggPSBnYW1lLm5hbWUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hRdWVyeS50b0xvd2VyQ2FzZSgpKSB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgIGdhbWUuZGVzY3JpcHRpb24udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hRdWVyeS50b0xvd2VyQ2FzZSgpKTtcbiAgICBjb25zdCBtYXRjaGVzQ2F0ZWdvcnkgPSBzZWxlY3RlZENhdGVnb3J5ID09PSAnYWxsJyB8fCBnYW1lLmNhdGVnb3J5ID09PSBzZWxlY3RlZENhdGVnb3J5O1xuICAgIHJldHVybiBtYXRjaGVzU2VhcmNoICYmIG1hdGNoZXNDYXRlZ29yeTtcbiAgfSk7XG5cbiAgY29uc3QgbmV4dEZlYXR1cmVkID0gKCkgPT4ge1xuICAgIHNldEN1cnJlbnRGZWF0dXJlZEluZGV4KChwcmV2KSA9PlxuICAgICAgcHJldiA9PT0gZmVhdHVyZWRHYW1lcy5sZW5ndGggLSAxID8gMCA6IHByZXYgKyAxXG4gICAgKTtcbiAgfTtcblxuICBjb25zdCBwcmV2RmVhdHVyZWQgPSAoKSA9PiB7XG4gICAgc2V0Q3VycmVudEZlYXR1cmVkSW5kZXgoKHByZXYpID0+XG4gICAgICBwcmV2ID09PSAwID8gZmVhdHVyZWRHYW1lcy5sZW5ndGggLSAxIDogcHJldiAtIDFcbiAgICApO1xuICB9O1xuXG4gIGlmIChhdXRoTG9hZGluZyB8fCBsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tZ3JheS05MDAgdmlhLXB1cnBsZS05MDAgdG8tdmlvbGV0LTkwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0zMiB3LTMyIGJvcmRlci1iLTIgYm9yZGVyLXdoaXRlXCI+PC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgaWYgKCF1c2VyKSB7XG4gICAgcmV0dXJuIG51bGw7IC8vIFdpbGwgcmVkaXJlY3QgdG8gbG9naW5cbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS05MDBcIj5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8aGVhZGVyIGNsYXNzTmFtZT1cImJnLWdyYXktODAwLzUwIGJvcmRlci1iIGJvcmRlci1ncmF5LTcwMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTQgcHktM1wiPlxuICAgICAgICAgIDxuYXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICA8R2VtIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1wdXJwbGUtNDAwXCIgLz5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPkJldE9jdGF2ZTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNzAwLzUwIHB4LTMgcHktMSByb3VuZGVkLW1kIHRleHQtd2hpdGUgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDBcIj57Zm9ybWF0Q3VycmVuY3kodXNlci51c2R0X2JhbGFuY2UpfSA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1vcmFuZ2UtNDAwXCI+4oK/PC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd1dhbGxldCh0cnVlKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTcwMCB0ZXh0LXdoaXRlIGJvcmRlci1ibHVlLTYwMFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8V2FsbGV0IGNsYXNzTmFtZT1cImgtNCB3LTQgc206bXItMlwiIC8+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiaGlkZGVuIHNtOmlubGluZVwiPldhbGxldDwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNvdW5kRW5hYmxlZCghc291bmRFbmFibGVkKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXItZ3JheS01MDAgdGV4dC1ncmF5LTQwMCBob3ZlcjpiZy1ncmF5LTYwMCBob3Zlcjp0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtzb3VuZEVuYWJsZWQgPyA8Vm9sdW1lMiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz4gOiA8Vm9sdW1lWCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz59XG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuXG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtsb2dvdXR9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyLWdyYXktNTAwIHRleHQtZ3JheS00MDAgaG92ZXI6YmctZ3JheS02MDAgaG92ZXI6dGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8TG9nT3V0IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvbmF2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvaGVhZGVyPlxuXG4gICAgICB7LyogTWFpbiBDb250ZW50ICovfVxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBsZzpncmlkLWNvbHMtNCBnYXAtNlwiPlxuICAgICAgICAgIHsvKiBMZWZ0IFNpZGViYXIgLSBTdGF0cyAmIFJlY2VudCBXaW5uZXJzICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6Y29sLXNwYW4tMSBzcGFjZS15LTZcIj5cbiAgICAgICAgICAgIHsvKiBMaXZlIFN0YXRpc3RpY3MgKi99XG4gICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMC84MCBib3JkZXItZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwicGItM1wiPlxuICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPFRyZW5kaW5nVXAgY2xhc3NOYW1lPVwiaC01IHctNSBtci0yIHRleHQtZ3JlZW4tNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIExpdmUgU3RhdHNcbiAgICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8VXNlcnMgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPlBsYXllcnMgT25saW5lPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTQwMCBmb250LXNlbWlib2xkXCI+e2xvYmJ5U3RhdHMucGxheWVyc09ubGluZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8VHJvcGh5IGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc21cIj5CaWdnZXN0IFdpbjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctNDAwIGZvbnQtc2VtaWJvbGRcIj57Zm9ybWF0Q3VycmVuY3kobG9iYnlTdGF0cy5iaWdnZXN0V2luVG9kYXkpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LWdyYXktMzAwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxDbG9jayBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+QmV0cyBUb2RheTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ibHVlLTQwMCBmb250LXNlbWlib2xkXCI+e2xvYmJ5U3RhdHMudG90YWxCZXRzVG9kYXkudG9Mb2NhbGVTdHJpbmcoKX08L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgICAgIHsvKiBSZWNlbnQgV2lubmVycyAqL31cbiAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImJnLWdyYXktODAwLzgwIGJvcmRlci1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJwYi0zXCI+XG4gICAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8VHJvcGh5IGNsYXNzTmFtZT1cImgtNSB3LTUgbXItMiB0ZXh0LXllbGxvdy00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgUmVjZW50IFdpbm5lcnNcbiAgICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAge3JlY2VudFdpbm5lcnMubWFwKCh3aW5uZXIpID0+IChcbiAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXt3aW5uZXIuaWR9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwLTIgYmctZ3JheS03MDAvNTAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+e3dpbm5lci51c2VybmFtZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC14c1wiPnt3aW5uZXIuZ2FtZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi00MDAgdGV4dC1zbSBmb250LXNlbWlib2xkXCI+e3dpbm5lci5tdWx0aXBsaWVyfXg8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgdGV4dC14c1wiPntmb3JtYXRDdXJyZW5jeSh3aW5uZXIud2luQW1vdW50KX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBNYWluIENvbnRlbnQgQXJlYSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTMgc3BhY2UteS02XCI+XG4gICAgICAgICAgICB7LyogRmVhdHVyZWQgR2FtZXMgQ2Fyb3VzZWwgKi99XG4gICAgICAgICAgICB7ZmVhdHVyZWRHYW1lcy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiYmctZ3JheS04MDAvODAgYm9yZGVyLWdyYXktNjAwIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cInBiLTNcIj5cbiAgICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8U3RhciBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTIgdGV4dC15ZWxsb3ctNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgRmVhdHVyZWQgR2FtZXNcbiAgICAgICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC0wXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi01MDAgZWFzZS1pbi1vdXRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgdHJhbnNmb3JtOiBgdHJhbnNsYXRlWCgtJHtjdXJyZW50RmVhdHVyZWRJbmRleCAqIDEwMH0lKWAgfX1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICB7ZmVhdHVyZWRHYW1lcy5tYXAoKGdhbWUpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2dhbWUuaWR9IGNsYXNzTmFtZT1cInctZnVsbCBmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBiZy1ncmFkaWVudC10by1yIGZyb20tcHVycGxlLTYwMCB0by1waW5rLTYwMCBwLTYgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIG1iLTJcIj57Z2FtZS5uYW1lfTwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1wdXJwbGUtMTAwIG1iLTRcIj57Z2FtZS5kZXNjcmlwdGlvbn08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTQgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYmctd2hpdGUvMjAgcHgtMiBweS0xIHJvdW5kZWQgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBNYXgge2dhbWUubWF4TXVsdGlwbGllcn14XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJiZy13aGl0ZS8yMCBweC0yIHB5LTEgcm91bmRlZCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE1pbiB7Zm9ybWF0Q3VycmVuY3koZ2FtZS5taW5CZXQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUdhbWVTZWxlY3QoZ2FtZS5pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy13aGl0ZSB0ZXh0LXB1cnBsZS02MDAgaG92ZXI6YmctZ3JheS0xMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQbGF5IGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBQbGF5IE5vd1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTZ4bCBvcGFjaXR5LTIwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2dhbWUuaWNvbn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAge2ZlYXR1cmVkR2FtZXMubGVuZ3RoID4gMSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17cHJldkZlYXR1cmVkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTIgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiBiZy1ibGFjay8yMCBob3ZlcjpiZy1ibGFjay80MCB0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPENoZXZyb25MZWZ0IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e25leHRGZWF0dXJlZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMiB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIGJnLWJsYWNrLzIwIGhvdmVyOmJnLWJsYWNrLzQwIHRleHQtd2hpdGVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hldnJvblJpZ2h0IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7LyogU2VhcmNoIGFuZCBGaWx0ZXIgKi99XG4gICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMC84MCBib3JkZXItZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgPFNlYXJjaCBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTMgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiBoLTQgdy00IHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlYXJjaCBnYW1lcy4uLlwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaFF1ZXJ5fVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VhcmNoUXVlcnkoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInBsLTEwIGJnLWdyYXktNzAwIGJvcmRlci1ncmF5LTYwMCB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyLWdyYXktNDAwXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPFRhYnMgdmFsdWU9e3NlbGVjdGVkQ2F0ZWdvcnl9IG9uVmFsdWVDaGFuZ2U9e3NldFNlbGVjdGVkQ2F0ZWdvcnl9IGNsYXNzTmFtZT1cInctZnVsbCBzbTp3LWF1dG9cIj5cbiAgICAgICAgICAgICAgICAgICAgPFRhYnNMaXN0IGNsYXNzTmFtZT1cImJnLWdyYXktNzAwIGJvcmRlci1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cImFsbFwiIGNsYXNzTmFtZT1cImRhdGEtW3N0YXRlPWFjdGl2ZV06YmctcHVycGxlLTYwMFwiPkFsbDwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwib3JpZ2luYWxzXCIgY2xhc3NOYW1lPVwiZGF0YS1bc3RhdGU9YWN0aXZlXTpiZy1wdXJwbGUtNjAwXCI+T3JpZ2luYWxzPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJzbG90c1wiIGNsYXNzTmFtZT1cImRhdGEtW3N0YXRlPWFjdGl2ZV06YmctcHVycGxlLTYwMFwiPlNsb3RzPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJsaXZlXCIgY2xhc3NOYW1lPVwiZGF0YS1bc3RhdGU9YWN0aXZlXTpiZy1wdXJwbGUtNjAwXCI+TGl2ZTwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwidGFibGVcIiBjbGFzc05hbWU9XCJkYXRhLVtzdGF0ZT1hY3RpdmVdOmJnLXB1cnBsZS02MDBcIj5UYWJsZTwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgICAgICAgICAgIDwvVGFic0xpc3Q+XG4gICAgICAgICAgICAgICAgICA8L1RhYnM+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgICAgIHsvKiBHYW1lcyBHcmlkICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIHNtOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC00XCI+XG4gICAgICAgICAgICAgIHtmaWx0ZXJlZEdhbWVzLm1hcCgoZ2FtZSkgPT4gKFxuICAgICAgICAgICAgICAgIDxDYXJkXG4gICAgICAgICAgICAgICAgICBrZXk9e2dhbWUuaWR9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMC84MCBib3JkZXItZ3JheS02MDAgaG92ZXI6Ym9yZGVyLXB1cnBsZS01MDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGN1cnNvci1wb2ludGVyIGdyb3VwXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUdhbWVTZWxlY3QoZ2FtZS5pZCl9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTN4bFwiPntnYW1lLmljb259PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtnYW1lLmlzRmVhdHVyZWQgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8U3RhciBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQteWVsbG93LTQwMCBmaWxsLWN1cnJlbnRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHtnYW1lLmlzTmV3ICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYmctZ3JlZW4tNTAwIHRleHQtd2hpdGUgdGV4dC14cyBweC0yIHB5LTEgcm91bmRlZFwiPk5FVzwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgbWItMiBncm91cC1ob3Zlcjp0ZXh0LXB1cnBsZS00MDAgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7Z2FtZS5uYW1lfVxuICAgICAgICAgICAgICAgICAgICA8L2gzPlxuXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbSBtYi0zIGxpbmUtY2xhbXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtnYW1lLmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICAgICAgICA8L3A+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gdGV4dC14cyB0ZXh0LWdyYXktNTAwIG1iLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5NaW46IHtmb3JtYXRDdXJyZW5jeShnYW1lLm1pbkJldCl9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPk1heDoge2dhbWUubWF4TXVsdGlwbGllcn14PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0xIG1iLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7Z2FtZS5mZWF0dXJlcy5zbGljZSgwLCAyKS5tYXAoKGZlYXR1cmUsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhblxuICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmF5LTcwMCB0ZXh0LWdyYXktMzAwIHRleHQteHMgcHgtMiBweS0xIHJvdW5kZWRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7ZmVhdHVyZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy1wdXJwbGUtNjAwIGhvdmVyOmJnLXB1cnBsZS03MDAgdGV4dC13aGl0ZSBncm91cC1ob3ZlcjpiZy1wdXJwbGUtNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPFBsYXkgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICB7KGdhbWUuaWQgPT09ICdtaW5lcycgfHwgZ2FtZS5pZCA9PT0gJ2RpY2UnKSA/ICdQbGF5IE5vdycgOiAnQ29taW5nIFNvb24nfVxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogTm8gR2FtZXMgRm91bmQgKi99XG4gICAgICAgICAgICB7ZmlsdGVyZWRHYW1lcy5sZW5ndGggPT09IDAgJiYgKFxuICAgICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMC84MCBib3JkZXItZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC04IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICA8U2VhcmNoIGNsYXNzTmFtZT1cImgtMTIgdy0xMiBteC1hdXRvIG1iLTQgb3BhY2l0eS01MFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItMlwiPk5vIGdhbWVzIGZvdW5kPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgPHA+VHJ5IGFkanVzdGluZyB5b3VyIHNlYXJjaCBvciBmaWx0ZXIgY3JpdGVyaWE8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgc2V0U2VhcmNoUXVlcnkoJycpO1xuICAgICAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkQ2F0ZWdvcnkoJ2FsbCcpO1xuICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXItZ3JheS01MDAgdGV4dC1ncmF5LTQwMCBob3ZlcjpiZy1ncmF5LTYwMCBob3Zlcjp0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgQ2xlYXIgRmlsdGVyc1xuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L21haW4+XG5cbiAgICAgIHsvKiBNb2RhbHMgKi99XG4gICAgICA8V2FsbGV0TW9kYWxcbiAgICAgICAgdXNlcj17dXNlcn1cbiAgICAgICAgaXNPcGVuPXtzaG93V2FsbGV0fVxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRTaG93V2FsbGV0KGZhbHNlKX1cbiAgICAgICAgb25CYWxhbmNlVXBkYXRlPXsoKSA9PiBzZXRTaG93V2FsbGV0KGZhbHNlKX1cbiAgICAgIC8+XG5cbiAgICAgIHsvKiBUb2FzdCBOb3RpZmljYXRpb25zICovfVxuICAgICAgPFRvYXN0ZXIgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn0iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJ1c2VSb3V0ZXIiLCJ1c2VBdXRoIiwiQnV0dG9uIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIklucHV0IiwiVGFicyIsIlRhYnNMaXN0IiwiVGFic1RyaWdnZXIiLCJXYWxsZXRNb2RhbCIsIkdlbSIsIkxvZ091dCIsIldhbGxldCIsIlZvbHVtZTIiLCJWb2x1bWVYIiwiU2VhcmNoIiwiVHJlbmRpbmdVcCIsIlVzZXJzIiwiVHJvcGh5IiwiQ2xvY2siLCJTdGFyIiwiUGxheSIsIkNoZXZyb25MZWZ0IiwiQ2hldnJvblJpZ2h0IiwiZm9ybWF0Q3VycmVuY3kiLCJzb3VuZE1hbmFnZXIiLCJUb2FzdGVyIiwidXNlVG9hc3QiLCJMb2JieSIsInVzZXIiLCJsb2dvdXQiLCJsb2FkaW5nIiwiYXV0aExvYWRpbmciLCJyZWZyZXNoVXNlciIsInJvdXRlciIsInRvYXN0Iiwic2hvd1dhbGxldCIsInNldFNob3dXYWxsZXQiLCJzb3VuZEVuYWJsZWQiLCJzZXRTb3VuZEVuYWJsZWQiLCJzZWFyY2hRdWVyeSIsInNldFNlYXJjaFF1ZXJ5Iiwic2VsZWN0ZWRDYXRlZ29yeSIsInNldFNlbGVjdGVkQ2F0ZWdvcnkiLCJnYW1lcyIsInNldEdhbWVzIiwiZmVhdHVyZWRHYW1lcyIsInNldEZlYXR1cmVkR2FtZXMiLCJsb2JieVN0YXRzIiwic2V0TG9iYnlTdGF0cyIsInBsYXllcnNPbmxpbmUiLCJ0b3RhbEJldHNUb2RheSIsImJpZ2dlc3RXaW5Ub2RheSIsInRvdGFsR2FtZXNQbGF5ZWQiLCJyZWNlbnRXaW5uZXJzIiwic2V0UmVjZW50V2lubmVycyIsInNldExvYWRpbmciLCJjdXJyZW50RmVhdHVyZWRJbmRleCIsInNldEN1cnJlbnRGZWF0dXJlZEluZGV4IiwicHVzaCIsInNldEVuYWJsZWQiLCJsb2FkTG9iYnlEYXRhIiwibGVuZ3RoIiwiaW50ZXJ2YWwiLCJzZXRJbnRlcnZhbCIsInByZXYiLCJjbGVhckludGVydmFsIiwiZ2FtZXNSZXNwb25zZSIsImZldGNoIiwiZ2FtZXNEYXRhIiwianNvbiIsInN1Y2Nlc3MiLCJmaWx0ZXIiLCJnYW1lIiwiaXNGZWF0dXJlZCIsInN0YXRzUmVzcG9uc2UiLCJzdGF0c0RhdGEiLCJzdGF0cyIsIk1hdGgiLCJmbG9vciIsInJhbmRvbSIsImVycm9yIiwiY29uc29sZSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJ2YXJpYW50IiwiaGFuZGxlR2FtZVNlbGVjdCIsImdhbWVJZCIsInBsYXkiLCJoYW5kbGVBZGRGdW5kcyIsInJlc3BvbnNlIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwiYW1vdW50IiwiZGF0YSIsIkVycm9yIiwiZmlsdGVyZWRHYW1lcyIsIm1hdGNoZXNTZWFyY2giLCJuYW1lIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsIm1hdGNoZXNDYXRlZ29yeSIsImNhdGVnb3J5IiwibmV4dEZlYXR1cmVkIiwicHJldkZlYXR1cmVkIiwiZGl2IiwiY2xhc3NOYW1lIiwiaGVhZGVyIiwibmF2Iiwic3BhbiIsInVzZHRfYmFsYW5jZSIsInNpemUiLCJvbkNsaWNrIiwibWFpbiIsInRvTG9jYWxlU3RyaW5nIiwibWFwIiwid2lubmVyIiwidXNlcm5hbWUiLCJtdWx0aXBsaWVyIiwid2luQW1vdW50IiwiaWQiLCJzdHlsZSIsInRyYW5zZm9ybSIsImgzIiwicCIsIm1heE11bHRpcGxpZXIiLCJtaW5CZXQiLCJpY29uIiwicGxhY2Vob2xkZXIiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsIm9uVmFsdWVDaGFuZ2UiLCJpc05ldyIsImZlYXR1cmVzIiwic2xpY2UiLCJmZWF0dXJlIiwiaW5kZXgiLCJpc09wZW4iLCJvbkNsb3NlIiwib25CYWxhbmNlVXBkYXRlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/lobby.tsx\n"));

/***/ })

});