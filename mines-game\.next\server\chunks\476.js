"use strict";exports.id=476,exports.ids=[476],exports.modules={2998:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{calculateMultiplier:()=>c,calculateProfit:()=>o,cashOut:()=>f,getActiveGame:()=>_,getGameHistory:()=>y,revealCell:()=>m,startGame:()=>d,validateGameParams:()=>l});var n=t(4490),a=t(3546),i=t(5804),u=e([i]);function c(e,r,t=25){if(0===r)return 1;let s=t-e-r;if(s<=0)return 1;let n=1-i.si.HOUSE_EDGE,a=1/(s/(t-e-r+1))*n*(1+.02*r);return Math.max(1.01,Number(a.toFixed(4)))}function o(e,r){return Number((e*r-e).toFixed(8))}function l(e,r,t){return e<i.si.MIN_BET?`Minimum bet is ${i.si.MIN_BET} USDT`:e>i.si.MAX_BET?`Maximum bet is ${i.si.MAX_BET} USDT`:e>t?"Insufficient balance":r<i.si.MIN_MINES?`Minimum ${i.si.MIN_MINES} mine required`:r>i.si.MAX_MINES?`Maximum ${i.si.MAX_MINES} mines allowed`:null}async function d(e,r,t,s){try{let u=a.Gy.findById(e);if(!u)return{success:!1,error:"User not found"};if(a.dW.findActiveByUserId(e))return{success:!1,error:"You have an active game. Please finish it first."};let c=l(r,t,u.usdt_balance);if(c)return{success:!1,error:c};let o=(0,n.IB)(),d=s||(0,n.El)(),m=(0,n.fy)(o,d,t),f=u.usdt_balance-r;a.Gy.updateBalance(e,"USDT",f);let y={user_id:e,game_type:"mines",grid_size:i.si.GRID_SIZE,mine_count:t,bet_amount:r,current_multiplier:1,status:"active",revealed_cells:[],mine_positions:m,server_seed:o,client_seed:d,profit:0},_=a.dW.create(y);a.DR.create({user_id:e,game_id:_.id,type:"bet",currency:"USDT",amount:r,status:"completed"});let h={..._,mine_positions:[],server_seed:(0,n.L8)(o)};return{success:!0,game:h}}catch(e){return console.error("Start game error:",e),{success:!1,error:"Failed to start game"}}}async function m(e,r,t){try{let s=a.dW.findById(e);if(!s)return{success:!1,error:"Game not found"};if(s.user_id!==t)return{success:!1,error:"Unauthorized"};if("active"!==s.status)return{success:!1,error:"Game is not active"};if(r<0||r>=s.grid_size)return{success:!1,error:"Invalid cell index"};if(s.revealed_cells.includes(r))return{success:!1,error:"Cell already revealed"};if(s.mine_positions.includes(r))return a.dW.update(e,{status:"lost",revealed_cells:[...s.revealed_cells,r],profit:-s.bet_amount}),{success:!0,hit:!0,multiplier:0,gameOver:!0,profit:-s.bet_amount,minePositions:s.mine_positions};{let n=[...s.revealed_cells,r],i=c(s.mine_count,n.length,s.grid_size),u=o(s.bet_amount,i);a.dW.update(e,{revealed_cells:n,current_multiplier:i,profit:u});let l=s.grid_size-s.mine_count;if(n.length===l){let r=s.bet_amount+u;a.dW.update(e,{status:"won",profit:u});let n=a.Gy.findById(t);return a.Gy.updateBalance(t,"USDT",n.usdt_balance+r),a.DR.create({user_id:t,game_id:e,type:"win",currency:"USDT",amount:r,status:"completed"}),{success:!0,hit:!1,multiplier:i,gameOver:!0,profit:u,minePositions:s.mine_positions}}return{success:!0,hit:!1,multiplier:i,gameOver:!1,profit:u}}}catch(e){return console.error("Reveal cell error:",e),{success:!1,error:"Failed to reveal cell"}}}async function f(e,r){try{let t=a.dW.findById(e);if(!t)return{success:!1,error:"Game not found"};if(t.user_id!==r)return{success:!1,error:"Unauthorized"};if("active"!==t.status)return{success:!1,error:"Game is not active"};if(0===t.revealed_cells.length)return{success:!1,error:"No cells revealed yet"};let s=t.profit,n=t.bet_amount+s;a.dW.update(e,{status:"cashed_out"});let i=a.Gy.findById(r);return a.Gy.updateBalance(r,"USDT",i.usdt_balance+n),a.DR.create({user_id:r,game_id:e,type:"win",currency:"USDT",amount:n,status:"completed"}),{success:!0,profit:s}}catch(e){return console.error("Cash out error:",e),{success:!1,error:"Failed to cash out"}}}function y(e,r=50){return a.dW.findByUserId(e,r)}function _(e){return a.dW.findActiveByUserId(e)}i=(u.then?(await u)():u)[0],s()}catch(e){s(e)}})},4490:(e,r,t)=>{t.d(r,{El:()=>i,IB:()=>a,L8:()=>o,fy:()=>c});var s=t(5511),n=t.n(s);function a(){return n().randomBytes(32).toString("hex")}function i(){return n().randomBytes(16).toString("hex")}function u(e,r,t=0){let s=`${e}:${r}:${t}`;return n().createHash("sha256").update(s).digest("hex")}function c(e,r,t,s=25){let n=u(e,r),a=[],i=Array.from({length:s},(e,r)=>r),o=0;for(;a.length<t&&i.length>0;){let t=parseInt(n.slice(8*o,(o+1)*8),16)%i.length,s=i[t];a.push(s),i.splice(t,1),8*++o>=n.length&&(u(e,r,o),o=n.length/8)}return a.sort((e,r)=>e-r)}function o(e){return n().createHash("sha256").update(e).digest("hex")}},5804:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.d(r,{si:()=>u});var n=t(802),a=t(5979),i=e([n,a]);[n,a]=i.then?(await i)():i;let u={GRID_SIZE:25,MIN_MINES:1,MAX_MINES:24,MIN_BET:.01,MAX_BET:1e3,HOUSE_EDGE:.04,BASE_MULTIPLIER:1};s()}catch(e){s(e)}})},9103:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.d(r,{DY:()=>f,Eb:()=>p,Lx:()=>y,OB:()=>h,Tf:()=>g,VX:()=>_,b9:()=>d,ru:()=>m});var n=t(829),a=t.n(n),i=t(3139),u=t(3546),c=e([i]);i=(c.then?(await c)():c)[0];let v="your-super-secret-jwt-key-change-this-in-production-make-it-very-long-and-random";async function o(e){return i.default.hash(e,12)}async function l(e,r){return i.default.compare(e,r)}function d(e){let r=function(e){let r=e.headers.authorization;if(r&&r.startsWith("Bearer "))return r.substring(7);let t=e.cookies.token;return t||null}(e);if(!r)return null;let t=function(e){try{return a().verify(e,v)}catch(e){return null}}(r);return t&&t.userId?u.Gy.findById(t.userId):null}function m(e){return async(r,t)=>{try{let s=d(r);if(!s)return t.status(401).json({success:!1,error:"Authentication required"});await e(r,t,s)}catch(e){console.error("Auth middleware error:",e),t.status(500).json({success:!1,error:"Internal server error"})}}}async function f(e,r,t){try{let s=!e||e.length<3||e.length>20?"Username must be between 3 and 20 characters":/^[a-zA-Z0-9_]+$/.test(e)?r&&/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r)?!t||t.length<8?"Password must be at least 8 characters long":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(t)?null:"Password must contain at least one uppercase letter, one lowercase letter, and one number":"Please provide a valid email address":"Username can only contain letters, numbers, and underscores";if(s)return{success:!1,error:s};if(u.Gy.findByEmail(r))return{success:!1,error:"Email already registered"};if(u.Gy.findByUsername(e))return{success:!1,error:"Username already taken"};let n=await o(t),{password_hash:a,...i}=u.Gy.create(e,r,n);return{success:!0,user:i}}catch(e){return console.error("Registration error:",e),{success:!1,error:"Failed to register user"}}}async function y(e,r){try{let t=e&&r?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)?null:"Please provide a valid email address":"Email and password are required";if(t)return{success:!1,error:t};let s=u.Gy.findByEmail(e);if(!s||!await l(r,s.password_hash))return{success:!1,error:"Invalid email or password"};let n=function(e){let r={userId:e.id,username:e.username,email:e.email};return a().sign(r,v,{expiresIn:"7d"})}(s),{password_hash:i,...c}=s;return{success:!0,user:c,token:n}}catch(e){return console.error("Login error:",e),{success:!1,error:"Failed to login"}}}function _(e,r){e.setHeader("Set-Cookie",[`token=${r}; HttpOnly; Path=/; Max-Age=604800; SameSite=Strict; Secure`])}function h(e){e.setHeader("Set-Cookie",["token=; HttpOnly; Path=/; Max-Age=0; SameSite=Strict"])}let I=new Map;function p(e,r=5,t=9e5){let s=Date.now(),n=I.get(e);return!n||s>n.resetTime?(I.set(e,{count:1,resetTime:s+t}),!0):!(n.count>=r)&&(n.count++,!0)}function g(e){let r=e.headers["x-forwarded-for"];return(r?Array.isArray(r)?r[0]:r.split(",")[0]:e.socket.remoteAddress)||"unknown"}s()}catch(e){s(e)}})}};