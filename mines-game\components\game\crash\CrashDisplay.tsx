import React, { useEffect, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CrashGameState } from '@/types';

interface CrashDisplayProps {
  gameState: CrashGameState | null;
  currentMultiplier: number;
  roundPhase: 'betting' | 'flying' | 'crashed' | 'waiting';
  timeElapsed: number;
  timeUntilNextRound: number;
}

export function CrashDisplay({
  gameState,
  currentMultiplier,
  roundPhase,
  timeElapsed,
  timeUntilNextRound
}: CrashDisplayProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const multiplierHistoryRef = useRef<number[]>([]);

  // Draw the crash curve
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const draw = () => {
      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Set canvas size
      canvas.width = canvas.offsetWidth * window.devicePixelRatio;
      canvas.height = canvas.offsetHeight * window.devicePixelRatio;
      ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

      const width = canvas.offsetWidth;
      const height = canvas.offsetHeight;

      // Background gradient
      const gradient = ctx.createLinearGradient(0, 0, 0, height);
      gradient.addColorStop(0, 'rgba(16, 185, 129, 0.1)');
      gradient.addColorStop(1, 'rgba(16, 185, 129, 0.05)');
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, width, height);

      // Grid lines
      ctx.strokeStyle = 'rgba(75, 85, 99, 0.3)';
      ctx.lineWidth = 1;
      
      // Horizontal grid lines
      for (let i = 1; i <= 5; i++) {
        const y = (height / 6) * i;
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(width, y);
        ctx.stroke();
      }

      // Vertical grid lines
      for (let i = 1; i <= 10; i++) {
        const x = (width / 11) * i;
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, height);
        ctx.stroke();
      }

      // Draw 45-degree line and rocket
      if (roundPhase === 'flying' || roundPhase === 'crashed') {
        // Add current multiplier to history
        if (roundPhase === 'flying') {
          multiplierHistoryRef.current.push(currentMultiplier);
          // Keep only last 100 points for performance
          if (multiplierHistoryRef.current.length > 100) {
            multiplierHistoryRef.current.shift();
          }
        }

        const history = multiplierHistoryRef.current;
        if (history.length > 0) {
          // Draw 45-degree line based on time elapsed
          const maxTime = Math.max(timeElapsed, 5000); // At least 5 seconds visible
          const maxMultiplier = Math.max(currentMultiplier, 2);

          // Calculate 45-degree line
          ctx.strokeStyle = roundPhase === 'crashed' ? '#ef4444' : '#10b981';
          ctx.lineWidth = 4;
          ctx.beginPath();

          // Start from bottom left
          const startX = 50;
          const startY = height - 50;

          // Calculate end point based on current multiplier and time
          // 45-degree angle means equal x and y progression
          const progressX = (timeElapsed / maxTime) * (width - 100);
          const progressY = ((currentMultiplier - 1) / (maxMultiplier - 1)) * (height - 100);

          const endX = startX + progressX;
          const endY = startY - progressY;

          ctx.moveTo(startX, startY);
          ctx.lineTo(endX, endY);
          ctx.stroke();

          // Fill area under line
          ctx.fillStyle = roundPhase === 'crashed'
            ? 'rgba(239, 68, 68, 0.1)'
            : 'rgba(16, 185, 129, 0.1)';
          ctx.beginPath();
          ctx.moveTo(startX, startY);
          ctx.lineTo(endX, endY);
          ctx.lineTo(endX, startY);
          ctx.closePath();
          ctx.fill();

          // Draw rocket at the end of the line
          if (roundPhase === 'flying') {
            ctx.fillStyle = '#fbbf24';
            ctx.font = '24px sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText('🚀', endX, endY - 10);
          }

          // Draw crash explosion if crashed
          if (roundPhase === 'crashed') {
            // Explosion effect
            ctx.fillStyle = '#ef4444';
            ctx.beginPath();
            ctx.arc(endX, endY, 12, 0, Math.PI * 2);
            ctx.fill();

            // Explosion emoji
            ctx.fillStyle = '#ffffff';
            ctx.font = '24px sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText('💥', endX, endY + 8);

            // Crash text
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 18px sans-serif';
            ctx.fillText('CRASHED!', endX, endY - 25);

            // Show crash multiplier
            ctx.font = 'bold 14px sans-serif';
            ctx.fillStyle = '#ef4444';
            ctx.fillText(`${currentMultiplier.toFixed(2)}x`, endX, endY + 35);
          }
        }
      } else {
        // Reset history for new round
        multiplierHistoryRef.current = [];
      }



      animationRef.current = requestAnimationFrame(draw);
    };

    draw();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [currentMultiplier, roundPhase, gameState]);

  const getPhaseColor = () => {
    switch (roundPhase) {
      case 'betting': return 'bg-blue-500';
      case 'flying': return 'bg-green-500';
      case 'crashed': return 'bg-red-500';
      case 'waiting': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  const getPhaseText = () => {
    switch (roundPhase) {
      case 'betting': {
        const timeLeft = Math.ceil(timeUntilNextRound / 1000);
        if (gameState) {
          return `Betting Phase - ${timeLeft}s`;
        } else {
          return timeLeft > 0 ? `Betting Phase - ${timeLeft}s` : 'Waiting for bets...';
        }
      }
      case 'flying': return 'Flying!';
      case 'crashed': return `Crashed at ${currentMultiplier.toFixed(2)}x`;
      case 'waiting': return `Next Round - ${Math.ceil(timeUntilNextRound / 1000)}s`;
      default: return 'Waiting...';
    }
  };

  return (
    <Card className="bg-gray-800/80 border-gray-600 h-full">
      <CardContent className="p-6 h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${getPhaseColor()} animate-pulse`} />
            <span className="text-white font-medium">{getPhaseText()}</span>
          </div>
          <Badge variant="outline" className="border-gray-600 text-gray-300">
            Crash Game
          </Badge>
        </div>

        {/* Multiplier Display */}
        <div className="text-center mb-6">
          <div className={`text-6xl font-bold mb-2 ${
            roundPhase === 'crashed' ? 'text-red-400' : 
            roundPhase === 'flying' ? 'text-green-400' : 
            'text-gray-400'
          }`}>
            {currentMultiplier.toFixed(2)}x
          </div>
          <div className="text-gray-400 text-sm">
            {roundPhase === 'flying' && `${(timeElapsed / 1000).toFixed(1)}s elapsed`}
            {roundPhase === 'betting' && (gameState ? 'Round starting soon!' : 'Place your bet to start!')}
            {roundPhase === 'crashed' && 'Round ended'}
            {roundPhase === 'waiting' && 'Preparing next round...'}
          </div>
        </div>

        {/* Graph Canvas */}
        <div className="flex-1 relative min-h-[300px]">
          <canvas
            ref={canvasRef}
            className="w-full h-full rounded-lg border border-gray-600"
            style={{ background: 'rgba(17, 24, 39, 0.5)' }}
          />
          
          {/* Overlay messages */}
          {roundPhase === 'betting' && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <div className="text-4xl mb-2">🚀</div>
                <div className="text-white text-xl font-semibold">
                  {gameState ? 'Get Ready!' : 'Place Your Bet!'}
                </div>
                <div className="text-gray-400">
                  {gameState ? 'Round starting soon...' : 'Round will start when bets are placed'}
                </div>
              </div>
            </div>
          )}
          
          {roundPhase === 'waiting' && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <div className="text-4xl mb-2">⏳</div>
                <div className="text-white text-xl font-semibold">Round Ended</div>
                <div className="text-gray-400">Next round starting soon...</div>
              </div>
            </div>
          )}
        </div>

        {/* Game Info */}
        {gameState && (
          <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
            <div className="text-center">
              <div className="text-gray-400">Your Bet</div>
              <div className="text-white font-semibold">${gameState.bet_amount}</div>
            </div>
            <div className="text-center">
              <div className="text-gray-400">Potential Win</div>
              <div className="text-green-400 font-semibold">
                ${(gameState.bet_amount * currentMultiplier).toFixed(2)}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
