"use strict";(()=>{var e={};e.id=334,e.ids=[334],e.modules={758:(e,s,t)=>{t.r(s),t.d(s,{config:()=>d,default:()=>c,routeModule:()=>u});var a={};t.r(a),t.d(a,{default:()=>l});var o=t(3480),r=t(8667),n=t(6435),i=t(3546);async function l(e,s){if("GET"!==e.method)return s.status(405).json({error:"Method not allowed"});try{console.log("\uD83E\uDDEA Testing Phase 2 Database Schema Enhancement...");let e=(0,i.s4)();console.log("✅ Database initialized successfully");let t=e.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name IN (
        'user_statistics', 'leaderboards', 'game_sessions', 
        'achievements', 'user_achievements'
      )
    `).all();console.log("\uD83D\uDCCB Phase 2 Tables Created:"),t.forEach(e=>{console.log(`  ✅ ${e.name}`)});let a={tablesCreated:t.length,expectedTables:5,tables:t.map(e=>e.name),tests:[]};console.log("\uD83D\uDD0D Testing User Statistics Operations...");try{i.TS.create(1,"mines"),a.tests.push({name:"User Statistics Creation",status:"success"}),console.log("  ✅ User statistics creation works")}catch(e){a.tests.push({name:"User Statistics Creation",status:"failed",error:e.message}),console.log("  ❌ User statistics creation failed:",e.message)}console.log("\uD83C\uDFC6 Testing Leaderboard Operations...");try{i.Ym.updateEntry(1,"testuser","mines","profit",100.5,"daily"),a.tests.push({name:"Leaderboard Update",status:"success"}),console.log("  ✅ Leaderboard update works")}catch(e){a.tests.push({name:"Leaderboard Update",status:"failed",error:e.message}),console.log("  ❌ Leaderboard update failed:",e.message)}console.log("\uD83C\uDFAE Testing Game Session Operations...");try{i.X_.startSession(1),a.tests.push({name:"Game Session Creation",status:"success"}),console.log("  ✅ Game session creation works")}catch(e){a.tests.push({name:"Game Session Creation",status:"failed",error:e.message}),console.log("  ❌ Game session creation failed:",e.message)}return console.log("✨ Phase 2 Database Schema Enhancement Test Complete!"),s.status(200).json({success:!0,message:"Phase 2 Database Schema Enhancement Test Complete!",results:a})}catch(e){return console.error("❌ Database test failed:",e),s.status(500).json({success:!1,error:"Database test failed",details:e.message})}}let c=(0,n.M)(a,"default"),d=(0,n.M)(a,"config"),u=new o.PagesAPIRouteModule({definition:{kind:r.A.PAGES_API,page:"/api/test/phase2-db",pathname:"/api/test/phase2-db",bundlePath:"",filename:""},userland:a})},3873:e=>{e.exports=require("path")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},7550:e=>{e.exports=require("better-sqlite3")},9021:e=>{e.exports=require("fs")}};var s=require("../../../webpack-api-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[405],()=>t(758));module.exports=a})();