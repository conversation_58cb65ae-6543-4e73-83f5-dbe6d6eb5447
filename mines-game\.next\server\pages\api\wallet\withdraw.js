"use strict";(()=>{var e={};e.id=305,e.ids=[305],e.modules={829:e=>{e.exports=require("jsonwebtoken")},3139:e=>{e.exports=import("bcryptjs")},3873:e=>{e.exports=require("path")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},7550:e=>{e.exports=require("better-sqlite3")},8271:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>i});var a=t(9103),n=t(3546),u=e([a]);a=(u.then?(await u)():u)[0];let i=(0,a.ru)(async(e,r,t)=>{if((0,n.s4)(),"POST"!==e.method)return r.status(405).json({success:!1,error:"Method not allowed"});try{let{currency:s,amount:a,address:u}=e.body;if(!s||!["USDT","LTC"].includes(s))return r.status(400).json({success:!1,error:"Invalid currency. Must be USDT or LTC"});if("number"!=typeof a||a<=0)return r.status(400).json({success:!1,error:"Invalid amount"});if(!u||"string"!=typeof u)return r.status(400).json({success:!1,error:"Withdrawal address is required"});let i="USDT"===s?5:.01,o="USDT"===s?5e3:5;if(a<i)return r.status(400).json({success:!1,error:`Minimum withdrawal is ${i} ${s}`});if(a>o)return r.status(400).json({success:!1,error:`Maximum withdrawal is ${o} ${s}`});let c="USDT"===s?t.usdt_balance:t.ltc_balance;if(a>c)return r.status(400).json({success:!1,error:"Insufficient balance"});let l=c-a;n.Gy.updateBalance(t.id,s,l);let d=n.DR.create({user_id:t.id,type:"withdraw",currency:s,amount:a,status:"pending"}),f=n.Gy.findById(t.id);return r.status(200).json({success:!0,transaction:d,balance:{usdt:f?.usdt_balance||0,ltc:f?.ltc_balance||0},message:`Withdrawal of ${a} ${s} initiated. It will be processed within 24 hours.`})}catch(e){return console.error("Withdraw API error:",e),r.status(500).json({success:!1,error:"Internal server error"})}});s()}catch(e){s(e)}})},9021:e=>{e.exports=require("fs")},9103:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.d(r,{DY:()=>y,Eb:()=>w,Lx:()=>h,OB:()=>p,Tf:()=>g,VX:()=>m,b9:()=>d,ru:()=>f});var a=t(829),n=t.n(a),u=t(3139),i=t(3546),o=e([u]);u=(o.then?(await o)():o)[0];let b="your-super-secret-jwt-key-change-this-in-production-make-it-very-long-and-random";async function c(e){return u.default.hash(e,12)}async function l(e,r){return u.default.compare(e,r)}function d(e){let r=function(e){let r=e.headers.authorization;if(r&&r.startsWith("Bearer "))return r.substring(7);let t=e.cookies.token;return t||null}(e);if(!r)return null;let t=function(e){try{return n().verify(e,b)}catch(e){return null}}(r);return t&&t.userId?i.Gy.findById(t.userId):null}function f(e){return async(r,t)=>{try{let s=d(r);if(!s)return t.status(401).json({success:!1,error:"Authentication required"});await e(r,t,s)}catch(e){console.error("Auth middleware error:",e),t.status(500).json({success:!1,error:"Internal server error"})}}}async function y(e,r,t){try{let s=!e||e.length<3||e.length>20?"Username must be between 3 and 20 characters":/^[a-zA-Z0-9_]+$/.test(e)?r&&/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r)?!t||t.length<8?"Password must be at least 8 characters long":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(t)?null:"Password must contain at least one uppercase letter, one lowercase letter, and one number":"Please provide a valid email address":"Username can only contain letters, numbers, and underscores";if(s)return{success:!1,error:s};if(i.Gy.findByEmail(r))return{success:!1,error:"Email already registered"};if(i.Gy.findByUsername(e))return{success:!1,error:"Username already taken"};let a=await c(t),{password_hash:n,...u}=i.Gy.create(e,r,a);return{success:!0,user:u}}catch(e){return console.error("Registration error:",e),{success:!1,error:"Failed to register user"}}}async function h(e,r){try{let t=e&&r?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)?null:"Please provide a valid email address":"Email and password are required";if(t)return{success:!1,error:t};let s=i.Gy.findByEmail(e);if(!s||!await l(r,s.password_hash))return{success:!1,error:"Invalid email or password"};let a=function(e){let r={userId:e.id,username:e.username,email:e.email};return n().sign(r,b,{expiresIn:"7d"})}(s),{password_hash:u,...o}=s;return{success:!0,user:o,token:a}}catch(e){return console.error("Login error:",e),{success:!1,error:"Failed to login"}}}function m(e,r){e.setHeader("Set-Cookie",[`token=${r}; HttpOnly; Path=/; Max-Age=604800; SameSite=Strict; Secure`])}function p(e){e.setHeader("Set-Cookie",["token=; HttpOnly; Path=/; Max-Age=0; SameSite=Strict"])}let v=new Map;function w(e,r=5,t=9e5){let s=Date.now(),a=v.get(e);return!a||s>a.resetTime?(v.set(e,{count:1,resetTime:s+t}),!0):!(a.count>=r)&&(a.count++,!0)}function g(e){let r=e.headers["x-forwarded-for"];return(r?Array.isArray(r)?r[0]:r.split(",")[0]:e.socket.remoteAddress)||"unknown"}s()}catch(e){s(e)}})},9692:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>l,default:()=>c,routeModule:()=>d});var a=t(3480),n=t(8667),u=t(6435),i=t(8271),o=e([i]);i=(o.then?(await o)():o)[0];let c=(0,u.M)(i,"default"),l=(0,u.M)(i,"config"),d=new a.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/wallet/withdraw",pathname:"/api/wallet/withdraw",bundlePath:"",filename:""},userland:i});s()}catch(e){s(e)}})}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[405],()=>t(9692));module.exports=s})();