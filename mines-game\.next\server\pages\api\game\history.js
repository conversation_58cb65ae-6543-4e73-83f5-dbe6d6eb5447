"use strict";(()=>{var e={};e.id=632,e.ids=[632],e.modules={829:e=>{e.exports=require("jsonwebtoken")},2616:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>l,default:()=>c,routeModule:()=>d});var n=t(3480),a=t(8667),o=t(6435),i=t(3734),u=e([i]);i=(u.then?(await u)():u)[0];let c=(0,o.M)(i,"default"),l=(0,o.M)(i,"config"),d=new n.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/game/history",pathname:"/api/game/history",bundlePath:"",filename:""},userland:i});s()}catch(e){s(e)}})},3139:e=>{e.exports=import("bcryptjs")},3734:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>i});var n=t(9103),a=t(3546),o=e([n]);n=(o.then?(await o)():o)[0];let i=(0,n.ru)(async(e,r,t)=>{if((0,a.s4)(),"GET"!==e.method)return r.status(405).json({success:!1,error:"Method not allowed"});try{let{game_type:s,limit:n}=e.query,o=n?parseInt(n):50;if(o>100)return r.status(400).json({success:!1,error:"Limit cannot exceed 100"});let i=a.dW.findByUserId(t.id,o,s).map(e=>({...e,server_seed:"active"===e.status?"hidden":e.server_seed,..."mines"===e.game_type&&"active"===e.status?{mine_positions:[]}:{}}));return r.status(200).json({success:!0,games:i,total:i.length})}catch(e){return console.error("Game history API error:",e),r.status(500).json({success:!1,error:"Internal server error"})}});s()}catch(e){s(e)}})},3873:e=>{e.exports=require("path")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},7550:e=>{e.exports=require("better-sqlite3")},9021:e=>{e.exports=require("fs")},9103:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.d(r,{DY:()=>y,Eb:()=>g,Lx:()=>f,OB:()=>p,Tf:()=>w,VX:()=>h,b9:()=>d,ru:()=>m});var n=t(829),a=t.n(n),o=t(3139),i=t(3546),u=e([o]);o=(u.then?(await u)():u)[0];let v="your-super-secret-jwt-key-change-this-in-production-make-it-very-long-and-random";async function c(e){return o.default.hash(e,12)}async function l(e,r){return o.default.compare(e,r)}function d(e){let r=function(e){let r=e.headers.authorization;if(r&&r.startsWith("Bearer "))return r.substring(7);let t=e.cookies.token;return t||null}(e);if(!r)return null;let t=function(e){try{return a().verify(e,v)}catch(e){return null}}(r);return t&&t.userId?i.Gy.findById(t.userId):null}function m(e){return async(r,t)=>{try{let s=d(r);if(!s)return t.status(401).json({success:!1,error:"Authentication required"});await e(r,t,s)}catch(e){console.error("Auth middleware error:",e),t.status(500).json({success:!1,error:"Internal server error"})}}}async function y(e,r,t){try{let s=!e||e.length<3||e.length>20?"Username must be between 3 and 20 characters":/^[a-zA-Z0-9_]+$/.test(e)?r&&/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r)?!t||t.length<8?"Password must be at least 8 characters long":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(t)?null:"Password must contain at least one uppercase letter, one lowercase letter, and one number":"Please provide a valid email address":"Username can only contain letters, numbers, and underscores";if(s)return{success:!1,error:s};if(i.Gy.findByEmail(r))return{success:!1,error:"Email already registered"};if(i.Gy.findByUsername(e))return{success:!1,error:"Username already taken"};let n=await c(t),{password_hash:a,...o}=i.Gy.create(e,r,n);return{success:!0,user:o}}catch(e){return console.error("Registration error:",e),{success:!1,error:"Failed to register user"}}}async function f(e,r){try{let t=e&&r?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)?null:"Please provide a valid email address":"Email and password are required";if(t)return{success:!1,error:t};let s=i.Gy.findByEmail(e);if(!s||!await l(r,s.password_hash))return{success:!1,error:"Invalid email or password"};let n=function(e){let r={userId:e.id,username:e.username,email:e.email};return a().sign(r,v,{expiresIn:"7d"})}(s),{password_hash:o,...u}=s;return{success:!0,user:u,token:n}}catch(e){return console.error("Login error:",e),{success:!1,error:"Failed to login"}}}function h(e,r){e.setHeader("Set-Cookie",[`token=${r}; HttpOnly; Path=/; Max-Age=604800; SameSite=Strict; Secure`])}function p(e){e.setHeader("Set-Cookie",["token=; HttpOnly; Path=/; Max-Age=0; SameSite=Strict"])}let x=new Map;function g(e,r=5,t=9e5){let s=Date.now(),n=x.get(e);return!n||s>n.resetTime?(x.set(e,{count:1,resetTime:s+t}),!0):!(n.count>=r)&&(n.count++,!0)}function w(e){let r=e.headers["x-forwarded-for"];return(r?Array.isArray(r)?r[0]:r.split(",")[0]:e.socket.remoteAddress)||"unknown"}s()}catch(e){s(e)}})}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[405],()=>t(2616));module.exports=s})();