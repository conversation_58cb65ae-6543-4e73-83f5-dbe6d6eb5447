"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_ENDPOINTS: () => (/* binding */ API_ENDPOINTS),\n/* harmony export */   GAME_CONFIG: () => (/* binding */ GAME_CONFIG),\n/* harmony export */   SessionStorage: () => (/* binding */ SessionStorage),\n/* harmony export */   calculateMultiplier: () => (/* binding */ calculateMultiplier),\n/* harmony export */   calculateProfit: () => (/* binding */ calculateProfit),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   generateClientSeed: () => (/* binding */ generateClientSeed),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidPassword: () => (/* binding */ isValidPassword),\n/* harmony export */   randomInt: () => (/* binding */ randomInt),\n/* harmony export */   shuffleArray: () => (/* binding */ shuffleArray),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   throttle: () => (/* binding */ throttle)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(pages-dir-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(pages-dir-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * Utility function to merge Tailwind CSS classes\n */ function cn() {\n    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputs[_key] = arguments[_key];\n    }\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Format currency values with proper decimal places\n */ function formatCurrency(amount) {\n    let currency = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'USDT', compact = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n    // Handle null/undefined values\n    if (amount == null) {\n        const decimals = currency === 'USDT' ? 2 : 8;\n        return 0..toFixed(decimals);\n    }\n    if (compact && Math.abs(amount) >= 1000) {\n        return formatNumber(amount);\n    }\n    const decimals = currency === 'USDT' ? 2 : 8;\n    return amount.toFixed(decimals);\n}\n/**\n * Format large numbers with K, M, B suffixes\n */ function formatNumber(num) {\n    if (num >= 1e9) {\n        return (num / 1e9).toFixed(1) + 'B';\n    }\n    if (num >= 1e6) {\n        return (num / 1e6).toFixed(1) + 'M';\n    }\n    if (num >= 1e3) {\n        return (num / 1e3).toFixed(1) + 'K';\n    }\n    return num.toString();\n}\n/**\n * Generate a random string for client seeds\n */ function generateClientSeed() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n}\n/**\n * Validate email format\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Validate password strength\n */ function isValidPassword(password) {\n    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number\n    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$/;\n    return passwordRegex.test(password);\n}\n/**\n * Calculate multiplier based on mines and revealed cells\n */ function calculateMultiplier(mineCount, revealedCount) {\n    let gridSize = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 25;\n    if (revealedCount === 0) return 1;\n    const safeCells = gridSize - mineCount;\n    const remainingSafeCells = safeCells - revealedCount;\n    if (remainingSafeCells <= 0) return 1;\n    // Base multiplier calculation with house edge\n    const baseMultiplier = safeCells / remainingSafeCells;\n    const houseEdge = 0.04; // 4% house edge\n    return Math.max(1, baseMultiplier * (1 - houseEdge));\n}\n/**\n * Calculate potential profit\n */ function calculateProfit(betAmount, multiplier) {\n    return betAmount * multiplier - betAmount;\n}\n/**\n * Debounce function for performance optimization\n */ function debounce(func, wait) {\n    let timeout;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * Throttle function for performance optimization\n */ function throttle(func, limit) {\n    let inThrottle;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n/**\n * Sleep utility for async operations\n */ function sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n/**\n * Generate a random integer between min and max (inclusive)\n */ function randomInt(min, max) {\n    return Math.floor(Math.random() * (max - min + 1)) + min;\n}\n/**\n * Shuffle array using Fisher-Yates algorithm\n */ function shuffleArray(array) {\n    const shuffled = [\n        ...array\n    ];\n    for(let i = shuffled.length - 1; i > 0; i--){\n        const j = Math.floor(Math.random() * (i + 1));\n        [shuffled[i], shuffled[j]] = [\n            shuffled[j],\n            shuffled[i]\n        ];\n    }\n    return shuffled;\n}\n/**\n * Format date to readable string\n */ function formatDate(date) {\n    const d = new Date(date);\n    return d.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n    });\n}\n/**\n * Copy text to clipboard\n */ async function copyToClipboard(text) {\n    try {\n        await navigator.clipboard.writeText(text);\n        return true;\n    } catch (err) {\n        console.error('Failed to copy text: ', err);\n        return false;\n    }\n}\n/**\n * Game configuration constants\n */ const GAME_CONFIG = {\n    GRID_SIZE: 25,\n    MIN_MINES: 1,\n    MAX_MINES: 24,\n    MIN_BET: 0.01,\n    MAX_BET: 1000,\n    HOUSE_EDGE: 0.04,\n    BASE_MULTIPLIER: 1.0\n};\n/**\n * API endpoints\n */ const API_ENDPOINTS = {\n    AUTH: {\n        LOGIN: '/api/auth/login',\n        SIGNUP: '/api/auth/signup',\n        ME: '/api/auth/me',\n        LOGOUT: '/api/auth/logout'\n    },\n    GAME: {\n        START: '/api/game/start',\n        MOVE: '/api/game/move',\n        CASHOUT: '/api/game/cashout',\n        HISTORY: '/api/game/history',\n        ACTIVE: '/api/game/active',\n        CONFIG: '/api/game/config',\n        LIST: '/api/game/list',\n        STATS: '/api/game/stats'\n    },\n    // Legacy endpoints for backward compatibility\n    MINES: {\n        START: '/api/game/start',\n        PICK: '/api/game/move',\n        CASHOUT: '/api/game/cashout',\n        HISTORY: '/api/game/history'\n    },\n    WALLET: {\n        DEPOSIT: '/api/wallet/deposit',\n        WITHDRAW: '/api/wallet/withdraw',\n        BALANCE: '/api/wallet/balance'\n    }\n};\n/**\n * Session storage utilities for tracking current session stats\n */ const SessionStorage = {\n    SESSION_KEY: 'betoctave_session_stats',\n    /**\n   * Get current session data\n   */ getSession: ()=>{\n        if (false) {}\n        try {\n            const sessionData = localStorage.getItem(SessionStorage.SESSION_KEY);\n            return sessionData ? JSON.parse(sessionData) : null;\n        } catch (error) {\n            console.error('Error reading session data:', error);\n            return null;\n        }\n    },\n    /**\n   * Initialize or reset session\n   */ resetSession: ()=>{\n        var _SessionStorage_getSession;\n        if (false) {}\n        const sessionData = {\n            startTime: new Date().toISOString(),\n            resetCount: (((_SessionStorage_getSession = SessionStorage.getSession()) === null || _SessionStorage_getSession === void 0 ? void 0 : _SessionStorage_getSession.resetCount) || 0) + 1\n        };\n        try {\n            localStorage.setItem(SessionStorage.SESSION_KEY, JSON.stringify(sessionData));\n        } catch (error) {\n            console.error('Error saving session data:', error);\n        }\n    },\n    /**\n   * Get session start time\n   */ getSessionStartTime: ()=>{\n        var _SessionStorage_getSession;\n        const session = SessionStorage.getSession();\n        if (session === null || session === void 0 ? void 0 : session.startTime) {\n            return session.startTime;\n        }\n        // If no session exists, create one and return its start time\n        SessionStorage.resetSession();\n        return ((_SessionStorage_getSession = SessionStorage.getSession()) === null || _SessionStorage_getSession === void 0 ? void 0 : _SessionStorage_getSession.startTime) || new Date().toISOString();\n    },\n    /**\n   * Check if session exists\n   */ hasSession: ()=>{\n        return SessionStorage.getSession() !== null;\n    },\n    /**\n   * Initialize session if it doesn't exist\n   */ initializeSession: ()=>{\n        if (!SessionStorage.hasSession()) {\n            console.log('📊 Initializing new session');\n            SessionStorage.resetSession();\n        } else {\n            console.log('📊 Session already exists:', SessionStorage.getSession());\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./lib/utils.ts\n"));

/***/ })

});