"use strict";exports.id=822,exports.ids=[822],exports.modules={4490:(e,t,r)=>{r.d(t,{El:()=>n,IB:()=>s,L8:()=>u,fy:()=>o});var a=r(5511),i=r.n(a);function s(){return i().randomBytes(32).toString("hex")}function n(){return i().randomBytes(16).toString("hex")}function l(e,t,r=0){let a=`${e}:${t}:${r}`;return i().createHash("sha256").update(a).digest("hex")}function o(e,t,r,a=25){let i=l(e,t),s=[],n=Array.from({length:a},(e,t)=>t),u=0;for(;s.length<r&&n.length>0;){let r=parseInt(i.slice(8*u,(u+1)*8),16)%n.length,a=n[r];s.push(a),n.splice(r,1),8*++u>=i.length&&(l(e,t,u),u=i.length/8)}return s.sort((e,t)=>e-t)}function u(e){return i().createHash("sha256").update(e).digest("hex")}},6851:(e,t,r)=>{r.d(t,{R$:()=>i,TL:()=>u,WJ:()=>l,r1:()=>s,wz:()=>o});var a=r(4490);class i{validateBaseParams(e){return"number"==typeof e&&!(e<=0)&&!(e<this.config.minBet)&&!(e>this.config.maxBet)}generateBaseGameData(e,t,r){return{user_id:e,game_type:this.gameType,bet_amount:t,current_multiplier:1,status:"active",server_seed:(0,a.IB)(),client_seed:r||(0,a.El)(),profit:0}}calculateProfit(e,t){return e*t-e}applyHouseEdge(e){return e*(1-this.config.houseEdge)}validateMultiplier(e){return e<=this.config.maxMultiplier}generateGameHash(e,t,a=0){let i=r(5511),s=`${e}:${t}:${a}`;return i.createHash("sha256").update(s).digest("hex")}generateRandomFromSeeds(e,t,r=0){return parseInt(this.generateGameHash(e,t,r).substring(0,8),16)/0xffffffff}generateRandomInt(e,t,r,a,i=0){return Math.floor(this.generateRandomFromSeeds(r,a,i)*(t-e+1))+e}generateRandomArray(e,t,r,a,i,s=0){let n=[],l=r-t+1;for(let r=0;r<e;r++){let e=Math.floor(this.generateRandomFromSeeds(a,i,s+r)*l)+t;n.push(e)}return n}shuffleArray(e,t,r,a=0){let i=[...e];for(let e=i.length-1;e>0;e--){let s=Math.floor(this.generateRandomFromSeeds(t,r,a+e)*(e+1));[i[e],i[s]]=[i[s],i[e]]}return i}validateGameOwnership(e,t){return e.user_id===t}isGameActive(e){return"active"===e.status}logGameAction(e,t,r){}}var s=function(e){return e.START_GAME="START_GAME",e.MAKE_MOVE="MAKE_MOVE",e.CASH_OUT="CASH_OUT",e.END_GAME="END_GAME",e.CANCEL_GAME="CANCEL_GAME",e}({});class n extends Error{constructor(e,t,r){super(e),this.code=t,this.gameType=r,this.name="GameError"}}class l extends n{constructor(e,t="Invalid game parameters"){super(t,"INVALID_PARAMS",e)}}class o extends n{constructor(e,t="Game is not active"){super(t,"GAME_NOT_ACTIVE",e)}}class u extends n{constructor(e,t){super(e,"INVALID_ACTION",t)}}},8822:(e,t,r)=>{r.d(t,{L:()=>d});class a{registerGame(e,t){this.games.has(e.id)&&console.warn(`Game ${e.id} is already registered. Overwriting...`),this.games.set(e.id,e),this.providers.set(e.id,t),console.log(`✅ Registered game: ${e.name} (${e.id})`)}getGameConfig(e){return this.games.get(e)}getGameProvider(e){return this.providers.get(e)}getAllGames(){return Array.from(this.games.values())}getGamesByCategory(e){return this.getAllGames().filter(t=>t.category===e)}getActiveGames(){return this.getAllGames().filter(e=>e.isActive)}getFeaturedGames(){return this.getAllGames().filter(e=>e.isFeatured&&e.isActive)}getNewGames(){return this.getAllGames().filter(e=>e.isNew&&e.isActive)}searchGames(e){let t=e.toLowerCase();return this.getAllGames().filter(e=>e.name.toLowerCase().includes(t)||e.description.toLowerCase().includes(t)||e.features.some(e=>e.toLowerCase().includes(t)))}isGameRegistered(e){return this.games.has(e)}getRegistryStats(){let e=this.getAllGames(),t=this.getActiveGames(),r=e.reduce((e,t)=>(e[t.category]=(e[t.category]||0)+1,e),{});return{totalGames:e.length,activeGames:t.length,gamesByCategory:r}}validateGameConfig(e){for(let t of["id","name","description","category","minBet","maxBet"])if(!(t in e))return console.error(`Game config missing required field: ${t}`),!1;return e.minBet>=e.maxBet?(console.error("minBet must be less than maxBet"),!1):!(e.houseEdge<0)&&!(e.houseEdge>1)||(console.error("houseEdge must be between 0 and 1"),!1)}unregisterGame(e){let t=this.games.has(e);return this.games.delete(e),this.providers.delete(e),t&&console.log(`🗑️ Unregistered game: ${e}`),t}clear(){this.games.clear(),this.providers.clear(),console.log("\uD83E\uDDF9 Cleared all registered games")}constructor(){this.games=new Map,this.providers=new Map}}let i=new a;var s=r(6851);class n extends s.R${validateGameParams(e){let{betAmount:t,mineCount:r}=e;return!!this.validateBaseParams(t)&&"number"==typeof r&&!(r<this.MIN_MINES)&&!(r>this.MAX_MINES)}calculateMultiplier(e,t){let r=t?.revealedCells??e.revealed_cells.length;if(0===r)return 1;let a=this.GRID_SIZE-e.mine_count;if(a-r<=0)return 1;let i=1;for(let e=0;e<r;e++)i*=1/((a-e)/(this.GRID_SIZE-e));return Math.min(i=this.applyHouseEdge(i),this.config.maxMultiplier)}generateGameData(e){let{userId:t,betAmount:r,mineCount:a,clientSeed:i}=e;if(!this.validateGameParams({betAmount:r,mineCount:a}))throw new s.WJ(this.gameType);let n=this.generateBaseGameData(t,r,i),l=this.generateMinePositions(n.server_seed,n.client_seed,a);return{...n,game_type:"mines",grid_size:this.GRID_SIZE,mine_count:a,revealed_cells:[],mine_positions:l}}async processGameAction(e,t){if(this.logGameAction(e,t),!this.isGameActive(e))throw new s.wz(this.gameType);switch(t.type){case s.r1.MAKE_MOVE:return this.processRevealCell(e,t.payload.cellIndex);case s.r1.CASH_OUT:return this.processCashOut(e);default:throw Error(`Unknown action type: ${t.type}`)}}processRevealCell(e,t){if(t<0||t>=this.GRID_SIZE)throw Error("Invalid cell index");if(e.revealed_cells.includes(t))throw Error("Cell already revealed");let r=[...e.revealed_cells,t];if(e.mine_positions.includes(t))return{...e,revealed_cells:r,status:"lost",profit:-e.bet_amount,current_multiplier:0};{let t=this.calculateMultiplier(e,{revealedCells:r.length}),a=this.calculateProfit(e.bet_amount,t),i=this.GRID_SIZE-e.mine_count,s=r.length===i;return{...e,revealed_cells:r,current_multiplier:t,profit:a,status:s?"won":"active"}}}processCashOut(e){if(0===e.revealed_cells.length)throw Error("Cannot cash out without revealing any cells");let t=this.calculateProfit(e.bet_amount,e.current_multiplier);return{...e,status:"cashed_out",profit:t}}generateMinePositions(e,t,r){let a=Array.from({length:this.GRID_SIZE},(e,t)=>t);return this.shuffleArray(a,e,t).slice(0,r).sort((e,t)=>e-t)}getSafeCellsRemaining(e){return this.GRID_SIZE-e.mine_count-e.revealed_cells.length}getNextMultiplier(e){if(!this.isGameActive(e))return e.current_multiplier;let t=e.revealed_cells.length+1;return this.calculateMultiplier(e,{revealedCells:t})}canCashOut(e){return this.isGameActive(e)&&e.revealed_cells.length>0}constructor(...e){super(...e),this.gameType="mines",this.config={id:"mines",name:"Mines",description:"Click tiles to reveal gems while avoiding hidden mines. Cash out anytime to secure your winnings!",icon:"\uD83D\uDC8E",category:"originals",minBet:.01,maxBet:1e3,houseEdge:.04,maxMultiplier:1e3,features:["Provably Fair","Instant Play","Auto Cashout","Custom Risk"],isActive:!0,isFeatured:!0,isNew:!1},this.GRID_SIZE=25,this.MIN_MINES=1,this.MAX_MINES=24}}class l extends s.R${validateGameParams(e){let{betAmount:t,targetNumber:r,rollUnder:a}=e;return!!this.validateBaseParams(t)&&"number"==typeof r&&!(r<this.MIN_TARGET)&&!(r>this.MAX_TARGET)&&"boolean"==typeof a}calculateMultiplier(e,t){let{target_number:r,roll_under:a}=e,i=this.calculateWinChance(r,a);return Math.max(1.01,Math.round(1e4*((100-100*this.config.houseEdge)/i))/1e4)}calculateWinChance(e,t){return t?e-1:100-e}generateGameData(e){let{userId:t,betAmount:r,targetNumber:a,rollUnder:i,clientSeed:n}=e;if(!this.validateGameParams({betAmount:r,targetNumber:a,rollUnder:i}))throw new s.WJ(this.gameType);let l=this.generateBaseGameData(t,r,n),o=this.calculateMultiplier({...l,game_type:"dice",target_number:a,roll_under:i});return{...l,game_type:"dice",target_number:a,roll_under:i,current_multiplier:o,result:void 0}}async processGameAction(e,t){if(t.type===s.r1.MAKE_MOVE)return this.rollDice(e);throw new s.TL(`Unsupported action type: ${t.type}`,this.gameType)}rollDice(e){if("active"!==e.status)throw new s.TL("Game is not active",this.gameType);if(void 0!==e.result)throw new s.TL("Dice already rolled",this.gameType);let t=this.generateRandomInt(this.DICE_MIN,this.DICE_MAX,e.server_seed,e.client_seed,0),r=this.checkWin(t,e.target_number,e.roll_under),a=r?e.bet_amount*(e.current_multiplier-1):-e.bet_amount;return{...e,result:t,status:r?"won":"lost",profit:a,updated_at:new Date().toISOString()}}checkWin(e,t,r){return r?e<t:e>t}getGameStats(e){let t=this.calculateWinChance(e.target_number,e.roll_under);return{targetNumber:e.target_number,rollUnder:e.roll_under,winChance:t,multiplier:e.current_multiplier,result:e.result,profit:e.profit,status:e.status}}constructor(...e){super(...e),this.gameType="dice",this.config={id:"dice",name:"Dice",description:"Roll the dice and predict if the result will be over or under your chosen number. Simple yet thrilling!",icon:"\uD83C\uDFB2",category:"originals",minBet:.01,maxBet:1e3,houseEdge:.01,maxMultiplier:9900,features:["Provably Fair","Instant Play","Custom Multiplier"],isActive:!0,isFeatured:!0,isNew:!0},this.MIN_TARGET=2,this.MAX_TARGET=98,this.DICE_MIN=1,this.DICE_MAX=100}}class o extends s.R${validateGameParams(e){let{bet_amount:t,auto_cash_out:r}=e;return!!this.validateBaseParams(t)&&(void 0===r||"number"==typeof r&&!(r<1.01)&&!(r>this.config.maxMultiplier))&&!0}calculateMultiplier(e,t){if("flying"!==e.phase)return 1;let r=Math.pow(1.002,(e.time_elapsed||0)/100);return e.crash_point&&r>=e.crash_point?e.crash_point:Math.round(100*r)/100}generateGameData(e){console.log("\uD83C\uDFAE CrashGameProvider - generateGameData called with params:",e);let{bet_amount:t,auto_cash_out:r,user_id:a,client_seed:i}=e,s=this.generateBaseGameData(a,t,i);console.log("\uD83C\uDFAE CrashGameProvider - baseData generated:",s);let n=this.generateCrashPoint(s.server_seed,s.client_seed);console.log("\uD83C\uDFAE CrashGameProvider - crashPoint generated:",n);let l={...s,game_type:"crash",crash_point:n,auto_cash_out:r,phase:"betting",time_elapsed:0,cashed_out:!1,round_id:this.generateRoundId()};return console.log("\uD83C\uDFAE CrashGameProvider - final result:",l),console.log("\uD83C\uDFAE CrashGameProvider - result keys:",Object.keys(l)),l}async processGameAction(e,t){switch(this.logGameAction(e,t),t.type){case s.r1.START_GAME:return this.handleStartGame(e,t.payload);case s.r1.CASH_OUT:return this.handleCashOut(e,t.payload);case"UPDATE_MULTIPLIER":return this.handleUpdateMultiplier(e,t.payload);case"CRASH":return this.handleCrash(e);case"START_ROUND":return this.handleStartRound(e);case"END_ROUND":return this.handleEndRound(e);default:throw Error(`Unknown action type: ${t.type}`)}}handleStartGame(e,t){return{...e,phase:"betting",status:"active",time_elapsed:0,current_multiplier:1,cashed_out:!1}}handleCashOut(e,t){if("flying"!==e.phase||e.cashed_out)throw Error("Cannot cash out at this time");let r=this.calculateMultiplier(e),a=this.calculateProfit(e.bet_amount,r);return{...e,cashed_out:!0,cash_out_at:r,current_multiplier:r,profit:a,status:"cashed_out"}}handleUpdateMultiplier(e,t){if("flying"!==e.phase)return e;let r=this.calculateMultiplier({...e,time_elapsed:t.timeElapsed});return e.crash_point&&r>=e.crash_point?this.handleCrash({...e,time_elapsed:t.timeElapsed,current_multiplier:e.crash_point}):e.auto_cash_out&&r>=e.auto_cash_out&&!e.cashed_out?this.handleCashOut({...e,time_elapsed:t.timeElapsed,current_multiplier:r},{}):{...e,time_elapsed:t.timeElapsed,current_multiplier:r}}handleCrash(e){let t=e.cashed_out?e.profit:-e.bet_amount;return{...e,phase:"crashed",status:e.cashed_out?"cashed_out":"lost",profit:t,current_multiplier:e.crash_point||e.current_multiplier}}handleStartRound(e){return{...e,phase:"flying",time_elapsed:0,current_multiplier:1}}handleEndRound(e){return{...e,phase:"waiting"}}generateCrashPoint(e,t){let r=this.generateRandomFromSeeds(e,t,0);return Math.min(Math.max(1.01,(1-this.config.houseEdge)/r),this.config.maxMultiplier)}generateRoundId(){return`crash_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}constructor(...e){super(...e),this.gameType="crash",this.config={id:"crash",name:"Crash",description:"Watch the multiplier rise and cash out before it crashes! The longer you wait, the higher the multiplier, but if you wait too long, you lose everything.",icon:"\uD83D\uDE80",category:"originals",minBet:.01,maxBet:1e3,houseEdge:.01,maxMultiplier:1e6,features:["Provably Fair","Real-time","Auto Cash Out","Live Multiplier"],isActive:!0,isNew:!0,isFeatured:!0}}}class u extends s.R${constructor(e){super(),this.gameType=e.id,this.config=e}validateGameParams(e){return e&&"number"==typeof e.betAmount&&e.betAmount>0}calculateMultiplier(e,t){return e.current_multiplier||1}generateGameData(e){return{...this.generateBaseGameData(e.userId,e.betAmount,e.clientSeed),game_type:this.gameType}}async processGameAction(e,t){return e}}let c=[{id:"plinko",name:"Plinko",description:"Drop balls down the plinko board and watch them bounce into different multiplier slots. Pure luck and excitement!",icon:"\uD83C\uDFC0",category:"originals",minBet:.01,maxBet:1e3,houseEdge:.01,maxMultiplier:1e3,features:["Provably Fair","Multiple Risk Levels","Animated Gameplay"],isActive:!0,isFeatured:!0,isNew:!1},{id:"limbo",name:"Limbo",description:"Choose your target multiplier and see if you can reach it. The higher the target, the lower the chance!",icon:"\uD83C\uDFAF",category:"originals",minBet:.01,maxBet:1e3,houseEdge:.01,maxMultiplier:1e6,features:["Provably Fair","Instant Results","Unlimited Multiplier"],isActive:!0,isFeatured:!1,isNew:!1},{id:"wheel",name:"Wheel",description:"Spin the wheel of fortune and win big! Choose your risk level and watch the wheel decide your fate.",icon:"\uD83C\uDFA1",category:"originals",minBet:.01,maxBet:1e3,houseEdge:.01,maxMultiplier:50,features:["Provably Fair","Multiple Risk Levels","Visual Spinning"],isActive:!0,isFeatured:!1,isNew:!0},{id:"blackjack",name:"Blackjack",description:"Classic card game where you try to get as close to 21 as possible without going over. Beat the dealer!",icon:"\uD83C\uDCCF",category:"table",minBet:.01,maxBet:1e3,houseEdge:.005,maxMultiplier:3,features:["Classic Rules","Strategy Based","Low House Edge"],isActive:!0,isFeatured:!1,isNew:!1},{id:"roulette",name:"Roulette",description:"Place your bets on the roulette table and watch the ball spin. Red or black? Odd or even? Your choice!",icon:"\uD83C\uDFB0",category:"table",minBet:.01,maxBet:1e3,houseEdge:.027,maxMultiplier:36,features:["European Rules","Multiple Bet Types","Live Animation"],isActive:!0,isFeatured:!1,isNew:!1}],h=()=>c.map(e=>new u(e));class m{constructor(){this.initialized=!1}static getInstance(){return m.instance||(m.instance=new m),m.instance}async initialize(){if(this.initialized)return void console.log("\uD83C\uDFAE Game factory already initialized");console.log("\uD83C\uDFAE Initializing game factory...");try{await this.registerAllGames(),this.initialized=!0,console.log("✅ Game factory initialized successfully");let e=i.getRegistryStats();console.log(`📊 Registered ${e.totalGames} games (${e.activeGames} active)`),console.log("\uD83D\uDCCB Games by category:",e.gamesByCategory)}catch(e){throw console.error("❌ Failed to initialize game factory:",e),e}}async registerAllGames(){for(let e of[new n,new l,new o,...h()])try{i.registerGame(e.config,e)}catch(t){console.error(`Failed to register ${e.gameType} game:`,t)}}async createGame(e,t){try{this.initialized||await this.initialize();let r=i.getGameProvider(e);if(console.log(`🎮 GameFactory - Retrieved provider for ${e}:`,r?.constructor.name),!r)return{success:!1,error:`Game type '${e}' is not registered`};if(console.log(`🎮 GameFactory - Validating params for ${e}:`,t),!r.validateGameParams(t))return{success:!1,error:"Invalid game parameters"};console.log(`🎮 GameFactory - Calling generateGameData for ${e}`);let a=r.generateGameData(t);return console.log(`🎮 GameFactory - Generated game data:`,a),{success:!0,game:a}}catch(t){return console.error(`Error creating ${e} game:`,t),{success:!1,error:t instanceof Error?t.message:"Unknown error"}}}async processGameAction(e,t,r,a){try{this.initialized||await this.initialize();let s=i.getGameProvider(e);if(!s)return{success:!1,error:`Game type '${e}' is not registered`};let n=await s.processGameAction(t,{type:r,payload:a});return{success:!0,gameState:n}}catch(t){return console.error(`Error processing ${e} action:`,t),{success:!1,error:t instanceof Error?t.message:"Unknown error"}}}calculateMultiplier(e,t,r){if(!this.initialized)return console.warn("Game factory not initialized, returning default multiplier"),1;let a=i.getGameProvider(e);return a?a.calculateMultiplier(t,r):(console.warn(`Game type '${e}' not found, returning default multiplier`),1)}getGameConfig(e){return i.getGameConfig(e)}getAllGames(){return i.getAllGames()}getGamesByCategory(e){return i.getGamesByCategory(e)}searchGames(e){return i.searchGames(e)}isGameAvailable(e){let t=i.getGameConfig(e);return t?.isActive??!1}getGameProvider(e){return i.getGameProvider(e)}reset(){i.clear(),this.initialized=!1,console.log("\uD83D\uDD04 Game factory reset")}isInitialized(){return this.initialized}}let d=m.getInstance();d.initialize().catch(console.error)}};