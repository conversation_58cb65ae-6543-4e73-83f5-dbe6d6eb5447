"use strict";(()=>{var e={};e.id=839,e.ids=[839],e.modules={829:e=>{e.exports=require("jsonwebtoken")},1938:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>l});var s=r(9103),o=r(3546),n=r(8822),i=e([s]);s=(i.then?(await i)():i)[0];let l=(0,s.ru)(async(e,t,r)=>{if((0,o.s4)(),console.log("\uD83D\uDE80 API /game/start - Request received, method:",e.method),console.log("\uD83D\uDE80 API /game/start - User from auth middleware:",r?{id:r.id,balance:r.usdt_balance}:"NO USER"),"POST"!==e.method)return t.status(405).json({success:!1,error:"Method not allowed"});try{let{game_type:a,bet_amount:s,client_seed:i,...l}=e.body;if(console.log("\uD83D\uDE80 API /game/start - Full request body:",JSON.stringify(e.body,null,2)),console.log("\uD83D\uDE80 API /game/start - Extracted values:",{game_type:a,bet_amount:s,client_seed:i,gameParams:l}),console.log("\uD83D\uDE80 API /game/start - User info:",{id:r.id,balance:r.usdt_balance}),console.log("\uD83D\uDE80 API /game/start - Step 1: Validating game type..."),!a||"string"!=typeof a)return console.log("\uD83D\uDE80 API /game/start - VALIDATION ERROR: Invalid game type:",a),t.status(400).json({success:!1,error:"Game type is required"});if(console.log("\uD83D\uDE80 API /game/start - Step 1: Game type validation passed"),console.log("\uD83D\uDE80 API /game/start - Step 2: Validating bet amount..."),"number"!=typeof s||s<=0)return console.log("\uD83D\uDE80 API /game/start - VALIDATION ERROR: Invalid bet amount:",{bet_amount:s,type:typeof s}),t.status(400).json({success:!1,error:"Invalid bet amount"});if(console.log("\uD83D\uDE80 API /game/start - Step 2: Bet amount validation passed"),console.log("\uD83D\uDE80 API /game/start - Step 3: Checking user balance..."),s>r.usdt_balance)return console.log("\uD83D\uDE80 API /game/start - VALIDATION ERROR: Insufficient balance:",{bet_amount:s,user_balance:r.usdt_balance}),t.status(400).json({success:!1,error:"Insufficient balance"});console.log("\uD83D\uDE80 API /game/start - Step 3: Balance check passed"),console.log("\uD83D\uDE80 API /game/start - Step 4: Checking for active games...");let c=o.dW.findActiveByUserId(r.id);if(c)return console.log("\uD83D\uDE80 API /game/start - VALIDATION ERROR: User has active game:",c.id),t.status(400).json({success:!1,error:"You already have an active game. Please finish it first."});if(console.log("\uD83D\uDE80 API /game/start - Step 4: No active games found"),console.log("\uD83D\uDE80 API /game/start - Step 5: Initializing game factory..."),await n.L.initialize(),console.log("\uD83D\uDE80 API /game/start - Step 6: Checking if game type is available..."),!n.L.isGameAvailable(a))return console.log("\uD83D\uDE80 API /game/start - VALIDATION ERROR: Game type not available:",a),t.status(400).json({success:!1,error:`Game type '${a}' is not available`});console.log("\uD83D\uDE80 API /game/start - Step 6: Game type is available"),console.log("\uD83D\uDE80 API /game/start - Step 7: Creating game with factory...");let u={userId:r.id,betAmount:s,clientSeed:i};"mines"===a?u.mineCount=l.mine_count:"dice"===a?(u.targetNumber=l.target_number,u.rollUnder=l.roll_under):"crash"===a&&(u.bet_amount=s,u.auto_cash_out=l.auto_cash_out,u.user_id=r.id,u.client_seed=i),console.log("\uD83D\uDE80 API /game/start - Game creation params:",u);let d=await n.L.createGame(a,u);if(console.log("\uD83D\uDE80 API /game/start - Game creation result:",JSON.stringify(d,null,2)),d.success&&d.game&&(console.log("\uD83D\uDE80 API /game/start - Game data keys:",Object.keys(d.game)),console.log("\uD83D\uDE80 API /game/start - Crash point:",d.game.crash_point),console.log("\uD83D\uDE80 API /game/start - Phase:",d.game.phase),console.log("\uD83D\uDE80 API /game/start - Round ID:",d.game.round_id)),!d.success||!d.game)return console.log("\uD83D\uDE80 API /game/start - GAME CREATION ERROR:",d.error),t.status(400).json({success:!1,error:d.error||"Failed to create game"});console.log("\uD83D\uDE80 API /game/start - Step 7: Game created successfully");let m=r.usdt_balance-s;o.Gy.updateBalance(r.id,"USDT",m);let g=o.dW.create(d.game),p={...g,mine_positions:"mines"===a?[]:g.mine_positions,server_seed:void 0};return t.status(200).json({success:!0,game:p,message:"Game started successfully"})}catch(e){return console.error("\uD83D\uDE80 API /game/start - CATCH BLOCK ERROR:",e),console.error("\uD83D\uDE80 API /game/start - Error stack:",e instanceof Error?e.stack:"No stack trace"),t.status(500).json({success:!1,error:"Internal server error"})}});a()}catch(e){a(e)}})},3139:e=>{e.exports=import("bcryptjs")},3873:e=>{e.exports=require("path")},4729:e=>{e.exports=require("bcryptjs")},5328:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{config:()=>u,default:()=>c,routeModule:()=>d});var s=r(3480),o=r(8667),n=r(6435),i=r(1938),l=e([i]);i=(l.then?(await l)():l)[0];let c=(0,n.M)(i,"default"),u=(0,n.M)(i,"config"),d=new s.PagesAPIRouteModule({definition:{kind:o.A.PAGES_API,page:"/api/game/start",pathname:"/api/game/start",bundlePath:"",filename:""},userland:i});a()}catch(e){a(e)}})},5511:e=>{e.exports=require("crypto")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},7550:e=>{e.exports=require("better-sqlite3")},9021:e=>{e.exports=require("fs")},9103:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.d(t,{DY:()=>g,Eb:()=>I,Lx:()=>p,OB:()=>y,Tf:()=>A,VX:()=>f,b9:()=>d,ru:()=>m});var s=r(829),o=r.n(s),n=r(3139),i=r(3546),l=e([n]);n=(l.then?(await l)():l)[0];let h="your-super-secret-jwt-key-change-this-in-production-make-it-very-long-and-random";async function c(e){return n.default.hash(e,12)}async function u(e,t){return n.default.compare(e,t)}function d(e){let t=function(e){let t=e.headers.authorization;if(t&&t.startsWith("Bearer "))return t.substring(7);let r=e.cookies.token;return r||null}(e);if(!t)return null;let r=function(e){try{return o().verify(e,h)}catch(e){return null}}(t);return r&&r.userId?i.Gy.findById(r.userId):null}function m(e){return async(t,r)=>{try{let a=d(t);if(!a)return r.status(401).json({success:!1,error:"Authentication required"});await e(t,r,a)}catch(e){console.error("Auth middleware error:",e),r.status(500).json({success:!1,error:"Internal server error"})}}}async function g(e,t,r){try{let a=!e||e.length<3||e.length>20?"Username must be between 3 and 20 characters":/^[a-zA-Z0-9_]+$/.test(e)?t&&/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)?!r||r.length<8?"Password must be at least 8 characters long":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(r)?null:"Password must contain at least one uppercase letter, one lowercase letter, and one number":"Please provide a valid email address":"Username can only contain letters, numbers, and underscores";if(a)return{success:!1,error:a};if(i.Gy.findByEmail(t))return{success:!1,error:"Email already registered"};if(i.Gy.findByUsername(e))return{success:!1,error:"Username already taken"};let s=await c(r),{password_hash:o,...n}=i.Gy.create(e,t,s);return{success:!0,user:n}}catch(e){return console.error("Registration error:",e),{success:!1,error:"Failed to register user"}}}async function p(e,t){try{let r=e&&t?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)?null:"Please provide a valid email address":"Email and password are required";if(r)return{success:!1,error:r};let a=i.Gy.findByEmail(e);if(!a||!await u(t,a.password_hash))return{success:!1,error:"Invalid email or password"};let s=function(e){let t={userId:e.id,username:e.username,email:e.email};return o().sign(t,h,{expiresIn:"7d"})}(a),{password_hash:n,...l}=a;return{success:!0,user:l,token:s}}catch(e){return console.error("Login error:",e),{success:!1,error:"Failed to login"}}}function f(e,t){e.setHeader("Set-Cookie",[`token=${t}; HttpOnly; Path=/; Max-Age=604800; SameSite=Strict; Secure`])}function y(e){e.setHeader("Set-Cookie",["token=; HttpOnly; Path=/; Max-Age=0; SameSite=Strict"])}let P=new Map;function I(e,t=5,r=9e5){let a=Date.now(),s=P.get(e);return!s||a>s.resetTime?(P.set(e,{count:1,resetTime:a+r}),!0):!(s.count>=t)&&(s.count++,!0)}function A(e){let t=e.headers["x-forwarded-for"];return(t?Array.isArray(t)?t[0]:t.split(",")[0]:e.socket.remoteAddress)||"unknown"}a()}catch(e){a(e)}})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[405,822],()=>r(5328));module.exports=a})();