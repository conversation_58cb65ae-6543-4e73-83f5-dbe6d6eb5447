"use strict";(()=>{var e={};e.id=506,e.ids=[506],e.modules={829:e=>{e.exports=require("jsonwebtoken")},2160:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>o});var n=t(9103),a=e([n]);async function o(e,r){if("POST"!==e.method)return r.status(405).json({success:!1,error:"Method not allowed"});try{return(0,n.OB)(r),r.status(200).json({success:!0,message:"Logged out successfully"})}catch(e){return console.error("Logout API error:",e),r.status(500).json({success:!1,error:"Internal server error"})}}n=(a.then?(await a)():a)[0],s()}catch(e){s(e)}})},3139:e=>{e.exports=import("bcryptjs")},3873:e=>{e.exports=require("path")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},7550:e=>{e.exports=require("better-sqlite3")},8178:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>l,default:()=>c,routeModule:()=>d});var n=t(3480),a=t(8667),o=t(6435),u=t(2160),i=e([u]);u=(i.then?(await i)():i)[0];let c=(0,o.M)(u,"default"),l=(0,o.M)(u,"config"),d=new n.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/auth/logout",pathname:"/api/auth/logout",bundlePath:"",filename:""},userland:u});s()}catch(e){s(e)}})},9021:e=>{e.exports=require("fs")},9103:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.d(r,{DY:()=>m,Eb:()=>g,Lx:()=>y,OB:()=>p,Tf:()=>w,VX:()=>h,b9:()=>d,ru:()=>f});var n=t(829),a=t.n(n),o=t(3139),u=t(3546),i=e([o]);o=(i.then?(await i)():i)[0];let v="your-super-secret-jwt-key-change-this-in-production-make-it-very-long-and-random";async function c(e){return o.default.hash(e,12)}async function l(e,r){return o.default.compare(e,r)}function d(e){let r=function(e){let r=e.headers.authorization;if(r&&r.startsWith("Bearer "))return r.substring(7);let t=e.cookies.token;return t||null}(e);if(!r)return null;let t=function(e){try{return a().verify(e,v)}catch(e){return null}}(r);return t&&t.userId?u.Gy.findById(t.userId):null}function f(e){return async(r,t)=>{try{let s=d(r);if(!s)return t.status(401).json({success:!1,error:"Authentication required"});await e(r,t,s)}catch(e){console.error("Auth middleware error:",e),t.status(500).json({success:!1,error:"Internal server error"})}}}async function m(e,r,t){try{let s=!e||e.length<3||e.length>20?"Username must be between 3 and 20 characters":/^[a-zA-Z0-9_]+$/.test(e)?r&&/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r)?!t||t.length<8?"Password must be at least 8 characters long":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(t)?null:"Password must contain at least one uppercase letter, one lowercase letter, and one number":"Please provide a valid email address":"Username can only contain letters, numbers, and underscores";if(s)return{success:!1,error:s};if(u.Gy.findByEmail(r))return{success:!1,error:"Email already registered"};if(u.Gy.findByUsername(e))return{success:!1,error:"Username already taken"};let n=await c(t),{password_hash:a,...o}=u.Gy.create(e,r,n);return{success:!0,user:o}}catch(e){return console.error("Registration error:",e),{success:!1,error:"Failed to register user"}}}async function y(e,r){try{let t=e&&r?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)?null:"Please provide a valid email address":"Email and password are required";if(t)return{success:!1,error:t};let s=u.Gy.findByEmail(e);if(!s||!await l(r,s.password_hash))return{success:!1,error:"Invalid email or password"};let n=function(e){let r={userId:e.id,username:e.username,email:e.email};return a().sign(r,v,{expiresIn:"7d"})}(s),{password_hash:o,...i}=s;return{success:!0,user:i,token:n}}catch(e){return console.error("Login error:",e),{success:!1,error:"Failed to login"}}}function h(e,r){e.setHeader("Set-Cookie",[`token=${r}; HttpOnly; Path=/; Max-Age=604800; SameSite=Strict; Secure`])}function p(e){e.setHeader("Set-Cookie",["token=; HttpOnly; Path=/; Max-Age=0; SameSite=Strict"])}let k=new Map;function g(e,r=5,t=9e5){let s=Date.now(),n=k.get(e);return!n||s>n.resetTime?(k.set(e,{count:1,resetTime:s+t}),!0):!(n.count>=r)&&(n.count++,!0)}function w(e){let r=e.headers["x-forwarded-for"];return(r?Array.isArray(r)?r[0]:r.split(",")[0]:e.socket.remoteAddress)||"unknown"}s()}catch(e){s(e)}})}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[405],()=>t(8178));module.exports=s})();