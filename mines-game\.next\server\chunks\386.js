exports.id=386,exports.ids=[386],exports.modules={1912:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{P:()=>u,l:()=>c});var i=r(8732),s=r(2015),n=r(3038),o=r(8201),l=e([n,o]);[n,o]=l.then?(await l)():l;let d=(0,s.createContext)(void 0);function c({children:e}){let t=(0,n.t)(),{user:r}=(0,o.A)(),a="dice"===t.currentGameType?t.currentGame:null,s=t.gameHistory.filter(e=>"dice"===e.game_type),l=async(e,r,a)=>t.startGame("dice",{bet_amount:e,target_number:r,roll_under:a}),c=async()=>{if(!a||"active"!==a.status)throw Error("No active dice game");try{let e=await t.makeMove("dice",{game_id:a.id});if(e.success&&e.gameState){let t=e.gameState;return{result:t.result||0,won:"won"===t.status,multiplier:t.current_multiplier,profit:t.profit}}throw Error(e.error||"Failed to roll dice")}catch(e){throw console.error("Error rolling dice:",e),e}},u=async()=>{await t.loadGameHistory()},m=(e,t)=>t?e-1:100-e,h={gameState:a,gameHistory:s,loading:t.loading,error:t.error,startGame:l,rollDice:c,resetGame:()=>{t.resetGame()},makeMove:c,switchToDice:()=>{t.switchGame("dice")},loadGameHistory:u,canRollDice:()=>a?.status==="active"&&void 0===a.result,getDiceStats:()=>{if(!a)return null;let e=a.roll_under?a.target_number-1:100-a.target_number;return{targetNumber:a.target_number,rollUnder:a.roll_under,winChance:e,multiplier:a.current_multiplier,result:a.result,profit:a.profit,status:a.status}},calculateWinChance:m,calculateMultiplier:(e,t)=>{let r=m(e,t);return Math.max(1.01,Math.round(99/r*1e4)/1e4)}};return(0,i.jsx)(d.Provider,{value:h,children:e})}function u(){let e=(0,s.useContext)(d);if(void 0===e)throw Error("useDiceGame must be used within a DiceGameProvider");return e}a()}catch(e){a(e)}})},2244:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{f:()=>c,x:()=>u});var i=r(8732),s=r(2015),n=r(3038),o=r(8201),l=e([n,o]);[n,o]=l.then?(await l)():l;let d=(0,s.createContext)(void 0);function c({children:e}){let t=(0,n.t)(),{user:r}=(0,o.A)(),[a,l]=(0,s.useState)(1),[c,u]=(0,s.useState)(0),[m,h]=(0,s.useState)("betting"),[g,p]=(0,s.useState)(5e3),[f,y]=(0,s.useState)(null),v=(0,s.useRef)(null);(0,s.useRef)(null);let _=(0,s.useRef)(null),w="crash"===t.currentGameType?t.currentGame:null,G=t.gameHistory.filter(e=>"crash"===e.game_type),x=async(e,r)=>t.startGame("crash",{bet_amount:e,auto_cash_out:r}),S=async()=>{try{let e=await t.cashOut();return{success:e.success,profit:e.profit,multiplier:a}}catch(e){return console.error("Cash out error:",e),{success:!1,profit:0,multiplier:a}}},C=async()=>{await t.loadGameHistory("crash")},M=()=>{let e=Math.random();return e<.5?1.01+.99*Math.random():e<.8?2+3*Math.random():e<.95?5+5*Math.random():10+40*Math.random()},A=()=>{v.current&&clearInterval(v.current);let e=f,t=_.current;w&&w.crash_point?(e=w.crash_point,t=new Date(w.created_at).getTime(),y(e),_.current=t,console.log(`🚀 Resuming active game! Crash point: ${e.toFixed(2)}x`)):e||(e=M(),y(e),_.current=Date.now(),console.log(`🚀 New demo round started! Crash point: ${e.toFixed(2)}x`)),v.current=setInterval(()=>{if("flying"===m){let r=Date.now(),a=r-(t||r);u(a);let i=Math.pow(1.002,a);l(Math.round(100*i)/100),i>=e&&(console.log(`💥 CRASHED at ${i.toFixed(2)}x (target: ${e.toFixed(2)}x)`),l(e),h("crashed"),b(),w&&!w.cashed_out&&console.log("\uD83D\uDCA5 Player lost - game crashed before cash out"),setTimeout(()=>{E()},3e3)),w?.auto_cash_out&&i>=w.auto_cash_out&&!w.cashed_out&&S()}},50)},b=()=>{v.current&&(clearInterval(v.current),v.current=null)},E=()=>{h("waiting"),p(3e3),console.log("⏳ Waiting phase started - 3 seconds until next round");let e=setInterval(()=>{p(t=>{let r=t-100;return r<=0?(clearInterval(e),T(),0):r})},100)},T=()=>{h("betting"),p(5e3),l(1),u(0),y(null),console.log("\uD83D\uDCDD Betting phase started - 5 seconds to place bets!");let e=setInterval(()=>{p(t=>{let r=t-100;return r<=0?(clearInterval(e),w?(console.log("\uD83D\uDE80 Active bet found, starting flying phase!"),I()):(console.log("⏭️ No bets placed, skipping round and starting new betting phase"),setTimeout(()=>{T()},1e3)),0):r})},100)},I=()=>{h("flying"),p(0),console.log("\uD83D\uDE80 Flying phase started!"),A()},N={gameState:w,gameHistory:G,loading:t.loading,error:t.error,startGame:x,cashOut:S,resetGame:()=>{t.resetGame(),l(1),u(0),h("waiting"),p(0)},makeMove:S,switchToCrash:()=>{t.switchGame("crash")},loadGameHistory:C,canCashOut:()=>w?.status==="active"&&"flying"===m&&!w?.cashed_out,canPlaceBet:()=>"betting"===m&&!w,getCurrentMultiplier:()=>a,getTimeElapsed:()=>c,getRoundPhase:()=>m,getTimeUntilNextRound:()=>g,getCrashStats:()=>w?{betAmount:w.bet_amount,currentMultiplier:a,potentialPayout:w.bet_amount*a,phase:m,timeElapsed:c,autoCashOut:w.auto_cash_out,profit:w.profit,status:w.status}:null};return(0,i.jsx)(d.Provider,{value:N,children:e})}function u(){let e=(0,s.useContext)(d);if(void 0===e)throw Error("useCrashGame must be used within a CrashGameProvider");return e}a()}catch(e){a(e)}})},2386:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>g});var i=r(8732);r(2768);var s=r(9788),n=r.n(s),o=r(8201),l=r(3038),c=r(7501),u=r(1912),d=r(2244),m=r(3322),h=e([o,l,c,u,d,m]);function g({Component:e,pageProps:t}){return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(n(),{children:[(0,i.jsx)("title",{children:"BetOctave - Provably Fair Crypto Gambling"}),(0,i.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,i.jsx)("meta",{name:"description",content:"Experience the thrill of provably fair crypto gambling with multiple games, transparent mechanics, and instant payouts."}),(0,i.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,i.jsx)(m.tE,{children:(0,i.jsx)(o.O,{children:(0,i.jsx)(l.T,{children:(0,i.jsx)(c.GE,{children:(0,i.jsx)(u.l,{children:(0,i.jsx)(d.f,{children:(0,i.jsx)(e,{...t})})})})})})})]})}[o,l,c,u,d,m]=h.then?(await h)():h,a()}catch(e){a(e)}})},2768:()=>{},3038:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{T:()=>c,t:()=>u});var i=r(8732),s=r(2015),n=r(8201),o=r(6380),l=e([n]);n=(l.then?(await l)():l)[0];let d=(0,s.createContext)(void 0);function c({children:e}){let[t,r]=(0,s.useState)(null),[a,l]=(0,s.useState)(null),[c,u]=(0,s.useState)([]),[m,h]=(0,s.useState)(!1),[g,p]=(0,s.useState)(null),{user:f}=(0,n.A)(),y=async()=>{try{let e=await fetch("/api/game/active",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&t.game&&(r(t.game),l(t.game.game_type))}}catch(e){console.error("Failed to load active game:",e)}},v=async(e,t)=>{try{h(!0),p(null),a!==e&&l(e);let i={game_type:e,...t};console.log("\uD83C\uDF0D Frontend sending request:",JSON.stringify(i,null,2));let s=await fetch("/api/game/start",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(i)}),n=await s.json();if(n.success&&n.game)return r(n.game),!0;return console.error("Game start failed:",n.error),p(n.error||"Failed to start game"),!1}catch(e){return console.error("Start game error:",e),p("Failed to start game"),!1}finally{h(!1)}},_=async e=>{if(console.log("\uD83C\uDFAF UniversalGameContext: makeMove called with params:",e),console.log("\uD83C\uDFAF UniversalGameContext: currentGame:",t),console.log("\uD83C\uDFAF UniversalGameContext: currentGameType:",a),(!t||!a)&&(console.log("\uD83C\uDFAF UniversalGameContext: No current game, attempting to reload..."),await y(),!t||!a))return console.warn("\uD83C\uDFAF UniversalGameContext: No active game found after reload"),{success:!1,hit:!0,gameOver:!0,error:"No active game"};try{h(!0),p(null),console.log("\uD83C\uDFAF UniversalGameContext: Making API call to /api/game/move");let i=await fetch("/api/game/move",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({game_id:t.id,game_type:a,...e})}),s=await i.json();if(console.log("\uD83C\uDFAF UniversalGameContext: API response:",s),s.success)return s.gameState&&r(s.gameState),s.gameOver&&setTimeout(()=>{S(a),s.gameState?.status!=="active"&&r(null)},2e3),s;return console.error("\uD83C\uDFAF UniversalGameContext: Move failed:",s.error),p(s.error||"Move failed"),{success:!1,hit:!0,gameOver:!0,error:s.error||"Move failed"}}catch(e){return console.error("\uD83C\uDFAF UniversalGameContext: Make move error:",e),p(e instanceof Error?e.message:"Move failed"),{success:!1,hit:!0,gameOver:!0,error:e instanceof Error?e.message:"Move failed"}}finally{h(!1)}},w=async()=>{if(!t)throw Error("No active game");try{h(!0),p(null);let e=await fetch("/api/game/cashout",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({game_id:t.id,game_type:a})}),i=await e.json();if(i.success)return i.gameState&&r(i.gameState),setTimeout(()=>{a&&S(a),r(null)},2e3),{success:!0,profit:i.profit||0};return p(i.error||"Cash out failed"),{success:!1,profit:0}}catch(e){return console.error("Cash out error:",e),p("Cash out failed"),{success:!1,profit:0}}finally{h(!1)}},G=()=>{r(null),l(null),u([]),p(null)},x=async()=>{try{h(!0);let e=await fetch("/api/game/reset-active",{method:"POST",credentials:"include"});if(e.ok&&(await e.json()).success)return G(),await y(),!0;return!1}catch(e){return console.error("Failed to reset active game:",e),!1}finally{h(!1)}},S=async e=>{try{let t=e||a,r=t?`/api/game/history?game_type=${t}`:"/api/game/history",i=await fetch(r,{credentials:"include"});if(i.ok){let e=await i.json();e.success&&e.games&&u(e.games)}}catch(e){console.error("Failed to load game history:",e)}},C=()=>t?.status==="active";return(0,i.jsx)(d.Provider,{value:{currentGame:t,currentGameType:a,gameHistory:c,loading:m,error:g,startGame:v,makeMove:_,cashOut:w,resetGame:G,forceResetActiveGame:x,switchGame:e=>{if(t?.status==="active")return void console.warn("Cannot switch games while a game is active");l(e),r(null),p(null),S(e)},loadGameHistory:S,getGameConfig:e=>o.L.getGameConfig(e),isGameActive:C,canCashOut:()=>!!t&&!!a&&!!o.L.getGameProvider(a)&&("mines"===a?C()&&t.revealed_cells?.length>0:C()&&t.current_multiplier>1)},children:e})}function u(){let e=(0,s.useContext)(d);if(void 0===e)throw Error("useUniversalGame must be used within a UniversalGameProvider");return e}a()}catch(e){a(e)}})},3322:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{Sb:()=>f,US:()=>m,aD:()=>y,eC:()=>p,tE:()=>d,y8:()=>g});var i=r(8732),s=r(2015),n=r(6235),o=r(8938),l=r(9741),c=r(3678),u=e([n,o,c]);[n,o,c]=u.then?(await u)():u;let d=n.Provider,m=s.forwardRef(({className:e,...t},r)=>(0,i.jsx)(n.Viewport,{ref:r,className:(0,c.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));m.displayName=n.Viewport.displayName;let h=(0,o.cva)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive border-destructive bg-destructive text-destructive-foreground",success:"border-green-500 bg-green-500/10 text-green-400",warning:"border-yellow-500 bg-yellow-500/10 text-yellow-400",info:"border-blue-500 bg-blue-500/10 text-blue-400"}},defaultVariants:{variant:"default"}}),g=s.forwardRef(({className:e,variant:t,...r},a)=>(0,i.jsx)(n.Root,{ref:a,className:(0,c.cn)(h({variant:t}),e),...r}));g.displayName=n.Root.displayName,s.forwardRef(({className:e,...t},r)=>(0,i.jsx)(n.Action,{ref:r,className:(0,c.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=n.Action.displayName;let p=s.forwardRef(({className:e,...t},r)=>(0,i.jsx)(n.Close,{ref:r,className:(0,c.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:(0,i.jsx)(l.A,{className:"h-4 w-4"})}));p.displayName=n.Close.displayName;let f=s.forwardRef(({className:e,...t},r)=>(0,i.jsx)(n.Title,{ref:r,className:(0,c.cn)("text-sm font-semibold",e),...t}));f.displayName=n.Title.displayName;let y=s.forwardRef(({className:e,...t},r)=>(0,i.jsx)(n.Description,{ref:r,className:(0,c.cn)("text-sm opacity-90",e),...t}));y.displayName=n.Description.displayName,a()}catch(e){a(e)}})},3678:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{Sn:()=>d,Yq:()=>c,cn:()=>o,si:()=>u,uW:()=>m,vv:()=>l});var i=r(802),s=r(5979),n=e([i,s]);function o(...e){return(0,s.twMerge)((0,i.clsx)(e))}function l(e,t="USDT",r=!1){var a;return null==e?0..toFixed("USDT"===t?2:8):r&&Math.abs(e)>=1e3?(a=e)>=1e9?(a/1e9).toFixed(1)+"B":a>=1e6?(a/1e6).toFixed(1)+"M":a>=1e3?(a/1e3).toFixed(1)+"K":a.toString():e.toFixed("USDT"===t?2:8)}function c(e){return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})}[i,s]=n.then?(await n)():n;let u={GRID_SIZE:25,MIN_MINES:1,MAX_MINES:24,MIN_BET:.01,MAX_BET:1e3,HOUSE_EDGE:.04,BASE_MULTIPLIER:1},d={AUTH:{LOGIN:"/api/auth/login",SIGNUP:"/api/auth/signup",ME:"/api/auth/me",LOGOUT:"/api/auth/logout"},GAME:{START:"/api/game/start",MOVE:"/api/game/move",CASHOUT:"/api/game/cashout",HISTORY:"/api/game/history",ACTIVE:"/api/game/active",CONFIG:"/api/game/config",LIST:"/api/game/list",STATS:"/api/game/stats"},MINES:{START:"/api/game/start",PICK:"/api/game/move",CASHOUT:"/api/game/cashout",HISTORY:"/api/game/history"},WALLET:{DEPOSIT:"/api/wallet/deposit",WITHDRAW:"/api/wallet/withdraw",BALANCE:"/api/wallet/balance"}},m={SESSION_KEY:"betoctave_session_stats",getSession:()=>null,resetSession:()=>{},getSessionStartTime:()=>{let e=m.getSession();return e?.startTime?e.startTime:(m.resetSession(),m.getSession()?.startTime||new Date().toISOString())},hasSession:()=>null!==m.getSession(),initializeSession:()=>{m.hasSession()?console.log("\uD83D\uDCCA Session already exists:",m.getSession()):(console.log("\uD83D\uDCCA Initializing new session"),m.resetSession())}};a()}catch(e){a(e)}})},6380:(e,t,r)=>{"use strict";r.d(t,{L:()=>w});class a{registerGame(e,t){this.games.has(e.id)&&console.warn(`Game ${e.id} is already registered. Overwriting...`),this.games.set(e.id,e),this.providers.set(e.id,t),console.log(`✅ Registered game: ${e.name} (${e.id})`)}getGameConfig(e){return this.games.get(e)}getGameProvider(e){return this.providers.get(e)}getAllGames(){return Array.from(this.games.values())}getGamesByCategory(e){return this.getAllGames().filter(t=>t.category===e)}getActiveGames(){return this.getAllGames().filter(e=>e.isActive)}getFeaturedGames(){return this.getAllGames().filter(e=>e.isFeatured&&e.isActive)}getNewGames(){return this.getAllGames().filter(e=>e.isNew&&e.isActive)}searchGames(e){let t=e.toLowerCase();return this.getAllGames().filter(e=>e.name.toLowerCase().includes(t)||e.description.toLowerCase().includes(t)||e.features.some(e=>e.toLowerCase().includes(t)))}isGameRegistered(e){return this.games.has(e)}getRegistryStats(){let e=this.getAllGames(),t=this.getActiveGames(),r=e.reduce((e,t)=>(e[t.category]=(e[t.category]||0)+1,e),{});return{totalGames:e.length,activeGames:t.length,gamesByCategory:r}}validateGameConfig(e){for(let t of["id","name","description","category","minBet","maxBet"])if(!(t in e))return console.error(`Game config missing required field: ${t}`),!1;return e.minBet>=e.maxBet?(console.error("minBet must be less than maxBet"),!1):!(e.houseEdge<0)&&!(e.houseEdge>1)||(console.error("houseEdge must be between 0 and 1"),!1)}unregisterGame(e){let t=this.games.has(e);return this.games.delete(e),this.providers.delete(e),t&&console.log(`🗑️ Unregistered game: ${e}`),t}clear(){this.games.clear(),this.providers.clear(),console.log("\uD83E\uDDF9 Cleared all registered games")}constructor(){this.games=new Map,this.providers=new Map}}let i=new a;var s=r(5511),n=r.n(s);class o{validateBaseParams(e){return"number"==typeof e&&!(e<=0)&&!(e<this.config.minBet)&&!(e>this.config.maxBet)}generateBaseGameData(e,t,r){return{user_id:e,game_type:this.gameType,bet_amount:t,current_multiplier:1,status:"active",server_seed:n().randomBytes(32).toString("hex"),client_seed:r||n().randomBytes(16).toString("hex"),profit:0}}calculateProfit(e,t){return e*t-e}applyHouseEdge(e){return e*(1-this.config.houseEdge)}validateMultiplier(e){return e<=this.config.maxMultiplier}generateGameHash(e,t,a=0){let i=r(5511),s=`${e}:${t}:${a}`;return i.createHash("sha256").update(s).digest("hex")}generateRandomFromSeeds(e,t,r=0){return parseInt(this.generateGameHash(e,t,r).substring(0,8),16)/0xffffffff}generateRandomInt(e,t,r,a,i=0){return Math.floor(this.generateRandomFromSeeds(r,a,i)*(t-e+1))+e}generateRandomArray(e,t,r,a,i,s=0){let n=[],o=r-t+1;for(let r=0;r<e;r++){let e=Math.floor(this.generateRandomFromSeeds(a,i,s+r)*o)+t;n.push(e)}return n}shuffleArray(e,t,r,a=0){let i=[...e];for(let e=i.length-1;e>0;e--){let s=Math.floor(this.generateRandomFromSeeds(t,r,a+e)*(e+1));[i[e],i[s]]=[i[s],i[e]]}return i}validateGameOwnership(e,t){return e.user_id===t}isGameActive(e){return"active"===e.status}logGameAction(e,t,r){}}var l=function(e){return e.START_GAME="START_GAME",e.MAKE_MOVE="MAKE_MOVE",e.CASH_OUT="CASH_OUT",e.END_GAME="END_GAME",e.CANCEL_GAME="CANCEL_GAME",e}({});class c extends Error{constructor(e,t,r){super(e),this.code=t,this.gameType=r,this.name="GameError"}}class u extends c{constructor(e,t="Invalid game parameters"){super(t,"INVALID_PARAMS",e)}}class d extends c{constructor(e,t="Game is not active"){super(t,"GAME_NOT_ACTIVE",e)}}class m extends c{constructor(e,t){super(e,"INVALID_ACTION",t)}}class h extends o{validateGameParams(e){let{betAmount:t,mineCount:r}=e;return!!this.validateBaseParams(t)&&"number"==typeof r&&!(r<this.MIN_MINES)&&!(r>this.MAX_MINES)}calculateMultiplier(e,t){let r=t?.revealedCells??e.revealed_cells.length;if(0===r)return 1;let a=this.GRID_SIZE-e.mine_count;if(a-r<=0)return 1;let i=1;for(let e=0;e<r;e++)i*=1/((a-e)/(this.GRID_SIZE-e));return Math.min(i=this.applyHouseEdge(i),this.config.maxMultiplier)}generateGameData(e){let{userId:t,betAmount:r,mineCount:a,clientSeed:i}=e;if(!this.validateGameParams({betAmount:r,mineCount:a}))throw new u(this.gameType);let s=this.generateBaseGameData(t,r,i),n=this.generateMinePositions(s.server_seed,s.client_seed,a);return{...s,game_type:"mines",grid_size:this.GRID_SIZE,mine_count:a,revealed_cells:[],mine_positions:n}}async processGameAction(e,t){if(this.logGameAction(e,t),!this.isGameActive(e))throw new d(this.gameType);switch(t.type){case l.MAKE_MOVE:return this.processRevealCell(e,t.payload.cellIndex);case l.CASH_OUT:return this.processCashOut(e);default:throw Error(`Unknown action type: ${t.type}`)}}processRevealCell(e,t){if(t<0||t>=this.GRID_SIZE)throw Error("Invalid cell index");if(e.revealed_cells.includes(t))throw Error("Cell already revealed");let r=[...e.revealed_cells,t];if(e.mine_positions.includes(t))return{...e,revealed_cells:r,status:"lost",profit:-e.bet_amount,current_multiplier:0};{let t=this.calculateMultiplier(e,{revealedCells:r.length}),a=this.calculateProfit(e.bet_amount,t),i=this.GRID_SIZE-e.mine_count,s=r.length===i;return{...e,revealed_cells:r,current_multiplier:t,profit:a,status:s?"won":"active"}}}processCashOut(e){if(0===e.revealed_cells.length)throw Error("Cannot cash out without revealing any cells");let t=this.calculateProfit(e.bet_amount,e.current_multiplier);return{...e,status:"cashed_out",profit:t}}generateMinePositions(e,t,r){let a=Array.from({length:this.GRID_SIZE},(e,t)=>t);return this.shuffleArray(a,e,t).slice(0,r).sort((e,t)=>e-t)}getSafeCellsRemaining(e){return this.GRID_SIZE-e.mine_count-e.revealed_cells.length}getNextMultiplier(e){if(!this.isGameActive(e))return e.current_multiplier;let t=e.revealed_cells.length+1;return this.calculateMultiplier(e,{revealedCells:t})}canCashOut(e){return this.isGameActive(e)&&e.revealed_cells.length>0}constructor(...e){super(...e),this.gameType="mines",this.config={id:"mines",name:"Mines",description:"Click tiles to reveal gems while avoiding hidden mines. Cash out anytime to secure your winnings!",icon:"\uD83D\uDC8E",category:"originals",minBet:.01,maxBet:1e3,houseEdge:.04,maxMultiplier:1e3,features:["Provably Fair","Instant Play","Auto Cashout","Custom Risk"],isActive:!0,isFeatured:!0,isNew:!1},this.GRID_SIZE=25,this.MIN_MINES=1,this.MAX_MINES=24}}class g extends o{validateGameParams(e){let{betAmount:t,targetNumber:r,rollUnder:a}=e;return!!this.validateBaseParams(t)&&"number"==typeof r&&!(r<this.MIN_TARGET)&&!(r>this.MAX_TARGET)&&"boolean"==typeof a}calculateMultiplier(e,t){let{target_number:r,roll_under:a}=e,i=this.calculateWinChance(r,a);return Math.max(1.01,Math.round(1e4*((100-100*this.config.houseEdge)/i))/1e4)}calculateWinChance(e,t){return t?e-1:100-e}generateGameData(e){let{userId:t,betAmount:r,targetNumber:a,rollUnder:i,clientSeed:s}=e;if(!this.validateGameParams({betAmount:r,targetNumber:a,rollUnder:i}))throw new u(this.gameType);let n=this.generateBaseGameData(t,r,s),o=this.calculateMultiplier({...n,game_type:"dice",target_number:a,roll_under:i});return{...n,game_type:"dice",target_number:a,roll_under:i,current_multiplier:o,result:void 0}}async processGameAction(e,t){if(t.type===l.MAKE_MOVE)return this.rollDice(e);throw new m(`Unsupported action type: ${t.type}`,this.gameType)}rollDice(e){if("active"!==e.status)throw new m("Game is not active",this.gameType);if(void 0!==e.result)throw new m("Dice already rolled",this.gameType);let t=this.generateRandomInt(this.DICE_MIN,this.DICE_MAX,e.server_seed,e.client_seed,0),r=this.checkWin(t,e.target_number,e.roll_under),a=r?e.bet_amount*(e.current_multiplier-1):-e.bet_amount;return{...e,result:t,status:r?"won":"lost",profit:a,updated_at:new Date().toISOString()}}checkWin(e,t,r){return r?e<t:e>t}getGameStats(e){let t=this.calculateWinChance(e.target_number,e.roll_under);return{targetNumber:e.target_number,rollUnder:e.roll_under,winChance:t,multiplier:e.current_multiplier,result:e.result,profit:e.profit,status:e.status}}constructor(...e){super(...e),this.gameType="dice",this.config={id:"dice",name:"Dice",description:"Roll the dice and predict if the result will be over or under your chosen number. Simple yet thrilling!",icon:"\uD83C\uDFB2",category:"originals",minBet:.01,maxBet:1e3,houseEdge:.01,maxMultiplier:9900,features:["Provably Fair","Instant Play","Custom Multiplier"],isActive:!0,isFeatured:!0,isNew:!0},this.MIN_TARGET=2,this.MAX_TARGET=98,this.DICE_MIN=1,this.DICE_MAX=100}}class p extends o{validateGameParams(e){let{bet_amount:t,auto_cash_out:r}=e;return!!this.validateBaseParams(t)&&(void 0===r||"number"==typeof r&&!(r<1.01)&&!(r>this.config.maxMultiplier))&&!0}calculateMultiplier(e,t){if("flying"!==e.phase)return 1;let r=Math.pow(1.002,(e.time_elapsed||0)/100);return e.crash_point&&r>=e.crash_point?e.crash_point:Math.round(100*r)/100}generateGameData(e){console.log("\uD83C\uDFAE CrashGameProvider - generateGameData called with params:",e);let{bet_amount:t,auto_cash_out:r,user_id:a,client_seed:i}=e,s=this.generateBaseGameData(a,t,i);console.log("\uD83C\uDFAE CrashGameProvider - baseData generated:",s);let n=this.generateCrashPoint(s.server_seed,s.client_seed);console.log("\uD83C\uDFAE CrashGameProvider - crashPoint generated:",n);let o={...s,game_type:"crash",crash_point:n,auto_cash_out:r,phase:"betting",time_elapsed:0,cashed_out:!1,round_id:this.generateRoundId()};return console.log("\uD83C\uDFAE CrashGameProvider - final result:",o),console.log("\uD83C\uDFAE CrashGameProvider - result keys:",Object.keys(o)),o}async processGameAction(e,t){switch(this.logGameAction(e,t),t.type){case l.START_GAME:return this.handleStartGame(e,t.payload);case l.CASH_OUT:return this.handleCashOut(e,t.payload);case"UPDATE_MULTIPLIER":return this.handleUpdateMultiplier(e,t.payload);case"CRASH":return this.handleCrash(e);case"START_ROUND":return this.handleStartRound(e);case"END_ROUND":return this.handleEndRound(e);default:throw Error(`Unknown action type: ${t.type}`)}}handleStartGame(e,t){return{...e,phase:"betting",status:"active",time_elapsed:0,current_multiplier:1,cashed_out:!1}}handleCashOut(e,t){if("flying"!==e.phase||e.cashed_out)throw Error("Cannot cash out at this time");let r=this.calculateMultiplier(e),a=this.calculateProfit(e.bet_amount,r);return{...e,cashed_out:!0,cash_out_at:r,current_multiplier:r,profit:a,status:"cashed_out"}}handleUpdateMultiplier(e,t){if("flying"!==e.phase)return e;let r=this.calculateMultiplier({...e,time_elapsed:t.timeElapsed});return e.crash_point&&r>=e.crash_point?this.handleCrash({...e,time_elapsed:t.timeElapsed,current_multiplier:e.crash_point}):e.auto_cash_out&&r>=e.auto_cash_out&&!e.cashed_out?this.handleCashOut({...e,time_elapsed:t.timeElapsed,current_multiplier:r},{}):{...e,time_elapsed:t.timeElapsed,current_multiplier:r}}handleCrash(e){let t=e.cashed_out?e.profit:-e.bet_amount;return{...e,phase:"crashed",status:e.cashed_out?"cashed_out":"lost",profit:t,current_multiplier:e.crash_point||e.current_multiplier}}handleStartRound(e){return{...e,phase:"flying",time_elapsed:0,current_multiplier:1}}handleEndRound(e){return{...e,phase:"waiting"}}generateCrashPoint(e,t){let r=this.generateRandomFromSeeds(e,t,0);return Math.min(Math.max(1.01,(1-this.config.houseEdge)/r),this.config.maxMultiplier)}generateRoundId(){return`crash_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}constructor(...e){super(...e),this.gameType="crash",this.config={id:"crash",name:"Crash",description:"Watch the multiplier rise and cash out before it crashes! The longer you wait, the higher the multiplier, but if you wait too long, you lose everything.",icon:"\uD83D\uDE80",category:"originals",minBet:.01,maxBet:1e3,houseEdge:.01,maxMultiplier:1e6,features:["Provably Fair","Real-time","Auto Cash Out","Live Multiplier"],isActive:!0,isNew:!0,isFeatured:!0}}}class f extends o{constructor(e){super(),this.gameType=e.id,this.config=e}validateGameParams(e){return e&&"number"==typeof e.betAmount&&e.betAmount>0}calculateMultiplier(e,t){return e.current_multiplier||1}generateGameData(e){return{...this.generateBaseGameData(e.userId,e.betAmount,e.clientSeed),game_type:this.gameType}}async processGameAction(e,t){return e}}let y=[{id:"plinko",name:"Plinko",description:"Drop balls down the plinko board and watch them bounce into different multiplier slots. Pure luck and excitement!",icon:"\uD83C\uDFC0",category:"originals",minBet:.01,maxBet:1e3,houseEdge:.01,maxMultiplier:1e3,features:["Provably Fair","Multiple Risk Levels","Animated Gameplay"],isActive:!0,isFeatured:!0,isNew:!1},{id:"limbo",name:"Limbo",description:"Choose your target multiplier and see if you can reach it. The higher the target, the lower the chance!",icon:"\uD83C\uDFAF",category:"originals",minBet:.01,maxBet:1e3,houseEdge:.01,maxMultiplier:1e6,features:["Provably Fair","Instant Results","Unlimited Multiplier"],isActive:!0,isFeatured:!1,isNew:!1},{id:"wheel",name:"Wheel",description:"Spin the wheel of fortune and win big! Choose your risk level and watch the wheel decide your fate.",icon:"\uD83C\uDFA1",category:"originals",minBet:.01,maxBet:1e3,houseEdge:.01,maxMultiplier:50,features:["Provably Fair","Multiple Risk Levels","Visual Spinning"],isActive:!0,isFeatured:!1,isNew:!0},{id:"blackjack",name:"Blackjack",description:"Classic card game where you try to get as close to 21 as possible without going over. Beat the dealer!",icon:"\uD83C\uDCCF",category:"table",minBet:.01,maxBet:1e3,houseEdge:.005,maxMultiplier:3,features:["Classic Rules","Strategy Based","Low House Edge"],isActive:!0,isFeatured:!1,isNew:!1},{id:"roulette",name:"Roulette",description:"Place your bets on the roulette table and watch the ball spin. Red or black? Odd or even? Your choice!",icon:"\uD83C\uDFB0",category:"table",minBet:.01,maxBet:1e3,houseEdge:.027,maxMultiplier:36,features:["European Rules","Multiple Bet Types","Live Animation"],isActive:!0,isFeatured:!1,isNew:!1}],v=()=>y.map(e=>new f(e));class _{constructor(){this.initialized=!1}static getInstance(){return _.instance||(_.instance=new _),_.instance}async initialize(){if(this.initialized)return void console.log("\uD83C\uDFAE Game factory already initialized");console.log("\uD83C\uDFAE Initializing game factory...");try{await this.registerAllGames(),this.initialized=!0,console.log("✅ Game factory initialized successfully");let e=i.getRegistryStats();console.log(`📊 Registered ${e.totalGames} games (${e.activeGames} active)`),console.log("\uD83D\uDCCB Games by category:",e.gamesByCategory)}catch(e){throw console.error("❌ Failed to initialize game factory:",e),e}}async registerAllGames(){for(let e of[new h,new g,new p,...v()])try{i.registerGame(e.config,e)}catch(t){console.error(`Failed to register ${e.gameType} game:`,t)}}async createGame(e,t){try{this.initialized||await this.initialize();let r=i.getGameProvider(e);if(console.log(`🎮 GameFactory - Retrieved provider for ${e}:`,r?.constructor.name),!r)return{success:!1,error:`Game type '${e}' is not registered`};if(console.log(`🎮 GameFactory - Validating params for ${e}:`,t),!r.validateGameParams(t))return{success:!1,error:"Invalid game parameters"};console.log(`🎮 GameFactory - Calling generateGameData for ${e}`);let a=r.generateGameData(t);return console.log(`🎮 GameFactory - Generated game data:`,a),{success:!0,game:a}}catch(t){return console.error(`Error creating ${e} game:`,t),{success:!1,error:t instanceof Error?t.message:"Unknown error"}}}async processGameAction(e,t,r,a){try{this.initialized||await this.initialize();let s=i.getGameProvider(e);if(!s)return{success:!1,error:`Game type '${e}' is not registered`};let n=await s.processGameAction(t,{type:r,payload:a});return{success:!0,gameState:n}}catch(t){return console.error(`Error processing ${e} action:`,t),{success:!1,error:t instanceof Error?t.message:"Unknown error"}}}calculateMultiplier(e,t,r){if(!this.initialized)return console.warn("Game factory not initialized, returning default multiplier"),1;let a=i.getGameProvider(e);return a?a.calculateMultiplier(t,r):(console.warn(`Game type '${e}' not found, returning default multiplier`),1)}getGameConfig(e){return i.getGameConfig(e)}getAllGames(){return i.getAllGames()}getGamesByCategory(e){return i.getGamesByCategory(e)}searchGames(e){return i.searchGames(e)}isGameAvailable(e){let t=i.getGameConfig(e);return t?.isActive??!1}getGameProvider(e){return i.getGameProvider(e)}reset(){i.clear(),this.initialized=!1,console.log("\uD83D\uDD04 Game factory reset")}isInitialized(){return this.initialized}}let w=_.getInstance();w.initialize().catch(console.error)},7501:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{GE:()=>c,SX:()=>u});var i=r(8732),s=r(2015),n=r(3038),o=r(8201),l=e([n,o]);[n,o]=l.then?(await l)():l;let d=(0,s.createContext)(void 0);function c({children:e}){let t=(0,n.t)(),{user:r}=(0,o.A)(),a="mines"===t.currentGameType?t.currentGame:null,s=t.gameHistory.filter(e=>"mines"===e.game_type),l=async(e,r)=>t.startGame("mines",{bet_amount:e,mine_count:r}),c=async e=>{try{console.log("\uD83C\uDFAF MinesGameContext: Attempting to reveal cell",e),console.log("\uD83C\uDFAF MinesGameContext: Current minesGame state:",a),console.log("\uD83C\uDFAF MinesGameContext: Universal game active:",t.isGameActive());let r=await t.makeMove({cellIndex:e});return console.log("\uD83C\uDFAF MinesGameContext: Move result:",r),{hit:r.hit||!1,multiplier:r.multiplier||a?.current_multiplier||1,gameOver:r.gameOver||!1,profit:r.profit}}catch(e){throw console.error("\uD83C\uDFAF MinesGameContext: Reveal cell error:",e),e}},u=async()=>t.cashOut(),m=async()=>{await t.loadGameHistory("mines")},h=()=>a?(a.grid_size||25)-a.mine_count-a.revealed_cells.length:0,g={gameState:a,gameHistory:s,loading:t.loading,error:t.error,startGame:l,revealCell:c,cashOut:u,resetGame:()=>{t.resetGame()},makeMove:c,switchToMines:()=>{t.switchGame("mines")},loadGameHistory:m,canCashOut:()=>a?.status==="active"&&(a?.revealed_cells?.length||0)>0,getSafeCellsRemaining:h,getNextMultiplier:()=>a&&"active"===a.status?a.current_multiplier:a?.current_multiplier||1,isCellRevealed:e=>a?.revealed_cells?.includes(e)||!1,isCellMine:e=>!!a&&"active"!==a.status&&(a.mine_positions?.includes(e)||!1),getGameStats:()=>a?{gridSize:a.grid_size||25,mineCount:a.mine_count,revealedCells:a.revealed_cells.length,safeCellsRemaining:h(),currentMultiplier:a.current_multiplier,profit:a.profit,status:a.status}:null};return(0,i.jsx)(d.Provider,{value:g,children:e})}function u(){let e=(0,s.useContext)(d);if(void 0===e)throw Error("useMinesGame must be used within a MinesGameProvider");return e}a()}catch(e){a(e)}})},8201:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{A:()=>c,O:()=>l});var i=r(8732),s=r(2015),n=r(3678),o=e([n]);n=(o.then?(await o)():o)[0];let u=(0,s.createContext)(void 0);function l({children:e}){let[t,r]=(0,s.useState)(null),[a,o]=(0,s.useState)(!0),l=async(e,t)=>{try{o(!0);let a=await fetch(n.Sn.AUTH.LOGIN,{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:t})}),i=await a.json();if(i.success&&i.user)return r(i.user),!0;return console.error("Login failed:",i.error),!1}catch(e){return console.error("Login error:",e),!1}finally{o(!1)}},c=async(e,t,a)=>{try{o(!0);let i=await fetch(n.Sn.AUTH.SIGNUP,{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({username:e,email:t,password:a})}),s=await i.json();if(s.success&&s.user)return r(s.user),!0;return console.error("Signup failed:",s.error),!1}catch(e){return console.error("Signup error:",e),!1}finally{o(!1)}},d=async()=>{try{await fetch(n.Sn.AUTH.LOGOUT,{method:"POST",credentials:"include"})}catch(e){console.error("Logout error:",e)}finally{r(null)}};return(0,i.jsx)(u.Provider,{value:{user:t,login:l,signup:c,logout:d,loading:a},children:e})}function c(){let e=(0,s.useContext)(u);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}a()}catch(e){a(e)}})}};