"use strict";(()=>{var e={};e.id=919,e.ids=[919],e.modules={829:e=>{e.exports=require("jsonwebtoken")},3139:e=>{e.exports=import("bcryptjs")},3873:e=>{e.exports=require("path")},4256:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>l,default:()=>c,routeModule:()=>d});var n=t(3480),a=t(8667),i=t(6435),o=t(8613),u=e([o]);o=(u.then?(await u)():u)[0];let c=(0,i.M)(o,"default"),l=(0,i.M)(o,"config"),d=new n.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/game/move",pathname:"/api/game/move",bundlePath:"",filename:""},userland:o});s()}catch(e){s(e)}})},4729:e=>{e.exports=require("bcryptjs")},5511:e=>{e.exports=require("crypto")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},7550:e=>{e.exports=require("better-sqlite3")},8613:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>c});var n=t(9103),a=t(3546),i=t(8822),o=t(6851),u=e([n]);n=(u.then?(await u)():u)[0];let c=(0,n.ru)(async(e,r,t)=>{if((0,a.s4)(),"POST"!==e.method)return r.status(405).json({success:!1,error:"Method not allowed"});try{let{game_id:s,game_type:n,...u}=e.body;if(!s||"number"!=typeof s)return r.status(400).json({success:!1,error:"Game ID is required"});if(!n||"string"!=typeof n)return r.status(400).json({success:!1,error:"Game type is required"});let c=a.dW.findById(s);if(!c)return r.status(404).json({success:!1,error:"Game not found"});if(c.user_id!==t.id)return r.status(403).json({success:!1,error:"Unauthorized"});if("active"!==c.status)return r.status(400).json({success:!1,error:"Game is not active"});if(c.game_type!==n)return r.status(400).json({success:!1,error:"Game type mismatch"});await i.L.initialize();let l=await i.L.processGameAction(n,c,o.r1.MAKE_MOVE,u);if(!l.success||!l.gameState)return r.status(400).json({success:!1,error:l.error||"Move failed"});let d=l.gameState;if(a.dW.update(s,d),("won"===d.status||"cashed_out"===d.status)&&d.profit>0){let e=t.usdt_balance+d.bet_amount+d.profit;a.Gy.updateBalance(t.id,"USDT",e)}let m={success:!0,gameState:d,gameOver:"active"!==d.status};return"mines"===n?(m.hit=void 0!==u.cellIndex&&d.mine_positions?.includes(u.cellIndex),m.multiplier=d.current_multiplier,m.profit=d.profit,"active"!==d.status&&(m.minePositions=d.mine_positions)):"dice"===n&&(m.result=d.result,m.won="won"===d.status,m.multiplier=d.current_multiplier),r.status(200).json(m)}catch(e){return console.error("Move API error:",e),r.status(500).json({success:!1,error:"Internal server error"})}});s()}catch(e){s(e)}})},9021:e=>{e.exports=require("fs")},9103:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.d(r,{DY:()=>f,Eb:()=>g,Lx:()=>p,OB:()=>h,Tf:()=>w,VX:()=>y,b9:()=>d,ru:()=>m});var n=t(829),a=t.n(n),i=t(3139),o=t(3546),u=e([i]);i=(u.then?(await u)():u)[0];let v="your-super-secret-jwt-key-change-this-in-production-make-it-very-long-and-random";async function c(e){return i.default.hash(e,12)}async function l(e,r){return i.default.compare(e,r)}function d(e){let r=function(e){let r=e.headers.authorization;if(r&&r.startsWith("Bearer "))return r.substring(7);let t=e.cookies.token;return t||null}(e);if(!r)return null;let t=function(e){try{return a().verify(e,v)}catch(e){return null}}(r);return t&&t.userId?o.Gy.findById(t.userId):null}function m(e){return async(r,t)=>{try{let s=d(r);if(!s)return t.status(401).json({success:!1,error:"Authentication required"});await e(r,t,s)}catch(e){console.error("Auth middleware error:",e),t.status(500).json({success:!1,error:"Internal server error"})}}}async function f(e,r,t){try{let s=!e||e.length<3||e.length>20?"Username must be between 3 and 20 characters":/^[a-zA-Z0-9_]+$/.test(e)?r&&/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r)?!t||t.length<8?"Password must be at least 8 characters long":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(t)?null:"Password must contain at least one uppercase letter, one lowercase letter, and one number":"Please provide a valid email address":"Username can only contain letters, numbers, and underscores";if(s)return{success:!1,error:s};if(o.Gy.findByEmail(r))return{success:!1,error:"Email already registered"};if(o.Gy.findByUsername(e))return{success:!1,error:"Username already taken"};let n=await c(t),{password_hash:a,...i}=o.Gy.create(e,r,n);return{success:!0,user:i}}catch(e){return console.error("Registration error:",e),{success:!1,error:"Failed to register user"}}}async function p(e,r){try{let t=e&&r?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)?null:"Please provide a valid email address":"Email and password are required";if(t)return{success:!1,error:t};let s=o.Gy.findByEmail(e);if(!s||!await l(r,s.password_hash))return{success:!1,error:"Invalid email or password"};let n=function(e){let r={userId:e.id,username:e.username,email:e.email};return a().sign(r,v,{expiresIn:"7d"})}(s),{password_hash:i,...u}=s;return{success:!0,user:u,token:n}}catch(e){return console.error("Login error:",e),{success:!1,error:"Failed to login"}}}function y(e,r){e.setHeader("Set-Cookie",[`token=${r}; HttpOnly; Path=/; Max-Age=604800; SameSite=Strict; Secure`])}function h(e){e.setHeader("Set-Cookie",["token=; HttpOnly; Path=/; Max-Age=0; SameSite=Strict"])}let b=new Map;function g(e,r=5,t=9e5){let s=Date.now(),n=b.get(e);return!n||s>n.resetTime?(b.set(e,{count:1,resetTime:s+t}),!0):!(n.count>=r)&&(n.count++,!0)}function w(e){let r=e.headers["x-forwarded-for"];return(r?Array.isArray(r)?r[0]:r.split(",")[0]:e.socket.remoteAddress)||"unknown"}s()}catch(e){s(e)}})}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[405,822],()=>t(4256));module.exports=s})();